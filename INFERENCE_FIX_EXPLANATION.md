# LLaVA ECG 推理代码修复说明

## 问题分析

原始推理代码存在以下关键问题，导致模型无法正确读取ECG信息：

### 1. 对话模板处理不一致

**训练时的处理方式：**
- 使用 `preprocess_multimodal` 函数处理对话数据
- 根据 `model_args.version = "v1"` 设置，使用 `vicuna_v1` 对话模板
- ECG token 被正确地处理和放置

**原始推理代码的问题：**
- 直接使用 `conv_templates['v1']` 而不是 `conv_templates['vicuna_v1']`
- 手动构造 `f"<ecg>\n{question}"` 格式，与训练时不一致

### 2. Tokenization 方法不匹配

**训练时的处理方式：**
```python
# 训练时使用专门的 ECG token 处理函数
input_ids = torch.stack([
    tokenizer_ecg_token(prompt, tokenizer, return_tensors='pt') 
    for prompt in conversations
], dim=0)
```

**原始推理代码的问题：**
```python
# 直接使用普通 tokenizer，无法正确处理 ECG token
input_ids = self.tokenizer(prompt, return_tensors='pt').input_ids.to(self.device)
```

### 3. ECG Token 格式处理不正确

**训练时的格式：**
- 在 `preprocess_multimodal` 中，ECG token 被规范化处理
- 确保 `<ecg>` token 在正确位置，并与文本内容正确分离

**原始推理代码的问题：**
- 没有遵循训练时的 ECG token 处理逻辑
- 可能导致模型无法识别 ECG 输入

## 修复方案

### 1. 导入必要的模块

```python
from llava.constants import DEFAULT_ECG_TOKEN
from llava.mm_utils import tokenizer_ecg_token
```

### 2. 修正对话模板

```python
# 修复前
conv = conv_templates['v1'].copy()

# 修复后  
conv = conv_templates['vicuna_v1'].copy()
```

### 3. 修正 ECG token 格式

```python
# 修复前
conv.append_message(conv.roles[0], f"<ecg>\n{question}")

# 修复后
formatted_question = f"{DEFAULT_ECG_TOKEN}\n{question}"
conv.append_message(conv.roles[0], formatted_question)
```

### 4. 使用正确的 tokenization 方法

```python
# 修复前
input_ids = self.tokenizer(prompt, return_tensors='pt').input_ids.to(self.device)

# 修复后
input_ids = tokenizer_ecg_token(prompt, self.tokenizer, return_tensors='pt').to(self.device)
```

### 5. 修正批处理中的问题提取

```python
# 修复前
question = item['conversations'][0]['value'].replace('<ecg>\n', '')

# 修复后
question_value = item['conversations'][0]['value']
if DEFAULT_ECG_TOKEN in question_value:
    question = question_value.replace(DEFAULT_ECG_TOKEN, '').strip()
    if question.startswith('\n'):
        question = question[1:]
else:
    question = question_value
```

## 关键修复点总结

1. **对话模板一致性**：确保推理时使用与训练时相同的对话模板 (`vicuna_v1`)
2. **ECG Token 处理**：使用 `DEFAULT_ECG_TOKEN` 常量和正确的格式
3. **Tokenization 一致性**：使用 `tokenizer_ecg_token` 函数处理包含 ECG token 的文本
4. **数据预处理一致性**：确保推理时的数据预处理与训练时保持一致

## 测试验证

运行测试脚本验证修复效果：

```bash
python test_inference_fix.py
```

## 预期效果

修复后，模型应该能够：
1. 正确识别和处理 ECG 输入数据
2. 生成与 ECG 内容相关的分析结果
3. 避免生成无关的科普信息
4. 提供针对具体 ECG 数据的诊断建议

## 注意事项

1. 确保所有路径配置正确
2. 验证 checkpoint 文件完整性
3. 检查 ECG 数据文件格式是否正确
4. 确认模型加载时没有警告或错误信息
