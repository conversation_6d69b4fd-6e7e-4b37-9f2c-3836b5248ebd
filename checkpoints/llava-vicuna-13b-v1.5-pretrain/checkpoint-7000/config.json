{"_name_or_path": "/data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5", "architectures": ["LlamaForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bos_token_id": 1, "eos_token_id": 2, "freeze_mm_mlp_adapter": false, "hidden_act": "silu", "hidden_size": 5120, "initializer_range": 0.02, "intermediate_size": 13824, "max_length": 4096, "max_position_embeddings": 4096, "mm_ecg_tower": "/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt", "mm_hidden_size": 768, "mm_patch_merge_type": "flat", "mm_projector_lr": null, "mm_projector_type": "linear", "mm_use_ecg_patch_token": false, "mm_use_ecg_start_end": false, "model_type": "llava_llama", "num_attention_heads": 40, "num_hidden_layers": 40, "num_key_value_heads": 40, "pad_token_id": 0, "pretraining_tp": 1, "rms_norm_eps": 1e-05, "rope_scaling": null, "rope_theta": 10000.0, "tie_word_embeddings": false, "tokenizer_model_max_length": 2048, "tokenizer_padding_side": "right", "torch_dtype": "float16", "transformers_version": "4.37.2", "tune_mm_mlp_adapter": true, "use_cache": false, "use_mm_proj": true, "vocab_size": 32000}