{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.4204545454545454, "eval_steps": 500, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0, "learning_rate": 3.125e-06, "loss": 1.879, "step": 1}, {"epoch": 0.0, "learning_rate": 6.25e-06, "loss": 2.0201, "step": 2}, {"epoch": 0.0, "learning_rate": 9.375000000000001e-06, "loss": 1.9607, "step": 3}, {"epoch": 0.01, "learning_rate": 1.25e-05, "loss": 1.9719, "step": 4}, {"epoch": 0.01, "learning_rate": 1.5625e-05, "loss": 1.9403, "step": 5}, {"epoch": 0.01, "learning_rate": 1.8750000000000002e-05, "loss": 2.0394, "step": 6}, {"epoch": 0.01, "learning_rate": 2.1875e-05, "loss": 1.9894, "step": 7}, {"epoch": 0.01, "learning_rate": 2.5e-05, "loss": 1.9953, "step": 8}, {"epoch": 0.01, "learning_rate": 2.8125000000000003e-05, "loss": 1.9032, "step": 9}, {"epoch": 0.01, "learning_rate": 3.125e-05, "loss": 1.8875, "step": 10}, {"epoch": 0.02, "learning_rate": 3.4375e-05, "loss": 1.7733, "step": 11}, {"epoch": 0.02, "learning_rate": 3.7500000000000003e-05, "loss": 1.7575, "step": 12}, {"epoch": 0.02, "learning_rate": 4.0625000000000005e-05, "loss": 1.6313, "step": 13}, {"epoch": 0.02, "learning_rate": 4.375e-05, "loss": 1.6614, "step": 14}, {"epoch": 0.02, "learning_rate": 4.6875e-05, "loss": 1.3717, "step": 15}, {"epoch": 0.02, "learning_rate": 5e-05, "loss": 1.4089, "step": 16}, {"epoch": 0.02, "learning_rate": 5.3125000000000004e-05, "loss": 1.5035, "step": 17}, {"epoch": 0.03, "learning_rate": 5.6250000000000005e-05, "loss": 1.4314, "step": 18}, {"epoch": 0.03, "learning_rate": 5.9375e-05, "loss": 1.3397, "step": 19}, {"epoch": 0.03, "learning_rate": 6.25e-05, "loss": 1.2895, "step": 20}, {"epoch": 0.03, "learning_rate": 6.562500000000001e-05, "loss": 1.2703, "step": 21}, {"epoch": 0.03, "learning_rate": 6.875e-05, "loss": 1.2024, "step": 22}, {"epoch": 0.03, "learning_rate": 7.1875e-05, "loss": 1.1181, "step": 23}, {"epoch": 0.03, "learning_rate": 7.500000000000001e-05, "loss": 1.0621, "step": 24}, {"epoch": 0.04, "learning_rate": 7.8125e-05, "loss": 1.1552, "step": 25}, {"epoch": 0.04, "learning_rate": 8.125000000000001e-05, "loss": 1.072, "step": 26}, {"epoch": 0.04, "learning_rate": 8.4375e-05, "loss": 1.0174, "step": 27}, {"epoch": 0.04, "learning_rate": 8.75e-05, "loss": 1.0041, "step": 28}, {"epoch": 0.04, "learning_rate": 9.062500000000001e-05, "loss": 0.9927, "step": 29}, {"epoch": 0.04, "learning_rate": 9.375e-05, "loss": 1.0451, "step": 30}, {"epoch": 0.04, "learning_rate": 9.687500000000001e-05, "loss": 0.9676, "step": 31}, {"epoch": 0.05, "learning_rate": 0.0001, "loss": 1.0218, "step": 32}, {"epoch": 0.05, "learning_rate": 0.000103125, "loss": 0.9371, "step": 33}, {"epoch": 0.05, "learning_rate": 0.00010625000000000001, "loss": 1.0346, "step": 34}, {"epoch": 0.05, "learning_rate": 0.000109375, "loss": 0.9525, "step": 35}, {"epoch": 0.05, "learning_rate": 0.00011250000000000001, "loss": 0.9403, "step": 36}, {"epoch": 0.05, "learning_rate": 0.000115625, "loss": 0.9365, "step": 37}, {"epoch": 0.05, "learning_rate": 0.00011875, "loss": 0.873, "step": 38}, {"epoch": 0.06, "learning_rate": 0.00012187500000000001, "loss": 0.9355, "step": 39}, {"epoch": 0.06, "learning_rate": 0.000125, "loss": 0.8364, "step": 40}, {"epoch": 0.06, "learning_rate": 0.000128125, "loss": 0.8648, "step": 41}, {"epoch": 0.06, "learning_rate": 0.00013125000000000002, "loss": 0.794, "step": 42}, {"epoch": 0.06, "learning_rate": 0.000134375, "loss": 0.8647, "step": 43}, {"epoch": 0.06, "learning_rate": 0.0001375, "loss": 0.86, "step": 44}, {"epoch": 0.06, "learning_rate": 0.00014062500000000002, "loss": 0.8198, "step": 45}, {"epoch": 0.07, "learning_rate": 0.00014375, "loss": 0.7834, "step": 46}, {"epoch": 0.07, "learning_rate": 0.000146875, "loss": 0.784, "step": 47}, {"epoch": 0.07, "learning_rate": 0.00015000000000000001, "loss": 0.7656, "step": 48}, {"epoch": 0.07, "learning_rate": 0.000153125, "loss": 0.7672, "step": 49}, {"epoch": 0.07, "learning_rate": 0.00015625, "loss": 0.757, "step": 50}, {"epoch": 0.07, "learning_rate": 0.000159375, "loss": 0.6981, "step": 51}, {"epoch": 0.07, "learning_rate": 0.00016250000000000002, "loss": 0.7089, "step": 52}, {"epoch": 0.08, "learning_rate": 0.000165625, "loss": 0.7049, "step": 53}, {"epoch": 0.08, "learning_rate": 0.00016875, "loss": 0.686, "step": 54}, {"epoch": 0.08, "learning_rate": 0.00017187500000000002, "loss": 0.682, "step": 55}, {"epoch": 0.08, "learning_rate": 0.000175, "loss": 0.6788, "step": 56}, {"epoch": 0.08, "learning_rate": 0.000178125, "loss": 0.7012, "step": 57}, {"epoch": 0.08, "learning_rate": 0.00018125000000000001, "loss": 0.7304, "step": 58}, {"epoch": 0.08, "learning_rate": 0.000184375, "loss": 0.7093, "step": 59}, {"epoch": 0.09, "learning_rate": 0.0001875, "loss": 0.71, "step": 60}, {"epoch": 0.09, "learning_rate": 0.000190625, "loss": 0.7122, "step": 61}, {"epoch": 0.09, "learning_rate": 0.00019375000000000002, "loss": 0.6775, "step": 62}, {"epoch": 0.09, "learning_rate": 0.000196875, "loss": 0.6428, "step": 63}, {"epoch": 0.09, "learning_rate": 0.0002, "loss": 0.6753, "step": 64}, {"epoch": 0.09, "learning_rate": 0.0001999998823451702, "loss": 0.6821, "step": 65}, {"epoch": 0.09, "learning_rate": 0.00019999952938095763, "loss": 0.6813, "step": 66}, {"epoch": 0.1, "learning_rate": 0.00019999894110819285, "loss": 0.6462, "step": 67}, {"epoch": 0.1, "learning_rate": 0.00019999811752826013, "loss": 0.6671, "step": 68}, {"epoch": 0.1, "learning_rate": 0.00019999705864309745, "loss": 0.6121, "step": 69}, {"epoch": 0.1, "learning_rate": 0.00019999576445519642, "loss": 0.6966, "step": 70}, {"epoch": 0.1, "learning_rate": 0.00019999423496760242, "loss": 0.6414, "step": 71}, {"epoch": 0.1, "learning_rate": 0.00019999247018391447, "loss": 0.6587, "step": 72}, {"epoch": 0.1, "learning_rate": 0.00019999047010828532, "loss": 0.7012, "step": 73}, {"epoch": 0.11, "learning_rate": 0.00019998823474542125, "loss": 0.6356, "step": 74}, {"epoch": 0.11, "learning_rate": 0.0001999857641005824, "loss": 0.6016, "step": 75}, {"epoch": 0.11, "learning_rate": 0.00019998305817958234, "loss": 0.6285, "step": 76}, {"epoch": 0.11, "learning_rate": 0.00019998011698878844, "loss": 0.617, "step": 77}, {"epoch": 0.11, "learning_rate": 0.00019997694053512156, "loss": 0.61, "step": 78}, {"epoch": 0.11, "learning_rate": 0.00019997352882605618, "loss": 0.6156, "step": 79}, {"epoch": 0.11, "learning_rate": 0.00019996988186962041, "loss": 0.5893, "step": 80}, {"epoch": 0.12, "learning_rate": 0.00019996599967439594, "loss": 0.6579, "step": 81}, {"epoch": 0.12, "learning_rate": 0.00019996188224951787, "loss": 0.5918, "step": 82}, {"epoch": 0.12, "learning_rate": 0.00019995752960467493, "loss": 0.5904, "step": 83}, {"epoch": 0.12, "learning_rate": 0.00019995294175010933, "loss": 0.5991, "step": 84}, {"epoch": 0.12, "learning_rate": 0.0001999481186966167, "loss": 0.6022, "step": 85}, {"epoch": 0.12, "learning_rate": 0.00019994306045554617, "loss": 0.6016, "step": 86}, {"epoch": 0.12, "learning_rate": 0.0001999377670388003, "loss": 0.6342, "step": 87}, {"epoch": 0.12, "learning_rate": 0.00019993223845883495, "loss": 0.5841, "step": 88}, {"epoch": 0.13, "learning_rate": 0.00019992647472865947, "loss": 0.5144, "step": 89}, {"epoch": 0.13, "learning_rate": 0.0001999204758618364, "loss": 0.5578, "step": 90}, {"epoch": 0.13, "learning_rate": 0.00019991424187248172, "loss": 0.5883, "step": 91}, {"epoch": 0.13, "learning_rate": 0.00019990777277526455, "loss": 0.5013, "step": 92}, {"epoch": 0.13, "learning_rate": 0.00019990106858540734, "loss": 0.5896, "step": 93}, {"epoch": 0.13, "learning_rate": 0.0001998941293186857, "loss": 0.6137, "step": 94}, {"epoch": 0.13, "learning_rate": 0.00019988695499142836, "loss": 0.5656, "step": 95}, {"epoch": 0.14, "learning_rate": 0.00019987954562051725, "loss": 0.546, "step": 96}, {"epoch": 0.14, "learning_rate": 0.0001998719012233873, "loss": 0.5499, "step": 97}, {"epoch": 0.14, "learning_rate": 0.00019986402181802653, "loss": 0.6248, "step": 98}, {"epoch": 0.14, "learning_rate": 0.00019985590742297596, "loss": 0.6231, "step": 99}, {"epoch": 0.14, "learning_rate": 0.0001998475580573295, "loss": 0.5057, "step": 100}, {"epoch": 0.14, "learning_rate": 0.00019983897374073402, "loss": 0.5691, "step": 101}, {"epoch": 0.14, "learning_rate": 0.0001998301544933893, "loss": 0.5485, "step": 102}, {"epoch": 0.15, "learning_rate": 0.00019982110033604783, "loss": 0.5586, "step": 103}, {"epoch": 0.15, "learning_rate": 0.00019981181129001492, "loss": 0.5331, "step": 104}, {"epoch": 0.15, "learning_rate": 0.00019980228737714864, "loss": 0.584, "step": 105}, {"epoch": 0.15, "learning_rate": 0.0001997925286198596, "loss": 0.5505, "step": 106}, {"epoch": 0.15, "learning_rate": 0.00019978253504111118, "loss": 0.5311, "step": 107}, {"epoch": 0.15, "learning_rate": 0.00019977230666441917, "loss": 0.5739, "step": 108}, {"epoch": 0.15, "learning_rate": 0.00019976184351385196, "loss": 0.5513, "step": 109}, {"epoch": 0.16, "learning_rate": 0.00019975114561403037, "loss": 0.5849, "step": 110}, {"epoch": 0.16, "learning_rate": 0.00019974021299012752, "loss": 0.5628, "step": 111}, {"epoch": 0.16, "learning_rate": 0.00019972904566786903, "loss": 0.5164, "step": 112}, {"epoch": 0.16, "learning_rate": 0.00019971764367353263, "loss": 0.5575, "step": 113}, {"epoch": 0.16, "learning_rate": 0.0001997060070339483, "loss": 0.5807, "step": 114}, {"epoch": 0.16, "learning_rate": 0.00019969413577649822, "loss": 0.5523, "step": 115}, {"epoch": 0.16, "learning_rate": 0.00019968202992911658, "loss": 0.6021, "step": 116}, {"epoch": 0.17, "learning_rate": 0.0001996696895202896, "loss": 0.5406, "step": 117}, {"epoch": 0.17, "learning_rate": 0.0001996571145790555, "loss": 0.543, "step": 118}, {"epoch": 0.17, "learning_rate": 0.00019964430513500427, "loss": 0.6027, "step": 119}, {"epoch": 0.17, "learning_rate": 0.0001996312612182778, "loss": 0.5268, "step": 120}, {"epoch": 0.17, "learning_rate": 0.00019961798285956972, "loss": 0.544, "step": 121}, {"epoch": 0.17, "learning_rate": 0.00019960447009012523, "loss": 0.4913, "step": 122}, {"epoch": 0.17, "learning_rate": 0.00019959072294174117, "loss": 0.5317, "step": 123}, {"epoch": 0.18, "learning_rate": 0.000199576741446766, "loss": 0.5181, "step": 124}, {"epoch": 0.18, "learning_rate": 0.00019956252563809942, "loss": 0.5023, "step": 125}, {"epoch": 0.18, "learning_rate": 0.0001995480755491927, "loss": 0.5209, "step": 126}, {"epoch": 0.18, "learning_rate": 0.00019953339121404825, "loss": 0.5343, "step": 127}, {"epoch": 0.18, "learning_rate": 0.0001995184726672197, "loss": 0.5149, "step": 128}, {"epoch": 0.18, "learning_rate": 0.00019950331994381189, "loss": 0.5383, "step": 129}, {"epoch": 0.18, "learning_rate": 0.00019948793307948058, "loss": 0.5396, "step": 130}, {"epoch": 0.19, "learning_rate": 0.00019947231211043257, "loss": 0.4913, "step": 131}, {"epoch": 0.19, "learning_rate": 0.00019945645707342556, "loss": 0.5484, "step": 132}, {"epoch": 0.19, "learning_rate": 0.00019944036800576792, "loss": 0.55, "step": 133}, {"epoch": 0.19, "learning_rate": 0.0001994240449453188, "loss": 0.522, "step": 134}, {"epoch": 0.19, "learning_rate": 0.00019940748793048795, "loss": 0.5363, "step": 135}, {"epoch": 0.19, "learning_rate": 0.00019939069700023563, "loss": 0.5315, "step": 136}, {"epoch": 0.19, "learning_rate": 0.0001993736721940725, "loss": 0.5267, "step": 137}, {"epoch": 0.2, "learning_rate": 0.00019935641355205955, "loss": 0.5188, "step": 138}, {"epoch": 0.2, "learning_rate": 0.00019933892111480807, "loss": 0.529, "step": 139}, {"epoch": 0.2, "learning_rate": 0.00019932119492347945, "loss": 0.5441, "step": 140}, {"epoch": 0.2, "learning_rate": 0.00019930323501978517, "loss": 0.5566, "step": 141}, {"epoch": 0.2, "learning_rate": 0.0001992850414459865, "loss": 0.5829, "step": 142}, {"epoch": 0.2, "learning_rate": 0.0001992666142448948, "loss": 0.5148, "step": 143}, {"epoch": 0.2, "learning_rate": 0.000199247953459871, "loss": 0.5074, "step": 144}, {"epoch": 0.21, "learning_rate": 0.00019922905913482574, "loss": 0.5473, "step": 145}, {"epoch": 0.21, "learning_rate": 0.00019920993131421918, "loss": 0.5488, "step": 146}, {"epoch": 0.21, "learning_rate": 0.00019919057004306095, "loss": 0.546, "step": 147}, {"epoch": 0.21, "learning_rate": 0.00019917097536690997, "loss": 0.4565, "step": 148}, {"epoch": 0.21, "learning_rate": 0.00019915114733187438, "loss": 0.5145, "step": 149}, {"epoch": 0.21, "learning_rate": 0.00019913108598461156, "loss": 0.5753, "step": 150}, {"epoch": 0.21, "learning_rate": 0.0001991107913723277, "loss": 0.4592, "step": 151}, {"epoch": 0.22, "learning_rate": 0.000199090263542778, "loss": 0.5338, "step": 152}, {"epoch": 0.22, "learning_rate": 0.00019906950254426647, "loss": 0.6067, "step": 153}, {"epoch": 0.22, "learning_rate": 0.0001990485084256457, "loss": 0.5154, "step": 154}, {"epoch": 0.22, "learning_rate": 0.00019902728123631693, "loss": 0.5296, "step": 155}, {"epoch": 0.22, "learning_rate": 0.00019900582102622973, "loss": 0.5191, "step": 156}, {"epoch": 0.22, "learning_rate": 0.00019898412784588208, "loss": 0.5353, "step": 157}, {"epoch": 0.22, "learning_rate": 0.0001989622017463201, "loss": 0.4864, "step": 158}, {"epoch": 0.23, "learning_rate": 0.00019894004277913805, "loss": 0.531, "step": 159}, {"epoch": 0.23, "learning_rate": 0.0001989176509964781, "loss": 0.7777, "step": 160}, {"epoch": 0.23, "learning_rate": 0.00019889502645103032, "loss": 0.5556, "step": 161}, {"epoch": 0.23, "learning_rate": 0.00019887216919603238, "loss": 0.4751, "step": 162}, {"epoch": 0.23, "learning_rate": 0.00019884907928526967, "loss": 0.5465, "step": 163}, {"epoch": 0.23, "learning_rate": 0.00019882575677307495, "loss": 0.5302, "step": 164}, {"epoch": 0.23, "learning_rate": 0.00019880220171432839, "loss": 0.5246, "step": 165}, {"epoch": 0.24, "learning_rate": 0.00019877841416445722, "loss": 0.4843, "step": 166}, {"epoch": 0.24, "learning_rate": 0.00019875439417943593, "loss": 0.4844, "step": 167}, {"epoch": 0.24, "learning_rate": 0.00019873014181578586, "loss": 0.5731, "step": 168}, {"epoch": 0.24, "learning_rate": 0.0001987056571305751, "loss": 0.481, "step": 169}, {"epoch": 0.24, "learning_rate": 0.00019868094018141857, "loss": 0.5164, "step": 170}, {"epoch": 0.24, "learning_rate": 0.00019865599102647754, "loss": 0.4877, "step": 171}, {"epoch": 0.24, "learning_rate": 0.0001986308097244599, "loss": 0.452, "step": 172}, {"epoch": 0.25, "learning_rate": 0.00019860539633461953, "loss": 0.4933, "step": 173}, {"epoch": 0.25, "learning_rate": 0.00019857975091675675, "loss": 0.5214, "step": 174}, {"epoch": 0.25, "learning_rate": 0.00019855387353121762, "loss": 0.5485, "step": 175}, {"epoch": 0.25, "learning_rate": 0.0001985277642388941, "loss": 0.5629, "step": 176}, {"epoch": 0.25, "learning_rate": 0.000198501423101224, "loss": 0.5514, "step": 177}, {"epoch": 0.25, "learning_rate": 0.00019847485018019043, "loss": 0.475, "step": 178}, {"epoch": 0.25, "learning_rate": 0.0001984480455383221, "loss": 0.5007, "step": 179}, {"epoch": 0.26, "learning_rate": 0.0001984210092386929, "loss": 0.473, "step": 180}, {"epoch": 0.26, "learning_rate": 0.0001983937413449219, "loss": 0.5216, "step": 181}, {"epoch": 0.26, "learning_rate": 0.00019836624192117304, "loss": 0.4694, "step": 182}, {"epoch": 0.26, "learning_rate": 0.00019833851103215512, "loss": 0.5611, "step": 183}, {"epoch": 0.26, "learning_rate": 0.00019831054874312165, "loss": 0.5268, "step": 184}, {"epoch": 0.26, "learning_rate": 0.00019828235511987053, "loss": 0.4794, "step": 185}, {"epoch": 0.26, "learning_rate": 0.00019825393022874415, "loss": 0.5055, "step": 186}, {"epoch": 0.27, "learning_rate": 0.00019822527413662896, "loss": 0.4928, "step": 187}, {"epoch": 0.27, "learning_rate": 0.00019819638691095554, "loss": 0.5166, "step": 188}, {"epoch": 0.27, "learning_rate": 0.00019816726861969833, "loss": 0.4815, "step": 189}, {"epoch": 0.27, "learning_rate": 0.00019813791933137546, "loss": 0.4745, "step": 190}, {"epoch": 0.27, "learning_rate": 0.00019810833911504866, "loss": 0.52, "step": 191}, {"epoch": 0.27, "learning_rate": 0.00019807852804032305, "loss": 0.4522, "step": 192}, {"epoch": 0.27, "learning_rate": 0.00019804848617734696, "loss": 0.5077, "step": 193}, {"epoch": 0.28, "learning_rate": 0.00019801821359681173, "loss": 0.5345, "step": 194}, {"epoch": 0.28, "learning_rate": 0.00019798771036995177, "loss": 0.4586, "step": 195}, {"epoch": 0.28, "learning_rate": 0.00019795697656854405, "loss": 0.5182, "step": 196}, {"epoch": 0.28, "learning_rate": 0.0001979260122649082, "loss": 0.5526, "step": 197}, {"epoch": 0.28, "learning_rate": 0.00019789481753190624, "loss": 0.5176, "step": 198}, {"epoch": 0.28, "learning_rate": 0.00019786339244294232, "loss": 0.5162, "step": 199}, {"epoch": 0.28, "learning_rate": 0.00019783173707196277, "loss": 0.5386, "step": 200}, {"epoch": 0.29, "learning_rate": 0.00019779985149345574, "loss": 0.4674, "step": 201}, {"epoch": 0.29, "learning_rate": 0.000197767735782451, "loss": 0.5029, "step": 202}, {"epoch": 0.29, "learning_rate": 0.00019773539001452002, "loss": 0.4387, "step": 203}, {"epoch": 0.29, "learning_rate": 0.00019770281426577545, "loss": 0.4421, "step": 204}, {"epoch": 0.29, "learning_rate": 0.00019767000861287118, "loss": 0.5304, "step": 205}, {"epoch": 0.29, "learning_rate": 0.0001976369731330021, "loss": 0.5094, "step": 206}, {"epoch": 0.29, "learning_rate": 0.00019760370790390392, "loss": 0.4549, "step": 207}, {"epoch": 0.3, "learning_rate": 0.00019757021300385286, "loss": 0.4701, "step": 208}, {"epoch": 0.3, "learning_rate": 0.00019753648851166572, "loss": 0.5286, "step": 209}, {"epoch": 0.3, "learning_rate": 0.00019750253450669943, "loss": 0.4868, "step": 210}, {"epoch": 0.3, "learning_rate": 0.0001974683510688511, "loss": 0.4631, "step": 211}, {"epoch": 0.3, "learning_rate": 0.00019743393827855758, "loss": 0.5099, "step": 212}, {"epoch": 0.3, "learning_rate": 0.0001973992962167956, "loss": 0.5604, "step": 213}, {"epoch": 0.3, "learning_rate": 0.0001973644249650812, "loss": 0.52, "step": 214}, {"epoch": 0.31, "learning_rate": 0.00019732932460546986, "loss": 0.457, "step": 215}, {"epoch": 0.31, "learning_rate": 0.00019729399522055603, "loss": 0.5672, "step": 216}, {"epoch": 0.31, "learning_rate": 0.00019725843689347324, "loss": 0.5077, "step": 217}, {"epoch": 0.31, "learning_rate": 0.00019722264970789365, "loss": 0.483, "step": 218}, {"epoch": 0.31, "learning_rate": 0.00019718663374802795, "loss": 0.5057, "step": 219}, {"epoch": 0.31, "learning_rate": 0.00019715038909862517, "loss": 0.4997, "step": 220}, {"epoch": 0.31, "learning_rate": 0.00019711391584497251, "loss": 0.4265, "step": 221}, {"epoch": 0.32, "learning_rate": 0.00019707721407289505, "loss": 0.4523, "step": 222}, {"epoch": 0.32, "learning_rate": 0.00019704028386875554, "loss": 0.4835, "step": 223}, {"epoch": 0.32, "learning_rate": 0.00019700312531945442, "loss": 0.5532, "step": 224}, {"epoch": 0.32, "learning_rate": 0.00019696573851242925, "loss": 0.4967, "step": 225}, {"epoch": 0.32, "learning_rate": 0.00019692812353565487, "loss": 0.4931, "step": 226}, {"epoch": 0.32, "learning_rate": 0.0001968902804776429, "loss": 0.5019, "step": 227}, {"epoch": 0.32, "learning_rate": 0.00019685220942744174, "loss": 0.5212, "step": 228}, {"epoch": 0.33, "learning_rate": 0.00019681391047463628, "loss": 0.5012, "step": 229}, {"epoch": 0.33, "learning_rate": 0.00019677538370934755, "loss": 0.461, "step": 230}, {"epoch": 0.33, "learning_rate": 0.00019673662922223287, "loss": 0.4767, "step": 231}, {"epoch": 0.33, "learning_rate": 0.00019669764710448522, "loss": 0.4702, "step": 232}, {"epoch": 0.33, "learning_rate": 0.00019665843744783333, "loss": 0.4367, "step": 233}, {"epoch": 0.33, "learning_rate": 0.00019661900034454127, "loss": 0.4437, "step": 234}, {"epoch": 0.33, "learning_rate": 0.00019657933588740838, "loss": 0.4674, "step": 235}, {"epoch": 0.34, "learning_rate": 0.00019653944416976894, "loss": 0.4347, "step": 236}, {"epoch": 0.34, "learning_rate": 0.00019649932528549205, "loss": 0.4794, "step": 237}, {"epoch": 0.34, "learning_rate": 0.00019645897932898127, "loss": 0.45, "step": 238}, {"epoch": 0.34, "learning_rate": 0.00019641840639517458, "loss": 0.4798, "step": 239}, {"epoch": 0.34, "learning_rate": 0.000196377606579544, "loss": 0.5001, "step": 240}, {"epoch": 0.34, "learning_rate": 0.00019633657997809541, "loss": 0.4986, "step": 241}, {"epoch": 0.34, "learning_rate": 0.00019629532668736838, "loss": 0.4708, "step": 242}, {"epoch": 0.35, "learning_rate": 0.00019625384680443592, "loss": 0.5421, "step": 243}, {"epoch": 0.35, "learning_rate": 0.00019621214042690418, "loss": 0.5062, "step": 244}, {"epoch": 0.35, "learning_rate": 0.00019617020765291224, "loss": 0.5255, "step": 245}, {"epoch": 0.35, "learning_rate": 0.00019612804858113207, "loss": 0.4295, "step": 246}, {"epoch": 0.35, "learning_rate": 0.000196085663310768, "loss": 0.4757, "step": 247}, {"epoch": 0.35, "learning_rate": 0.0001960430519415566, "loss": 0.4842, "step": 248}, {"epoch": 0.35, "learning_rate": 0.0001960002145737666, "loss": 0.5124, "step": 249}, {"epoch": 0.36, "learning_rate": 0.00019595715130819844, "loss": 0.4752, "step": 250}, {"epoch": 0.36, "learning_rate": 0.0001959138622461842, "loss": 0.5261, "step": 251}, {"epoch": 0.36, "learning_rate": 0.00019587034748958716, "loss": 0.4542, "step": 252}, {"epoch": 0.36, "learning_rate": 0.0001958266071408018, "loss": 0.4972, "step": 253}, {"epoch": 0.36, "learning_rate": 0.0001957826413027533, "loss": 0.5065, "step": 254}, {"epoch": 0.36, "learning_rate": 0.0001957384500788976, "loss": 0.4704, "step": 255}, {"epoch": 0.36, "learning_rate": 0.0001956940335732209, "loss": 0.6936, "step": 256}, {"epoch": 0.37, "learning_rate": 0.00019564939189023952, "loss": 0.6363, "step": 257}, {"epoch": 0.37, "learning_rate": 0.00019560452513499966, "loss": 0.4575, "step": 258}, {"epoch": 0.37, "learning_rate": 0.00019555943341307712, "loss": 0.4495, "step": 259}, {"epoch": 0.37, "learning_rate": 0.0001955141168305771, "loss": 0.4952, "step": 260}, {"epoch": 0.37, "learning_rate": 0.00019546857549413384, "loss": 0.4597, "step": 261}, {"epoch": 0.37, "learning_rate": 0.00019542280951091056, "loss": 0.4649, "step": 262}, {"epoch": 0.37, "learning_rate": 0.00019537681898859903, "loss": 0.4672, "step": 263}, {"epoch": 0.38, "learning_rate": 0.00019533060403541938, "loss": 0.5061, "step": 264}, {"epoch": 0.38, "learning_rate": 0.00019528416476011988, "loss": 0.4712, "step": 265}, {"epoch": 0.38, "learning_rate": 0.0001952375012719766, "loss": 0.5007, "step": 266}, {"epoch": 0.38, "learning_rate": 0.00019519061368079323, "loss": 0.5047, "step": 267}, {"epoch": 0.38, "learning_rate": 0.00019514350209690084, "loss": 0.4631, "step": 268}, {"epoch": 0.38, "learning_rate": 0.00019509616663115754, "loss": 0.4445, "step": 269}, {"epoch": 0.38, "learning_rate": 0.0001950486073949482, "loss": 0.4956, "step": 270}, {"epoch": 0.38, "learning_rate": 0.0001950008245001843, "loss": 0.4738, "step": 271}, {"epoch": 0.39, "learning_rate": 0.00019495281805930367, "loss": 0.4975, "step": 272}, {"epoch": 0.39, "learning_rate": 0.00019490458818527008, "loss": 0.4929, "step": 273}, {"epoch": 0.39, "learning_rate": 0.00019485613499157305, "loss": 0.4764, "step": 274}, {"epoch": 0.39, "learning_rate": 0.0001948074585922276, "loss": 0.6595, "step": 275}, {"epoch": 0.39, "learning_rate": 0.0001947585591017741, "loss": 0.4671, "step": 276}, {"epoch": 0.39, "learning_rate": 0.00019470943663527773, "loss": 0.4385, "step": 277}, {"epoch": 0.39, "learning_rate": 0.00019466009130832837, "loss": 0.4875, "step": 278}, {"epoch": 0.4, "learning_rate": 0.00019461052323704037, "loss": 0.47, "step": 279}, {"epoch": 0.4, "learning_rate": 0.00019456073253805214, "loss": 0.4543, "step": 280}, {"epoch": 0.4, "learning_rate": 0.00019451071932852606, "loss": 0.4534, "step": 281}, {"epoch": 0.4, "learning_rate": 0.00019446048372614804, "loss": 0.5182, "step": 282}, {"epoch": 0.4, "learning_rate": 0.00019441002584912726, "loss": 0.4929, "step": 283}, {"epoch": 0.4, "learning_rate": 0.00019435934581619603, "loss": 0.479, "step": 284}, {"epoch": 0.4, "learning_rate": 0.00019430844374660936, "loss": 0.422, "step": 285}, {"epoch": 0.41, "learning_rate": 0.0001942573197601447, "loss": 0.489, "step": 286}, {"epoch": 0.41, "learning_rate": 0.00019420597397710174, "loss": 0.4499, "step": 287}, {"epoch": 0.41, "learning_rate": 0.00019415440651830208, "loss": 0.5082, "step": 288}, {"epoch": 0.41, "learning_rate": 0.00019410261750508892, "loss": 0.496, "step": 289}, {"epoch": 0.41, "learning_rate": 0.00019405060705932683, "loss": 0.518, "step": 290}, {"epoch": 0.41, "learning_rate": 0.00019399837530340142, "loss": 0.4779, "step": 291}, {"epoch": 0.41, "learning_rate": 0.000193945922360219, "loss": 0.4337, "step": 292}, {"epoch": 0.42, "learning_rate": 0.00019389324835320646, "loss": 0.4693, "step": 293}, {"epoch": 0.42, "learning_rate": 0.0001938403534063108, "loss": 0.4474, "step": 294}, {"epoch": 0.42, "learning_rate": 0.000193787237643999, "loss": 0.4901, "step": 295}, {"epoch": 0.42, "learning_rate": 0.00019373390119125752, "loss": 0.455, "step": 296}, {"epoch": 0.42, "learning_rate": 0.00019368034417359216, "loss": 0.4534, "step": 297}, {"epoch": 0.42, "learning_rate": 0.00019362656671702784, "loss": 0.511, "step": 298}, {"epoch": 0.42, "learning_rate": 0.00019357256894810804, "loss": 0.4405, "step": 299}, {"epoch": 0.43, "learning_rate": 0.00019351835099389478, "loss": 0.5082, "step": 300}, {"epoch": 0.43, "learning_rate": 0.00019346391298196808, "loss": 0.4824, "step": 301}, {"epoch": 0.43, "learning_rate": 0.00019340925504042592, "loss": 0.4905, "step": 302}, {"epoch": 0.43, "learning_rate": 0.00019335437729788363, "loss": 0.4913, "step": 303}, {"epoch": 0.43, "learning_rate": 0.0001932992798834739, "loss": 0.425, "step": 304}, {"epoch": 0.43, "learning_rate": 0.00019324396292684623, "loss": 0.446, "step": 305}, {"epoch": 0.43, "learning_rate": 0.00019318842655816683, "loss": 0.4836, "step": 306}, {"epoch": 0.44, "learning_rate": 0.00019313267090811804, "loss": 0.4826, "step": 307}, {"epoch": 0.44, "learning_rate": 0.00019307669610789838, "loss": 0.4565, "step": 308}, {"epoch": 0.44, "learning_rate": 0.00019302050228922193, "loss": 0.4743, "step": 309}, {"epoch": 0.44, "learning_rate": 0.00019296408958431816, "loss": 0.4451, "step": 310}, {"epoch": 0.44, "learning_rate": 0.0001929074581259316, "loss": 0.4836, "step": 311}, {"epoch": 0.44, "learning_rate": 0.00019285060804732158, "loss": 0.4596, "step": 312}, {"epoch": 0.44, "learning_rate": 0.0001927935394822618, "loss": 0.4414, "step": 313}, {"epoch": 0.45, "learning_rate": 0.0001927362525650401, "loss": 0.4205, "step": 314}, {"epoch": 0.45, "learning_rate": 0.00019267874743045818, "loss": 0.467, "step": 315}, {"epoch": 0.45, "learning_rate": 0.00019262102421383116, "loss": 0.4648, "step": 316}, {"epoch": 0.45, "learning_rate": 0.0001925630830509873, "loss": 0.4922, "step": 317}, {"epoch": 0.45, "learning_rate": 0.00019250492407826776, "loss": 0.4716, "step": 318}, {"epoch": 0.45, "learning_rate": 0.0001924465474325263, "loss": 0.4912, "step": 319}, {"epoch": 0.45, "learning_rate": 0.0001923879532511287, "loss": 0.465, "step": 320}, {"epoch": 0.46, "learning_rate": 0.00019232914167195277, "loss": 0.4518, "step": 321}, {"epoch": 0.46, "learning_rate": 0.00019227011283338787, "loss": 0.4569, "step": 322}, {"epoch": 0.46, "learning_rate": 0.00019221086687433453, "loss": 0.4797, "step": 323}, {"epoch": 0.46, "learning_rate": 0.0001921514039342042, "loss": 0.4831, "step": 324}, {"epoch": 0.46, "learning_rate": 0.00019209172415291897, "loss": 0.5159, "step": 325}, {"epoch": 0.46, "learning_rate": 0.0001920318276709111, "loss": 0.4666, "step": 326}, {"epoch": 0.46, "learning_rate": 0.00019197171462912276, "loss": 0.4557, "step": 327}, {"epoch": 0.47, "learning_rate": 0.0001919113851690058, "loss": 0.4493, "step": 328}, {"epoch": 0.47, "learning_rate": 0.00019185083943252122, "loss": 0.5566, "step": 329}, {"epoch": 0.47, "learning_rate": 0.00019179007756213906, "loss": 0.4992, "step": 330}, {"epoch": 0.47, "learning_rate": 0.0001917290997008378, "loss": 0.5923, "step": 331}, {"epoch": 0.47, "learning_rate": 0.00019166790599210428, "loss": 0.4521, "step": 332}, {"epoch": 0.47, "learning_rate": 0.00019160649657993316, "loss": 0.452, "step": 333}, {"epoch": 0.47, "learning_rate": 0.0001915448716088268, "loss": 0.4689, "step": 334}, {"epoch": 0.48, "learning_rate": 0.00019148303122379462, "loss": 0.469, "step": 335}, {"epoch": 0.48, "learning_rate": 0.00019142097557035308, "loss": 0.4638, "step": 336}, {"epoch": 0.48, "learning_rate": 0.0001913587047945251, "loss": 0.6187, "step": 337}, {"epoch": 0.48, "learning_rate": 0.00019129621904283983, "loss": 0.4396, "step": 338}, {"epoch": 0.48, "learning_rate": 0.00019123351846233227, "loss": 0.4665, "step": 339}, {"epoch": 0.48, "learning_rate": 0.000191170603200543, "loss": 0.4498, "step": 340}, {"epoch": 0.48, "learning_rate": 0.00019110747340551765, "loss": 0.483, "step": 341}, {"epoch": 0.49, "learning_rate": 0.00019104412922580673, "loss": 0.4782, "step": 342}, {"epoch": 0.49, "learning_rate": 0.00019098057081046524, "loss": 0.4919, "step": 343}, {"epoch": 0.49, "learning_rate": 0.00019091679830905226, "loss": 0.4522, "step": 344}, {"epoch": 0.49, "learning_rate": 0.00019085281187163061, "loss": 0.4862, "step": 345}, {"epoch": 0.49, "learning_rate": 0.00019078861164876663, "loss": 0.4256, "step": 346}, {"epoch": 0.49, "learning_rate": 0.0001907241977915296, "loss": 0.4699, "step": 347}, {"epoch": 0.49, "learning_rate": 0.00019065957045149155, "loss": 0.4685, "step": 348}, {"epoch": 0.5, "learning_rate": 0.00019059472978072685, "loss": 0.4591, "step": 349}, {"epoch": 0.5, "learning_rate": 0.0001905296759318119, "loss": 0.5427, "step": 350}, {"epoch": 0.5, "learning_rate": 0.00019046440905782464, "loss": 0.4739, "step": 351}, {"epoch": 0.5, "learning_rate": 0.00019039892931234435, "loss": 0.455, "step": 352}, {"epoch": 0.5, "learning_rate": 0.00019033323684945118, "loss": 0.4879, "step": 353}, {"epoch": 0.5, "learning_rate": 0.0001902673318237259, "loss": 0.4165, "step": 354}, {"epoch": 0.5, "learning_rate": 0.00019020121439024932, "loss": 0.4527, "step": 355}, {"epoch": 0.51, "learning_rate": 0.0001901348847046022, "loss": 0.5734, "step": 356}, {"epoch": 0.51, "learning_rate": 0.00019006834292286472, "loss": 0.4861, "step": 357}, {"epoch": 0.51, "learning_rate": 0.00019000158920161604, "loss": 0.4878, "step": 358}, {"epoch": 0.51, "learning_rate": 0.00018993462369793415, "loss": 0.4038, "step": 359}, {"epoch": 0.51, "learning_rate": 0.0001898674465693954, "loss": 0.4888, "step": 360}, {"epoch": 0.51, "learning_rate": 0.000189800057974074, "loss": 0.4435, "step": 361}, {"epoch": 0.51, "learning_rate": 0.00018973245807054183, "loss": 0.477, "step": 362}, {"epoch": 0.52, "learning_rate": 0.00018966464701786803, "loss": 0.4694, "step": 363}, {"epoch": 0.52, "learning_rate": 0.00018959662497561853, "loss": 0.5219, "step": 364}, {"epoch": 0.52, "learning_rate": 0.00018952839210385576, "loss": 0.4658, "step": 365}, {"epoch": 0.52, "learning_rate": 0.00018945994856313828, "loss": 0.4935, "step": 366}, {"epoch": 0.52, "learning_rate": 0.00018939129451452035, "loss": 0.463, "step": 367}, {"epoch": 0.52, "learning_rate": 0.00018932243011955154, "loss": 0.4348, "step": 368}, {"epoch": 0.52, "learning_rate": 0.00018925335554027646, "loss": 0.5122, "step": 369}, {"epoch": 0.53, "learning_rate": 0.0001891840709392343, "loss": 0.4088, "step": 370}, {"epoch": 0.53, "learning_rate": 0.00018911457647945832, "loss": 0.5123, "step": 371}, {"epoch": 0.53, "learning_rate": 0.0001890448723244758, "loss": 0.4706, "step": 372}, {"epoch": 0.53, "learning_rate": 0.0001889749586383073, "loss": 0.4223, "step": 373}, {"epoch": 0.53, "learning_rate": 0.00018890483558546648, "loss": 0.4806, "step": 374}, {"epoch": 0.53, "learning_rate": 0.00018883450333095965, "loss": 0.438, "step": 375}, {"epoch": 0.53, "learning_rate": 0.0001887639620402854, "loss": 0.4521, "step": 376}, {"epoch": 0.54, "learning_rate": 0.00018869321187943422, "loss": 0.4752, "step": 377}, {"epoch": 0.54, "learning_rate": 0.00018862225301488806, "loss": 0.4203, "step": 378}, {"epoch": 0.54, "learning_rate": 0.00018855108561362, "loss": 0.4235, "step": 379}, {"epoch": 0.54, "learning_rate": 0.0001884797098430938, "loss": 0.503, "step": 380}, {"epoch": 0.54, "learning_rate": 0.00018840812587126352, "loss": 0.4616, "step": 381}, {"epoch": 0.54, "learning_rate": 0.00018833633386657316, "loss": 0.4529, "step": 382}, {"epoch": 0.54, "learning_rate": 0.0001882643339979563, "loss": 0.4528, "step": 383}, {"epoch": 0.55, "learning_rate": 0.0001881921264348355, "loss": 0.461, "step": 384}, {"epoch": 0.55, "learning_rate": 0.0001881197113471222, "loss": 0.4955, "step": 385}, {"epoch": 0.55, "learning_rate": 0.00018804708890521609, "loss": 0.4452, "step": 386}, {"epoch": 0.55, "learning_rate": 0.00018797425928000476, "loss": 0.4247, "step": 387}, {"epoch": 0.55, "learning_rate": 0.00018790122264286335, "loss": 0.4939, "step": 388}, {"epoch": 0.55, "learning_rate": 0.0001878279791656542, "loss": 0.4524, "step": 389}, {"epoch": 0.55, "learning_rate": 0.00018775452902072614, "loss": 0.4602, "step": 390}, {"epoch": 0.56, "learning_rate": 0.00018768087238091457, "loss": 0.4751, "step": 391}, {"epoch": 0.56, "learning_rate": 0.00018760700941954065, "loss": 0.521, "step": 392}, {"epoch": 0.56, "learning_rate": 0.0001875329403104111, "loss": 0.4092, "step": 393}, {"epoch": 0.56, "learning_rate": 0.00018745866522781762, "loss": 0.4534, "step": 394}, {"epoch": 0.56, "learning_rate": 0.0001873841843465367, "loss": 0.465, "step": 395}, {"epoch": 0.56, "learning_rate": 0.00018730949784182902, "loss": 0.457, "step": 396}, {"epoch": 0.56, "learning_rate": 0.00018723460588943914, "loss": 0.4319, "step": 397}, {"epoch": 0.57, "learning_rate": 0.0001871595086655951, "loss": 0.4778, "step": 398}, {"epoch": 0.57, "learning_rate": 0.0001870842063470079, "loss": 0.4857, "step": 399}, {"epoch": 0.57, "learning_rate": 0.00018700869911087115, "loss": 0.4247, "step": 400}, {"epoch": 0.57, "learning_rate": 0.0001869329871348607, "loss": 0.4665, "step": 401}, {"epoch": 0.57, "learning_rate": 0.0001868570705971341, "loss": 0.4857, "step": 402}, {"epoch": 0.57, "learning_rate": 0.00018678094967633033, "loss": 0.4664, "step": 403}, {"epoch": 0.57, "learning_rate": 0.00018670462455156928, "loss": 0.4938, "step": 404}, {"epoch": 0.58, "learning_rate": 0.0001866280954024513, "loss": 0.4675, "step": 405}, {"epoch": 0.58, "learning_rate": 0.00018655136240905692, "loss": 0.4623, "step": 406}, {"epoch": 0.58, "learning_rate": 0.00018647442575194623, "loss": 0.4516, "step": 407}, {"epoch": 0.58, "learning_rate": 0.0001863972856121587, "loss": 0.3981, "step": 408}, {"epoch": 0.58, "learning_rate": 0.00018631994217121242, "loss": 0.4271, "step": 409}, {"epoch": 0.58, "learning_rate": 0.00018624239561110408, "loss": 0.4125, "step": 410}, {"epoch": 0.58, "learning_rate": 0.00018616464611430816, "loss": 0.4884, "step": 411}, {"epoch": 0.59, "learning_rate": 0.00018608669386377673, "loss": 0.4218, "step": 412}, {"epoch": 0.59, "learning_rate": 0.000186008539042939, "loss": 0.4757, "step": 413}, {"epoch": 0.59, "learning_rate": 0.00018593018183570085, "loss": 0.4201, "step": 414}, {"epoch": 0.59, "learning_rate": 0.0001858516224264443, "loss": 0.4402, "step": 415}, {"epoch": 0.59, "learning_rate": 0.00018577286100002723, "loss": 0.4789, "step": 416}, {"epoch": 0.59, "learning_rate": 0.0001856938977417829, "loss": 0.4673, "step": 417}, {"epoch": 0.59, "learning_rate": 0.00018561473283751947, "loss": 0.4476, "step": 418}, {"epoch": 0.6, "learning_rate": 0.0001855353664735196, "loss": 0.4431, "step": 419}, {"epoch": 0.6, "learning_rate": 0.00018545579883654005, "loss": 0.4942, "step": 420}, {"epoch": 0.6, "learning_rate": 0.00018537603011381114, "loss": 0.4461, "step": 421}, {"epoch": 0.6, "learning_rate": 0.00018529606049303636, "loss": 0.4979, "step": 422}, {"epoch": 0.6, "learning_rate": 0.00018521589016239198, "loss": 0.463, "step": 423}, {"epoch": 0.6, "learning_rate": 0.00018513551931052653, "loss": 0.4645, "step": 424}, {"epoch": 0.6, "learning_rate": 0.00018505494812656034, "loss": 0.4075, "step": 425}, {"epoch": 0.61, "learning_rate": 0.00018497417680008525, "loss": 0.4329, "step": 426}, {"epoch": 0.61, "learning_rate": 0.000184893205521164, "loss": 0.4695, "step": 427}, {"epoch": 0.61, "learning_rate": 0.00018481203448032974, "loss": 0.4838, "step": 428}, {"epoch": 0.61, "learning_rate": 0.00018473066386858584, "loss": 0.4408, "step": 429}, {"epoch": 0.61, "learning_rate": 0.0001846490938774052, "loss": 0.4197, "step": 430}, {"epoch": 0.61, "learning_rate": 0.00018456732469872994, "loss": 0.439, "step": 431}, {"epoch": 0.61, "learning_rate": 0.00018448535652497073, "loss": 0.458, "step": 432}, {"epoch": 0.62, "learning_rate": 0.00018440318954900665, "loss": 0.4499, "step": 433}, {"epoch": 0.62, "learning_rate": 0.00018432082396418456, "loss": 0.4741, "step": 434}, {"epoch": 0.62, "learning_rate": 0.0001842382599643186, "loss": 0.4451, "step": 435}, {"epoch": 0.62, "learning_rate": 0.00018415549774368985, "loss": 0.4426, "step": 436}, {"epoch": 0.62, "learning_rate": 0.00018407253749704584, "loss": 0.4665, "step": 437}, {"epoch": 0.62, "learning_rate": 0.00018398937941959998, "loss": 0.4931, "step": 438}, {"epoch": 0.62, "learning_rate": 0.00018390602370703128, "loss": 0.4571, "step": 439}, {"epoch": 0.62, "learning_rate": 0.0001838224705554838, "loss": 0.4264, "step": 440}, {"epoch": 0.63, "learning_rate": 0.00018373872016156623, "loss": 0.4644, "step": 441}, {"epoch": 0.63, "learning_rate": 0.00018365477272235122, "loss": 0.4795, "step": 442}, {"epoch": 0.63, "learning_rate": 0.00018357062843537528, "loss": 0.4284, "step": 443}, {"epoch": 0.63, "learning_rate": 0.000183486287498638, "loss": 0.4793, "step": 444}, {"epoch": 0.63, "learning_rate": 0.00018340175011060183, "loss": 0.4722, "step": 445}, {"epoch": 0.63, "learning_rate": 0.00018331701647019132, "loss": 0.5024, "step": 446}, {"epoch": 0.63, "learning_rate": 0.00018323208677679297, "loss": 0.4455, "step": 447}, {"epoch": 0.64, "learning_rate": 0.00018314696123025454, "loss": 0.4505, "step": 448}, {"epoch": 0.64, "learning_rate": 0.00018306164003088464, "loss": 0.4523, "step": 449}, {"epoch": 0.64, "learning_rate": 0.0001829761233794523, "loss": 0.4436, "step": 450}, {"epoch": 0.64, "learning_rate": 0.0001828904114771865, "loss": 0.479, "step": 451}, {"epoch": 0.64, "learning_rate": 0.00018280450452577558, "loss": 0.4258, "step": 452}, {"epoch": 0.64, "learning_rate": 0.0001827184027273669, "loss": 0.5084, "step": 453}, {"epoch": 0.64, "learning_rate": 0.00018263210628456636, "loss": 0.3654, "step": 454}, {"epoch": 0.65, "learning_rate": 0.00018254561540043777, "loss": 0.4657, "step": 455}, {"epoch": 0.65, "learning_rate": 0.00018245893027850254, "loss": 0.4425, "step": 456}, {"epoch": 0.65, "learning_rate": 0.00018237205112273913, "loss": 0.4492, "step": 457}, {"epoch": 0.65, "learning_rate": 0.00018228497813758265, "loss": 0.4533, "step": 458}, {"epoch": 0.65, "learning_rate": 0.00018219771152792416, "loss": 0.4402, "step": 459}, {"epoch": 0.65, "learning_rate": 0.00018211025149911047, "loss": 0.493, "step": 460}, {"epoch": 0.65, "learning_rate": 0.00018202259825694347, "loss": 0.4115, "step": 461}, {"epoch": 0.66, "learning_rate": 0.00018193475200767968, "loss": 0.4405, "step": 462}, {"epoch": 0.66, "learning_rate": 0.00018184671295802987, "loss": 0.4409, "step": 463}, {"epoch": 0.66, "learning_rate": 0.00018175848131515837, "loss": 0.4532, "step": 464}, {"epoch": 0.66, "learning_rate": 0.0001816700572866828, "loss": 0.4806, "step": 465}, {"epoch": 0.66, "learning_rate": 0.0001815814410806734, "loss": 0.4715, "step": 466}, {"epoch": 0.66, "learning_rate": 0.0001814926329056527, "loss": 0.4594, "step": 467}, {"epoch": 0.66, "learning_rate": 0.00018140363297059487, "loss": 0.4643, "step": 468}, {"epoch": 0.67, "learning_rate": 0.00018131444148492535, "loss": 0.4859, "step": 469}, {"epoch": 0.67, "learning_rate": 0.0001812250586585204, "loss": 0.4558, "step": 470}, {"epoch": 0.67, "learning_rate": 0.0001811354847017064, "loss": 0.4371, "step": 471}, {"epoch": 0.67, "learning_rate": 0.0001810457198252595, "loss": 0.455, "step": 472}, {"epoch": 0.67, "learning_rate": 0.00018095576424040512, "loss": 0.4637, "step": 473}, {"epoch": 0.67, "learning_rate": 0.0001808656181588175, "loss": 0.4467, "step": 474}, {"epoch": 0.67, "learning_rate": 0.00018077528179261904, "loss": 0.4424, "step": 475}, {"epoch": 0.68, "learning_rate": 0.00018068475535437995, "loss": 0.4285, "step": 476}, {"epoch": 0.68, "learning_rate": 0.00018059403905711766, "loss": 0.4268, "step": 477}, {"epoch": 0.68, "learning_rate": 0.00018050313311429638, "loss": 0.4629, "step": 478}, {"epoch": 0.68, "learning_rate": 0.00018041203773982658, "loss": 0.4742, "step": 479}, {"epoch": 0.68, "learning_rate": 0.00018032075314806448, "loss": 0.4159, "step": 480}, {"epoch": 0.68, "learning_rate": 0.0001802292795538116, "loss": 0.4704, "step": 481}, {"epoch": 0.68, "learning_rate": 0.00018013761717231404, "loss": 0.4822, "step": 482}, {"epoch": 0.69, "learning_rate": 0.0001800457662192623, "loss": 0.507, "step": 483}, {"epoch": 0.69, "learning_rate": 0.00017995372691079052, "loss": 0.4542, "step": 484}, {"epoch": 0.69, "learning_rate": 0.0001798614994634761, "loss": 0.4403, "step": 485}, {"epoch": 0.69, "learning_rate": 0.00017976908409433914, "loss": 0.4284, "step": 486}, {"epoch": 0.69, "learning_rate": 0.0001796764810208419, "loss": 0.447, "step": 487}, {"epoch": 0.69, "learning_rate": 0.00017958369046088837, "loss": 0.4431, "step": 488}, {"epoch": 0.69, "learning_rate": 0.00017949071263282371, "loss": 0.4354, "step": 489}, {"epoch": 0.7, "learning_rate": 0.0001793975477554337, "loss": 0.4625, "step": 490}, {"epoch": 0.7, "learning_rate": 0.00017930419604794437, "loss": 0.4768, "step": 491}, {"epoch": 0.7, "learning_rate": 0.00017921065773002126, "loss": 0.4491, "step": 492}, {"epoch": 0.7, "learning_rate": 0.00017911693302176903, "loss": 0.4644, "step": 493}, {"epoch": 0.7, "learning_rate": 0.00017902302214373102, "loss": 0.4529, "step": 494}, {"epoch": 0.7, "learning_rate": 0.00017892892531688856, "loss": 0.4509, "step": 495}, {"epoch": 0.7, "learning_rate": 0.00017883464276266064, "loss": 0.4286, "step": 496}, {"epoch": 0.71, "learning_rate": 0.00017874017470290317, "loss": 0.4048, "step": 497}, {"epoch": 0.71, "learning_rate": 0.00017864552135990857, "loss": 0.4381, "step": 498}, {"epoch": 0.71, "learning_rate": 0.0001785506829564054, "loss": 0.4189, "step": 499}, {"epoch": 0.71, "learning_rate": 0.00017845565971555754, "loss": 0.4501, "step": 500}, {"epoch": 0.71, "learning_rate": 0.00017836045186096384, "loss": 0.4677, "step": 501}, {"epoch": 0.71, "learning_rate": 0.00017826505961665757, "loss": 0.4617, "step": 502}, {"epoch": 0.71, "learning_rate": 0.00017816948320710597, "loss": 0.4449, "step": 503}, {"epoch": 0.72, "learning_rate": 0.00017807372285720945, "loss": 0.4813, "step": 504}, {"epoch": 0.72, "learning_rate": 0.00017797777879230146, "loss": 0.44, "step": 505}, {"epoch": 0.72, "learning_rate": 0.0001778816512381476, "loss": 0.4505, "step": 506}, {"epoch": 0.72, "learning_rate": 0.00017778534042094533, "loss": 0.4302, "step": 507}, {"epoch": 0.72, "learning_rate": 0.00017768884656732325, "loss": 0.4325, "step": 508}, {"epoch": 0.72, "learning_rate": 0.00017759216990434078, "loss": 0.4797, "step": 509}, {"epoch": 0.72, "learning_rate": 0.0001774953106594874, "loss": 0.4742, "step": 510}, {"epoch": 0.73, "learning_rate": 0.00017739826906068231, "loss": 0.4571, "step": 511}, {"epoch": 0.73, "learning_rate": 0.0001773010453362737, "loss": 0.4343, "step": 512}, {"epoch": 0.73, "learning_rate": 0.00017720363971503847, "loss": 0.4415, "step": 513}, {"epoch": 0.73, "learning_rate": 0.00017710605242618138, "loss": 0.4113, "step": 514}, {"epoch": 0.73, "learning_rate": 0.0001770082836993348, "loss": 0.4455, "step": 515}, {"epoch": 0.73, "learning_rate": 0.000176910333764558, "loss": 0.4252, "step": 516}, {"epoch": 0.73, "learning_rate": 0.00017681220285233656, "loss": 0.3949, "step": 517}, {"epoch": 0.74, "learning_rate": 0.00017671389119358204, "loss": 0.4582, "step": 518}, {"epoch": 0.74, "learning_rate": 0.0001766153990196313, "loss": 0.454, "step": 519}, {"epoch": 0.74, "learning_rate": 0.0001765167265622459, "loss": 0.4582, "step": 520}, {"epoch": 0.74, "learning_rate": 0.0001764178740536117, "loss": 0.4177, "step": 521}, {"epoch": 0.74, "learning_rate": 0.0001763188417263381, "loss": 0.4639, "step": 522}, {"epoch": 0.74, "learning_rate": 0.0001762196298134579, "loss": 0.4606, "step": 523}, {"epoch": 0.74, "learning_rate": 0.0001761202385484262, "loss": 0.5176, "step": 524}, {"epoch": 0.75, "learning_rate": 0.00017602066816512025, "loss": 0.4649, "step": 525}, {"epoch": 0.75, "learning_rate": 0.00017592091889783882, "loss": 0.4851, "step": 526}, {"epoch": 0.75, "learning_rate": 0.00017582099098130153, "loss": 0.4828, "step": 527}, {"epoch": 0.75, "learning_rate": 0.00017572088465064848, "loss": 0.4342, "step": 528}, {"epoch": 0.75, "learning_rate": 0.00017562060014143945, "loss": 0.4098, "step": 529}, {"epoch": 0.75, "learning_rate": 0.00017552013768965368, "loss": 0.4463, "step": 530}, {"epoch": 0.75, "learning_rate": 0.00017541949753168893, "loss": 0.4211, "step": 531}, {"epoch": 0.76, "learning_rate": 0.00017531867990436126, "loss": 0.4405, "step": 532}, {"epoch": 0.76, "learning_rate": 0.00017521768504490427, "loss": 0.4318, "step": 533}, {"epoch": 0.76, "learning_rate": 0.00017511651319096868, "loss": 0.4462, "step": 534}, {"epoch": 0.76, "learning_rate": 0.0001750151645806215, "loss": 0.5177, "step": 535}, {"epoch": 0.76, "learning_rate": 0.00017491363945234593, "loss": 0.4321, "step": 536}, {"epoch": 0.76, "learning_rate": 0.00017481193804504036, "loss": 0.3963, "step": 537}, {"epoch": 0.76, "learning_rate": 0.00017471006059801802, "loss": 0.4681, "step": 538}, {"epoch": 0.77, "learning_rate": 0.0001746080073510064, "loss": 0.4857, "step": 539}, {"epoch": 0.77, "learning_rate": 0.00017450577854414662, "loss": 0.4452, "step": 540}, {"epoch": 0.77, "learning_rate": 0.00017440337441799292, "loss": 0.4741, "step": 541}, {"epoch": 0.77, "learning_rate": 0.00017430079521351218, "loss": 0.4632, "step": 542}, {"epoch": 0.77, "learning_rate": 0.0001741980411720831, "loss": 0.439, "step": 543}, {"epoch": 0.77, "learning_rate": 0.00017409511253549593, "loss": 0.4582, "step": 544}, {"epoch": 0.77, "learning_rate": 0.00017399200954595163, "loss": 0.4306, "step": 545}, {"epoch": 0.78, "learning_rate": 0.0001738887324460615, "loss": 0.5483, "step": 546}, {"epoch": 0.78, "learning_rate": 0.0001737852814788466, "loss": 0.4887, "step": 547}, {"epoch": 0.78, "learning_rate": 0.000173681656887737, "loss": 0.4178, "step": 548}, {"epoch": 0.78, "learning_rate": 0.00017357785891657137, "loss": 0.4463, "step": 549}, {"epoch": 0.78, "learning_rate": 0.00017347388780959637, "loss": 0.466, "step": 550}, {"epoch": 0.78, "learning_rate": 0.00017336974381146605, "loss": 0.4817, "step": 551}, {"epoch": 0.78, "learning_rate": 0.00017326542716724128, "loss": 0.4384, "step": 552}, {"epoch": 0.79, "learning_rate": 0.00017316093812238926, "loss": 0.4909, "step": 553}, {"epoch": 0.79, "learning_rate": 0.00017305627692278276, "loss": 0.4894, "step": 554}, {"epoch": 0.79, "learning_rate": 0.0001729514438146997, "loss": 0.4007, "step": 555}, {"epoch": 0.79, "learning_rate": 0.00017284643904482252, "loss": 0.4482, "step": 556}, {"epoch": 0.79, "learning_rate": 0.00017274126286023758, "loss": 0.4351, "step": 557}, {"epoch": 0.79, "learning_rate": 0.0001726359155084346, "loss": 0.4254, "step": 558}, {"epoch": 0.79, "learning_rate": 0.0001725303972373061, "loss": 0.4956, "step": 559}, {"epoch": 0.8, "learning_rate": 0.00017242470829514672, "loss": 0.4096, "step": 560}, {"epoch": 0.8, "learning_rate": 0.00017231884893065274, "loss": 0.4895, "step": 561}, {"epoch": 0.8, "learning_rate": 0.00017221281939292155, "loss": 0.4549, "step": 562}, {"epoch": 0.8, "learning_rate": 0.00017210661993145081, "loss": 0.4387, "step": 563}, {"epoch": 0.8, "learning_rate": 0.00017200025079613818, "loss": 0.4346, "step": 564}, {"epoch": 0.8, "learning_rate": 0.00017189371223728047, "loss": 0.4233, "step": 565}, {"epoch": 0.8, "learning_rate": 0.00017178700450557317, "loss": 0.4277, "step": 566}, {"epoch": 0.81, "learning_rate": 0.00017168012785210996, "loss": 0.4231, "step": 567}, {"epoch": 0.81, "learning_rate": 0.00017157308252838187, "loss": 0.462, "step": 568}, {"epoch": 0.81, "learning_rate": 0.0001714658687862769, "loss": 0.4627, "step": 569}, {"epoch": 0.81, "learning_rate": 0.00017135848687807937, "loss": 0.4287, "step": 570}, {"epoch": 0.81, "learning_rate": 0.00017125093705646925, "loss": 0.4558, "step": 571}, {"epoch": 0.81, "learning_rate": 0.00017114321957452163, "loss": 0.454, "step": 572}, {"epoch": 0.81, "learning_rate": 0.00017103533468570625, "loss": 0.4784, "step": 573}, {"epoch": 0.82, "learning_rate": 0.00017092728264388657, "loss": 0.4383, "step": 574}, {"epoch": 0.82, "learning_rate": 0.00017081906370331956, "loss": 0.4056, "step": 575}, {"epoch": 0.82, "learning_rate": 0.00017071067811865476, "loss": 0.4805, "step": 576}, {"epoch": 0.82, "learning_rate": 0.000170602126144934, "loss": 0.4377, "step": 577}, {"epoch": 0.82, "learning_rate": 0.0001704934080375905, "loss": 0.46, "step": 578}, {"epoch": 0.82, "learning_rate": 0.0001703845240524485, "loss": 0.4276, "step": 579}, {"epoch": 0.82, "learning_rate": 0.00017027547444572254, "loss": 0.4166, "step": 580}, {"epoch": 0.83, "learning_rate": 0.00017016625947401684, "loss": 0.4613, "step": 581}, {"epoch": 0.83, "learning_rate": 0.00017005687939432486, "loss": 0.3957, "step": 582}, {"epoch": 0.83, "learning_rate": 0.00016994733446402838, "loss": 0.3944, "step": 583}, {"epoch": 0.83, "learning_rate": 0.0001698376249408973, "loss": 0.4443, "step": 584}, {"epoch": 0.83, "learning_rate": 0.00016972775108308868, "loss": 0.4439, "step": 585}, {"epoch": 0.83, "learning_rate": 0.00016961771314914632, "loss": 0.4919, "step": 586}, {"epoch": 0.83, "learning_rate": 0.0001695075113980001, "loss": 0.4287, "step": 587}, {"epoch": 0.84, "learning_rate": 0.0001693971460889654, "loss": 0.5257, "step": 588}, {"epoch": 0.84, "learning_rate": 0.0001692866174817425, "loss": 0.4454, "step": 589}, {"epoch": 0.84, "learning_rate": 0.00016917592583641578, "loss": 0.465, "step": 590}, {"epoch": 0.84, "learning_rate": 0.0001690650714134535, "loss": 0.4698, "step": 591}, {"epoch": 0.84, "learning_rate": 0.0001689540544737067, "loss": 0.4295, "step": 592}, {"epoch": 0.84, "learning_rate": 0.00016884287527840907, "loss": 0.429, "step": 593}, {"epoch": 0.84, "learning_rate": 0.00016873153408917592, "loss": 0.4479, "step": 594}, {"epoch": 0.85, "learning_rate": 0.00016862003116800386, "loss": 0.4298, "step": 595}, {"epoch": 0.85, "learning_rate": 0.00016850836677727003, "loss": 0.4071, "step": 596}, {"epoch": 0.85, "learning_rate": 0.00016839654117973155, "loss": 0.3825, "step": 597}, {"epoch": 0.85, "learning_rate": 0.00016828455463852482, "loss": 0.4215, "step": 598}, {"epoch": 0.85, "learning_rate": 0.000168172407417165, "loss": 0.4908, "step": 599}, {"epoch": 0.85, "learning_rate": 0.00016806009977954532, "loss": 0.4401, "step": 600}, {"epoch": 0.85, "learning_rate": 0.0001679476319899365, "loss": 0.4489, "step": 601}, {"epoch": 0.86, "learning_rate": 0.00016783500431298616, "loss": 0.4765, "step": 602}, {"epoch": 0.86, "learning_rate": 0.00016772221701371804, "loss": 0.4356, "step": 603}, {"epoch": 0.86, "learning_rate": 0.0001676092703575316, "loss": 0.477, "step": 604}, {"epoch": 0.86, "learning_rate": 0.0001674961646102012, "loss": 0.4364, "step": 605}, {"epoch": 0.86, "learning_rate": 0.00016738290003787563, "loss": 0.3916, "step": 606}, {"epoch": 0.86, "learning_rate": 0.0001672694769070773, "loss": 0.4597, "step": 607}, {"epoch": 0.86, "learning_rate": 0.00016715589548470185, "loss": 0.4425, "step": 608}, {"epoch": 0.87, "learning_rate": 0.0001670421560380173, "loss": 0.4282, "step": 609}, {"epoch": 0.87, "learning_rate": 0.0001669282588346636, "loss": 0.464, "step": 610}, {"epoch": 0.87, "learning_rate": 0.00016681420414265189, "loss": 0.4171, "step": 611}, {"epoch": 0.87, "learning_rate": 0.00016669999223036376, "loss": 0.4492, "step": 612}, {"epoch": 0.87, "learning_rate": 0.000166585623366551, "loss": 0.425, "step": 613}, {"epoch": 0.87, "learning_rate": 0.0001664710978203345, "loss": 0.3864, "step": 614}, {"epoch": 0.87, "learning_rate": 0.000166356415861204, "loss": 0.4664, "step": 615}, {"epoch": 0.88, "learning_rate": 0.0001662415777590172, "loss": 0.4473, "step": 616}, {"epoch": 0.88, "learning_rate": 0.00016612658378399922, "loss": 0.4797, "step": 617}, {"epoch": 0.88, "learning_rate": 0.00016601143420674205, "loss": 0.4619, "step": 618}, {"epoch": 0.88, "learning_rate": 0.00016589612929820375, "loss": 0.4391, "step": 619}, {"epoch": 0.88, "learning_rate": 0.00016578066932970787, "loss": 0.4766, "step": 620}, {"epoch": 0.88, "learning_rate": 0.00016566505457294293, "loss": 0.4503, "step": 621}, {"epoch": 0.88, "learning_rate": 0.00016554928529996158, "loss": 0.4594, "step": 622}, {"epoch": 0.88, "learning_rate": 0.0001654333617831801, "loss": 0.4332, "step": 623}, {"epoch": 0.89, "learning_rate": 0.00016531728429537766, "loss": 0.4636, "step": 624}, {"epoch": 0.89, "learning_rate": 0.00016520105310969597, "loss": 0.4079, "step": 625}, {"epoch": 0.89, "learning_rate": 0.0001650846684996381, "loss": 0.4276, "step": 626}, {"epoch": 0.89, "learning_rate": 0.00016496813073906834, "loss": 0.4673, "step": 627}, {"epoch": 0.89, "learning_rate": 0.00016485144010221125, "loss": 0.3915, "step": 628}, {"epoch": 0.89, "learning_rate": 0.0001647345968636512, "loss": 0.4426, "step": 629}, {"epoch": 0.89, "learning_rate": 0.00016461760129833164, "loss": 0.4439, "step": 630}, {"epoch": 0.9, "learning_rate": 0.00016450045368155442, "loss": 0.4521, "step": 631}, {"epoch": 0.9, "learning_rate": 0.00016438315428897915, "loss": 0.3904, "step": 632}, {"epoch": 0.9, "learning_rate": 0.0001642657033966227, "loss": 0.433, "step": 633}, {"epoch": 0.9, "learning_rate": 0.00016414810128085835, "loss": 0.4739, "step": 634}, {"epoch": 0.9, "learning_rate": 0.00016403034821841516, "loss": 0.4387, "step": 635}, {"epoch": 0.9, "learning_rate": 0.00016391244448637758, "loss": 0.4523, "step": 636}, {"epoch": 0.9, "learning_rate": 0.00016379439036218443, "loss": 0.4365, "step": 637}, {"epoch": 0.91, "learning_rate": 0.00016367618612362841, "loss": 0.428, "step": 638}, {"epoch": 0.91, "learning_rate": 0.00016355783204885564, "loss": 0.4267, "step": 639}, {"epoch": 0.91, "learning_rate": 0.00016343932841636456, "loss": 0.4077, "step": 640}, {"epoch": 0.91, "learning_rate": 0.00016332067550500572, "loss": 0.4975, "step": 641}, {"epoch": 0.91, "learning_rate": 0.00016320187359398093, "loss": 0.4763, "step": 642}, {"epoch": 0.91, "learning_rate": 0.00016308292296284246, "loss": 0.4626, "step": 643}, {"epoch": 0.91, "learning_rate": 0.00016296382389149273, "loss": 0.4687, "step": 644}, {"epoch": 0.92, "learning_rate": 0.0001628445766601833, "loss": 0.4464, "step": 645}, {"epoch": 0.92, "learning_rate": 0.00016272518154951442, "loss": 0.441, "step": 646}, {"epoch": 0.92, "learning_rate": 0.00016260563884043435, "loss": 0.4616, "step": 647}, {"epoch": 0.92, "learning_rate": 0.00016248594881423863, "loss": 0.4154, "step": 648}, {"epoch": 0.92, "learning_rate": 0.0001623661117525695, "loss": 0.4394, "step": 649}, {"epoch": 0.92, "learning_rate": 0.000162246127937415, "loss": 0.4268, "step": 650}, {"epoch": 0.92, "learning_rate": 0.00016212599765110878, "loss": 0.4666, "step": 651}, {"epoch": 0.93, "learning_rate": 0.00016200572117632892, "loss": 0.5111, "step": 652}, {"epoch": 0.93, "learning_rate": 0.00016188529879609763, "loss": 0.4185, "step": 653}, {"epoch": 0.93, "learning_rate": 0.0001617647307937804, "loss": 0.4591, "step": 654}, {"epoch": 0.93, "learning_rate": 0.00016164401745308538, "loss": 0.4424, "step": 655}, {"epoch": 0.93, "learning_rate": 0.00016152315905806268, "loss": 0.4703, "step": 656}, {"epoch": 0.93, "learning_rate": 0.00016140215589310387, "loss": 0.4362, "step": 657}, {"epoch": 0.93, "learning_rate": 0.00016128100824294096, "loss": 0.4535, "step": 658}, {"epoch": 0.94, "learning_rate": 0.0001611597163926462, "loss": 0.3978, "step": 659}, {"epoch": 0.94, "learning_rate": 0.00016103828062763095, "loss": 0.4227, "step": 660}, {"epoch": 0.94, "learning_rate": 0.00016091670123364533, "loss": 0.4205, "step": 661}, {"epoch": 0.94, "learning_rate": 0.00016079497849677738, "loss": 0.4922, "step": 662}, {"epoch": 0.94, "learning_rate": 0.00016067311270345246, "loss": 0.495, "step": 663}, {"epoch": 0.94, "learning_rate": 0.00016055110414043257, "loss": 0.4208, "step": 664}, {"epoch": 0.94, "learning_rate": 0.00016042895309481564, "loss": 0.4792, "step": 665}, {"epoch": 0.95, "learning_rate": 0.00016030665985403484, "loss": 0.4339, "step": 666}, {"epoch": 0.95, "learning_rate": 0.00016018422470585802, "loss": 0.4481, "step": 667}, {"epoch": 0.95, "learning_rate": 0.00016006164793838692, "loss": 0.421, "step": 668}, {"epoch": 0.95, "learning_rate": 0.00015993892984005647, "loss": 0.4393, "step": 669}, {"epoch": 0.95, "learning_rate": 0.00015981607069963423, "loss": 0.4285, "step": 670}, {"epoch": 0.95, "learning_rate": 0.00015969307080621964, "loss": 0.4056, "step": 671}, {"epoch": 0.95, "learning_rate": 0.00015956993044924334, "loss": 0.4445, "step": 672}, {"epoch": 0.96, "learning_rate": 0.00015944664991846645, "loss": 0.4333, "step": 673}, {"epoch": 0.96, "learning_rate": 0.00015932322950397998, "loss": 0.433, "step": 674}, {"epoch": 0.96, "learning_rate": 0.0001591996694962041, "loss": 0.4198, "step": 675}, {"epoch": 0.96, "learning_rate": 0.00015907597018588744, "loss": 0.4408, "step": 676}, {"epoch": 0.96, "learning_rate": 0.0001589521318641064, "loss": 0.4394, "step": 677}, {"epoch": 0.96, "learning_rate": 0.00015882815482226454, "loss": 0.4289, "step": 678}, {"epoch": 0.96, "learning_rate": 0.0001587040393520918, "loss": 0.4423, "step": 679}, {"epoch": 0.97, "learning_rate": 0.0001585797857456439, "loss": 0.389, "step": 680}, {"epoch": 0.97, "learning_rate": 0.00015845539429530154, "loss": 0.4046, "step": 681}, {"epoch": 0.97, "learning_rate": 0.00015833086529376983, "loss": 0.4582, "step": 682}, {"epoch": 0.97, "learning_rate": 0.00015820619903407756, "loss": 0.4417, "step": 683}, {"epoch": 0.97, "learning_rate": 0.00015808139580957646, "loss": 0.4271, "step": 684}, {"epoch": 0.97, "learning_rate": 0.00015795645591394058, "loss": 0.4616, "step": 685}, {"epoch": 0.97, "learning_rate": 0.00015783137964116558, "loss": 0.4429, "step": 686}, {"epoch": 0.98, "learning_rate": 0.00015770616728556796, "loss": 0.4487, "step": 687}, {"epoch": 0.98, "learning_rate": 0.00015758081914178456, "loss": 0.398, "step": 688}, {"epoch": 0.98, "learning_rate": 0.0001574553355047716, "loss": 0.4445, "step": 689}, {"epoch": 0.98, "learning_rate": 0.00015732971666980423, "loss": 0.4666, "step": 690}, {"epoch": 0.98, "learning_rate": 0.0001572039629324757, "loss": 0.4086, "step": 691}, {"epoch": 0.98, "learning_rate": 0.00015707807458869674, "loss": 0.4649, "step": 692}, {"epoch": 0.98, "learning_rate": 0.00015695205193469474, "loss": 0.6108, "step": 693}, {"epoch": 0.99, "learning_rate": 0.00015682589526701314, "loss": 0.4175, "step": 694}, {"epoch": 0.99, "learning_rate": 0.00015669960488251089, "loss": 0.4439, "step": 695}, {"epoch": 0.99, "learning_rate": 0.0001565731810783613, "loss": 0.4699, "step": 696}, {"epoch": 0.99, "learning_rate": 0.00015644662415205195, "loss": 0.3735, "step": 697}, {"epoch": 0.99, "learning_rate": 0.00015631993440138342, "loss": 0.4114, "step": 698}, {"epoch": 0.99, "learning_rate": 0.00015619311212446894, "loss": 0.4655, "step": 699}, {"epoch": 0.99, "learning_rate": 0.00015606615761973362, "loss": 0.4351, "step": 700}, {"epoch": 1.0, "learning_rate": 0.00015593907118591362, "loss": 0.4509, "step": 701}, {"epoch": 1.0, "learning_rate": 0.00015581185312205561, "loss": 0.4406, "step": 702}, {"epoch": 1.0, "learning_rate": 0.000155684503727516, "loss": 0.4414, "step": 703}, {"epoch": 1.0, "learning_rate": 0.00015555702330196023, "loss": 0.434, "step": 704}, {"epoch": 1.0, "learning_rate": 0.000155429412145362, "loss": 0.4325, "step": 705}, {"epoch": 1.0, "learning_rate": 0.00015530167055800278, "loss": 0.4252, "step": 706}, {"epoch": 1.0, "learning_rate": 0.00015517379884047076, "loss": 0.3996, "step": 707}, {"epoch": 1.01, "learning_rate": 0.00015504579729366049, "loss": 0.3998, "step": 708}, {"epoch": 1.01, "learning_rate": 0.00015491766621877198, "loss": 0.4178, "step": 709}, {"epoch": 1.01, "learning_rate": 0.00015478940591731, "loss": 0.4122, "step": 710}, {"epoch": 1.01, "learning_rate": 0.0001546610166910835, "loss": 0.4285, "step": 711}, {"epoch": 1.01, "learning_rate": 0.00015453249884220464, "loss": 0.3736, "step": 712}, {"epoch": 1.01, "learning_rate": 0.0001544038526730884, "loss": 0.3948, "step": 713}, {"epoch": 1.01, "learning_rate": 0.0001542750784864516, "loss": 0.423, "step": 714}, {"epoch": 1.02, "learning_rate": 0.00015414617658531237, "loss": 0.4592, "step": 715}, {"epoch": 1.02, "learning_rate": 0.0001540171472729893, "loss": 0.4262, "step": 716}, {"epoch": 1.02, "learning_rate": 0.00015388799085310083, "loss": 0.4042, "step": 717}, {"epoch": 1.02, "learning_rate": 0.00015375870762956458, "loss": 0.397, "step": 718}, {"epoch": 1.02, "learning_rate": 0.00015362929790659633, "loss": 0.3892, "step": 719}, {"epoch": 1.02, "learning_rate": 0.00015349976198870973, "loss": 0.4161, "step": 720}, {"epoch": 1.02, "learning_rate": 0.0001533701001807153, "loss": 0.4406, "step": 721}, {"epoch": 1.03, "learning_rate": 0.00015324031278771981, "loss": 0.4453, "step": 722}, {"epoch": 1.03, "learning_rate": 0.00015311040011512552, "loss": 0.4193, "step": 723}, {"epoch": 1.03, "learning_rate": 0.0001529803624686295, "loss": 0.4083, "step": 724}, {"epoch": 1.03, "learning_rate": 0.00015285020015422287, "loss": 0.3725, "step": 725}, {"epoch": 1.03, "learning_rate": 0.00015271991347819014, "loss": 0.3998, "step": 726}, {"epoch": 1.03, "learning_rate": 0.00015258950274710847, "loss": 0.4367, "step": 727}, {"epoch": 1.03, "learning_rate": 0.00015245896826784688, "loss": 0.399, "step": 728}, {"epoch": 1.04, "learning_rate": 0.00015232831034756565, "loss": 0.4117, "step": 729}, {"epoch": 1.04, "learning_rate": 0.00015219752929371546, "loss": 0.4264, "step": 730}, {"epoch": 1.04, "learning_rate": 0.00015206662541403674, "loss": 0.3732, "step": 731}, {"epoch": 1.04, "learning_rate": 0.00015193559901655897, "loss": 0.3802, "step": 732}, {"epoch": 1.04, "learning_rate": 0.00015180445040959993, "loss": 0.4598, "step": 733}, {"epoch": 1.04, "learning_rate": 0.000151673179901765, "loss": 0.4048, "step": 734}, {"epoch": 1.04, "learning_rate": 0.0001515417878019463, "loss": 0.4352, "step": 735}, {"epoch": 1.05, "learning_rate": 0.00015141027441932216, "loss": 0.3924, "step": 736}, {"epoch": 1.05, "learning_rate": 0.0001512786400633563, "loss": 0.4068, "step": 737}, {"epoch": 1.05, "learning_rate": 0.00015114688504379707, "loss": 0.4051, "step": 738}, {"epoch": 1.05, "learning_rate": 0.00015101500967067667, "loss": 0.4605, "step": 739}, {"epoch": 1.05, "learning_rate": 0.00015088301425431072, "loss": 0.4516, "step": 740}, {"epoch": 1.05, "learning_rate": 0.00015075089910529708, "loss": 0.4011, "step": 741}, {"epoch": 1.05, "learning_rate": 0.00015061866453451556, "loss": 0.4353, "step": 742}, {"epoch": 1.06, "learning_rate": 0.00015048631085312674, "loss": 0.4364, "step": 743}, {"epoch": 1.06, "learning_rate": 0.00015035383837257177, "loss": 0.408, "step": 744}, {"epoch": 1.06, "learning_rate": 0.00015022124740457108, "loss": 0.4459, "step": 745}, {"epoch": 1.06, "learning_rate": 0.0001500885382611241, "loss": 0.4384, "step": 746}, {"epoch": 1.06, "learning_rate": 0.0001499557112545082, "loss": 0.4372, "step": 747}, {"epoch": 1.06, "learning_rate": 0.0001498227666972782, "loss": 0.4279, "step": 748}, {"epoch": 1.06, "learning_rate": 0.00014968970490226546, "loss": 0.4485, "step": 749}, {"epoch": 1.07, "learning_rate": 0.00014955652618257726, "loss": 0.3987, "step": 750}, {"epoch": 1.07, "learning_rate": 0.00014942323085159599, "loss": 0.4479, "step": 751}, {"epoch": 1.07, "learning_rate": 0.00014928981922297842, "loss": 0.4484, "step": 752}, {"epoch": 1.07, "learning_rate": 0.000149156291610655, "loss": 0.4383, "step": 753}, {"epoch": 1.07, "learning_rate": 0.0001490226483288291, "loss": 0.4382, "step": 754}, {"epoch": 1.07, "learning_rate": 0.00014888888969197633, "loss": 0.3951, "step": 755}, {"epoch": 1.07, "learning_rate": 0.0001487550160148436, "loss": 0.4319, "step": 756}, {"epoch": 1.08, "learning_rate": 0.00014862102761244866, "loss": 0.4292, "step": 757}, {"epoch": 1.08, "learning_rate": 0.00014848692480007913, "loss": 0.3879, "step": 758}, {"epoch": 1.08, "learning_rate": 0.00014835270789329187, "loss": 0.4003, "step": 759}, {"epoch": 1.08, "learning_rate": 0.0001482183772079123, "loss": 0.4049, "step": 760}, {"epoch": 1.08, "learning_rate": 0.0001480839330600334, "loss": 0.4168, "step": 761}, {"epoch": 1.08, "learning_rate": 0.0001479493757660153, "loss": 0.446, "step": 762}, {"epoch": 1.08, "learning_rate": 0.00014781470564248432, "loss": 0.443, "step": 763}, {"epoch": 1.09, "learning_rate": 0.00014767992300633223, "loss": 0.4157, "step": 764}, {"epoch": 1.09, "learning_rate": 0.00014754502817471558, "loss": 0.4392, "step": 765}, {"epoch": 1.09, "learning_rate": 0.000147410021465055, "loss": 0.42, "step": 766}, {"epoch": 1.09, "learning_rate": 0.0001472749031950343, "loss": 0.3767, "step": 767}, {"epoch": 1.09, "learning_rate": 0.0001471396736825998, "loss": 0.4104, "step": 768}, {"epoch": 1.09, "learning_rate": 0.00014700433324595956, "loss": 0.4187, "step": 769}, {"epoch": 1.09, "learning_rate": 0.00014686888220358281, "loss": 0.4336, "step": 770}, {"epoch": 1.1, "learning_rate": 0.00014673332087419887, "loss": 0.426, "step": 771}, {"epoch": 1.1, "learning_rate": 0.00014659764957679661, "loss": 0.4437, "step": 772}, {"epoch": 1.1, "learning_rate": 0.0001464618686306238, "loss": 0.3988, "step": 773}, {"epoch": 1.1, "learning_rate": 0.00014632597835518603, "loss": 0.4154, "step": 774}, {"epoch": 1.1, "learning_rate": 0.0001461899790702463, "loss": 0.4045, "step": 775}, {"epoch": 1.1, "learning_rate": 0.000146053871095824, "loss": 0.39, "step": 776}, {"epoch": 1.1, "learning_rate": 0.0001459176547521944, "loss": 0.3987, "step": 777}, {"epoch": 1.11, "learning_rate": 0.00014578133035988774, "loss": 0.5726, "step": 778}, {"epoch": 1.11, "learning_rate": 0.0001456448982396884, "loss": 0.4045, "step": 779}, {"epoch": 1.11, "learning_rate": 0.0001455083587126344, "loss": 0.4001, "step": 780}, {"epoch": 1.11, "learning_rate": 0.0001453717121000164, "loss": 0.4144, "step": 781}, {"epoch": 1.11, "learning_rate": 0.0001452349587233771, "loss": 0.4408, "step": 782}, {"epoch": 1.11, "learning_rate": 0.0001450980989045104, "loss": 0.3858, "step": 783}, {"epoch": 1.11, "learning_rate": 0.00014496113296546067, "loss": 0.3967, "step": 784}, {"epoch": 1.12, "learning_rate": 0.000144824061228522, "loss": 0.391, "step": 785}, {"epoch": 1.12, "learning_rate": 0.00014468688401623745, "loss": 0.4353, "step": 786}, {"epoch": 1.12, "learning_rate": 0.00014454960165139816, "loss": 0.3972, "step": 787}, {"epoch": 1.12, "learning_rate": 0.00014441221445704293, "loss": 0.4304, "step": 788}, {"epoch": 1.12, "learning_rate": 0.00014427472275645701, "loss": 0.4106, "step": 789}, {"epoch": 1.12, "learning_rate": 0.00014413712687317166, "loss": 0.432, "step": 790}, {"epoch": 1.12, "learning_rate": 0.00014399942713096332, "loss": 0.389, "step": 791}, {"epoch": 1.12, "learning_rate": 0.00014386162385385278, "loss": 0.4027, "step": 792}, {"epoch": 1.13, "learning_rate": 0.00014372371736610443, "loss": 0.4072, "step": 793}, {"epoch": 1.13, "learning_rate": 0.00014358570799222556, "loss": 0.4049, "step": 794}, {"epoch": 1.13, "learning_rate": 0.00014344759605696558, "loss": 0.4175, "step": 795}, {"epoch": 1.13, "learning_rate": 0.0001433093818853152, "loss": 0.4245, "step": 796}, {"epoch": 1.13, "learning_rate": 0.00014317106580250574, "loss": 0.4201, "step": 797}, {"epoch": 1.13, "learning_rate": 0.00014303264813400826, "loss": 0.3911, "step": 798}, {"epoch": 1.13, "learning_rate": 0.00014289412920553296, "loss": 0.4039, "step": 799}, {"epoch": 1.14, "learning_rate": 0.00014275550934302823, "loss": 0.4192, "step": 800}, {"epoch": 1.14, "learning_rate": 0.00014261678887267997, "loss": 0.4243, "step": 801}, {"epoch": 1.14, "learning_rate": 0.00014247796812091087, "loss": 0.3688, "step": 802}, {"epoch": 1.14, "learning_rate": 0.0001423390474143796, "loss": 0.4015, "step": 803}, {"epoch": 1.14, "learning_rate": 0.00014220002707997998, "loss": 0.4013, "step": 804}, {"epoch": 1.14, "learning_rate": 0.00014206090744484025, "loss": 0.4671, "step": 805}, {"epoch": 1.14, "learning_rate": 0.00014192168883632239, "loss": 0.4071, "step": 806}, {"epoch": 1.15, "learning_rate": 0.00014178237158202122, "loss": 0.4279, "step": 807}, {"epoch": 1.15, "learning_rate": 0.00014164295600976374, "loss": 0.4615, "step": 808}, {"epoch": 1.15, "learning_rate": 0.0001415034424476082, "loss": 0.449, "step": 809}, {"epoch": 1.15, "learning_rate": 0.00014136383122384348, "loss": 0.4099, "step": 810}, {"epoch": 1.15, "learning_rate": 0.0001412241226669883, "loss": 0.3829, "step": 811}, {"epoch": 1.15, "learning_rate": 0.0001410843171057904, "loss": 0.4473, "step": 812}, {"epoch": 1.15, "learning_rate": 0.00014094441486922575, "loss": 0.4383, "step": 813}, {"epoch": 1.16, "learning_rate": 0.00014080441628649788, "loss": 0.3896, "step": 814}, {"epoch": 1.16, "learning_rate": 0.0001406643216870369, "loss": 0.4177, "step": 815}, {"epoch": 1.16, "learning_rate": 0.000140524131400499, "loss": 0.4231, "step": 816}, {"epoch": 1.16, "learning_rate": 0.00014038384575676543, "loss": 0.4516, "step": 817}, {"epoch": 1.16, "learning_rate": 0.00014024346508594186, "loss": 0.4104, "step": 818}, {"epoch": 1.16, "learning_rate": 0.00014010298971835758, "loss": 0.4053, "step": 819}, {"epoch": 1.16, "learning_rate": 0.0001399624199845647, "loss": 0.4291, "step": 820}, {"epoch": 1.17, "learning_rate": 0.00013982175621533738, "loss": 0.3989, "step": 821}, {"epoch": 1.17, "learning_rate": 0.00013968099874167103, "loss": 0.4068, "step": 822}, {"epoch": 1.17, "learning_rate": 0.00013954014789478164, "loss": 0.459, "step": 823}, {"epoch": 1.17, "learning_rate": 0.00013939920400610483, "loss": 0.359, "step": 824}, {"epoch": 1.17, "learning_rate": 0.00013925816740729515, "loss": 0.4649, "step": 825}, {"epoch": 1.17, "learning_rate": 0.0001391170384302254, "loss": 0.382, "step": 826}, {"epoch": 1.17, "learning_rate": 0.00013897581740698563, "loss": 0.4205, "step": 827}, {"epoch": 1.18, "learning_rate": 0.00013883450466988263, "loss": 0.4317, "step": 828}, {"epoch": 1.18, "learning_rate": 0.00013869310055143887, "loss": 0.4234, "step": 829}, {"epoch": 1.18, "learning_rate": 0.00013855160538439192, "loss": 0.4281, "step": 830}, {"epoch": 1.18, "learning_rate": 0.00013841001950169353, "loss": 0.3767, "step": 831}, {"epoch": 1.18, "learning_rate": 0.000138268343236509, "loss": 0.3827, "step": 832}, {"epoch": 1.18, "learning_rate": 0.00013812657692221624, "loss": 0.4484, "step": 833}, {"epoch": 1.18, "learning_rate": 0.00013798472089240514, "loss": 0.4008, "step": 834}, {"epoch": 1.19, "learning_rate": 0.00013784277548087656, "loss": 0.4308, "step": 835}, {"epoch": 1.19, "learning_rate": 0.00013770074102164182, "loss": 0.4361, "step": 836}, {"epoch": 1.19, "learning_rate": 0.00013755861784892174, "loss": 0.402, "step": 837}, {"epoch": 1.19, "learning_rate": 0.00013741640629714582, "loss": 0.4096, "step": 838}, {"epoch": 1.19, "learning_rate": 0.00013727410670095157, "loss": 0.3915, "step": 839}, {"epoch": 1.19, "learning_rate": 0.00013713171939518378, "loss": 0.3981, "step": 840}, {"epoch": 1.19, "learning_rate": 0.00013698924471489344, "loss": 0.4532, "step": 841}, {"epoch": 1.2, "learning_rate": 0.00013684668299533725, "loss": 0.3731, "step": 842}, {"epoch": 1.2, "learning_rate": 0.00013670403457197674, "loss": 0.4293, "step": 843}, {"epoch": 1.2, "learning_rate": 0.0001365612997804774, "loss": 0.3686, "step": 844}, {"epoch": 1.2, "learning_rate": 0.00013641847895670797, "loss": 0.3929, "step": 845}, {"epoch": 1.2, "learning_rate": 0.0001362755724367397, "loss": 0.406, "step": 846}, {"epoch": 1.2, "learning_rate": 0.00013613258055684543, "loss": 0.4374, "step": 847}, {"epoch": 1.2, "learning_rate": 0.00013598950365349883, "loss": 0.3927, "step": 848}, {"epoch": 1.21, "learning_rate": 0.00013584634206337365, "loss": 0.4987, "step": 849}, {"epoch": 1.21, "learning_rate": 0.00013570309612334302, "loss": 0.3969, "step": 850}, {"epoch": 1.21, "learning_rate": 0.0001355597661704784, "loss": 0.3967, "step": 851}, {"epoch": 1.21, "learning_rate": 0.00013541635254204904, "loss": 0.3941, "step": 852}, {"epoch": 1.21, "learning_rate": 0.00013527285557552108, "loss": 0.4225, "step": 853}, {"epoch": 1.21, "learning_rate": 0.00013512927560855673, "loss": 0.3797, "step": 854}, {"epoch": 1.21, "learning_rate": 0.0001349856129790135, "loss": 0.3962, "step": 855}, {"epoch": 1.22, "learning_rate": 0.00013484186802494345, "loss": 0.3833, "step": 856}, {"epoch": 1.22, "learning_rate": 0.0001346980410845924, "loss": 0.4169, "step": 857}, {"epoch": 1.22, "learning_rate": 0.00013455413249639892, "loss": 0.3726, "step": 858}, {"epoch": 1.22, "learning_rate": 0.00013441014259899393, "loss": 0.4145, "step": 859}, {"epoch": 1.22, "learning_rate": 0.00013426607173119945, "loss": 0.4359, "step": 860}, {"epoch": 1.22, "learning_rate": 0.00013412192023202826, "loss": 0.3711, "step": 861}, {"epoch": 1.22, "learning_rate": 0.0001339776884406827, "loss": 0.3983, "step": 862}, {"epoch": 1.23, "learning_rate": 0.00013383337669655414, "loss": 0.4237, "step": 863}, {"epoch": 1.23, "learning_rate": 0.000133688985339222, "loss": 0.4024, "step": 864}, {"epoch": 1.23, "learning_rate": 0.00013354451470845317, "loss": 0.4493, "step": 865}, {"epoch": 1.23, "learning_rate": 0.00013339996514420097, "loss": 0.4122, "step": 866}, {"epoch": 1.23, "learning_rate": 0.00013325533698660443, "loss": 0.3898, "step": 867}, {"epoch": 1.23, "learning_rate": 0.00013311063057598764, "loss": 0.4276, "step": 868}, {"epoch": 1.23, "learning_rate": 0.00013296584625285878, "loss": 0.4249, "step": 869}, {"epoch": 1.24, "learning_rate": 0.00013282098435790925, "loss": 0.41, "step": 870}, {"epoch": 1.24, "learning_rate": 0.00013267604523201318, "loss": 0.4588, "step": 871}, {"epoch": 1.24, "learning_rate": 0.0001325310292162263, "loss": 0.421, "step": 872}, {"epoch": 1.24, "learning_rate": 0.00013238593665178532, "loss": 0.3721, "step": 873}, {"epoch": 1.24, "learning_rate": 0.00013224076788010701, "loss": 0.3855, "step": 874}, {"epoch": 1.24, "learning_rate": 0.00013209552324278755, "loss": 0.4029, "step": 875}, {"epoch": 1.24, "learning_rate": 0.00013195020308160157, "loss": 0.3955, "step": 876}, {"epoch": 1.25, "learning_rate": 0.0001318048077385015, "loss": 0.4154, "step": 877}, {"epoch": 1.25, "learning_rate": 0.0001316593375556166, "loss": 0.3752, "step": 878}, {"epoch": 1.25, "learning_rate": 0.00013151379287525226, "loss": 0.4036, "step": 879}, {"epoch": 1.25, "learning_rate": 0.00013136817403988917, "loss": 0.3992, "step": 880}, {"epoch": 1.25, "learning_rate": 0.00013122248139218253, "loss": 0.3875, "step": 881}, {"epoch": 1.25, "learning_rate": 0.00013107671527496116, "loss": 0.4642, "step": 882}, {"epoch": 1.25, "learning_rate": 0.00013093087603122688, "loss": 0.3934, "step": 883}, {"epoch": 1.26, "learning_rate": 0.0001307849640041535, "loss": 0.5233, "step": 884}, {"epoch": 1.26, "learning_rate": 0.0001306389795370861, "loss": 0.3582, "step": 885}, {"epoch": 1.26, "learning_rate": 0.00013049292297354024, "loss": 0.4285, "step": 886}, {"epoch": 1.26, "learning_rate": 0.00013034679465720114, "loss": 0.3679, "step": 887}, {"epoch": 1.26, "learning_rate": 0.00013020059493192284, "loss": 0.3927, "step": 888}, {"epoch": 1.26, "learning_rate": 0.00013005432414172735, "loss": 0.4005, "step": 889}, {"epoch": 1.26, "learning_rate": 0.00012990798263080405, "loss": 0.3745, "step": 890}, {"epoch": 1.27, "learning_rate": 0.00012976157074350863, "loss": 0.3826, "step": 891}, {"epoch": 1.27, "learning_rate": 0.0001296150888243624, "loss": 0.473, "step": 892}, {"epoch": 1.27, "learning_rate": 0.00012946853721805144, "loss": 0.4093, "step": 893}, {"epoch": 1.27, "learning_rate": 0.00012932191626942587, "loss": 0.3669, "step": 894}, {"epoch": 1.27, "learning_rate": 0.00012917522632349893, "loss": 0.4707, "step": 895}, {"epoch": 1.27, "learning_rate": 0.00012902846772544624, "loss": 0.5148, "step": 896}, {"epoch": 1.27, "learning_rate": 0.00012888164082060496, "loss": 0.4013, "step": 897}, {"epoch": 1.28, "learning_rate": 0.00012873474595447297, "loss": 0.43, "step": 898}, {"epoch": 1.28, "learning_rate": 0.0001285877834727081, "loss": 0.3571, "step": 899}, {"epoch": 1.28, "learning_rate": 0.0001284407537211272, "loss": 0.4232, "step": 900}, {"epoch": 1.28, "learning_rate": 0.00012829365704570555, "loss": 0.4251, "step": 901}, {"epoch": 1.28, "learning_rate": 0.00012814649379257583, "loss": 0.3636, "step": 902}, {"epoch": 1.28, "learning_rate": 0.00012799926430802735, "loss": 0.4205, "step": 903}, {"epoch": 1.28, "learning_rate": 0.0001278519689385053, "loss": 0.3794, "step": 904}, {"epoch": 1.29, "learning_rate": 0.00012770460803061, "loss": 0.3792, "step": 905}, {"epoch": 1.29, "learning_rate": 0.00012755718193109582, "loss": 0.4513, "step": 906}, {"epoch": 1.29, "learning_rate": 0.00012740969098687063, "loss": 0.4218, "step": 907}, {"epoch": 1.29, "learning_rate": 0.0001272621355449949, "loss": 0.4323, "step": 908}, {"epoch": 1.29, "learning_rate": 0.0001271145159526808, "loss": 0.3712, "step": 909}, {"epoch": 1.29, "learning_rate": 0.0001269668325572915, "loss": 0.4144, "step": 910}, {"epoch": 1.29, "learning_rate": 0.00012681908570634032, "loss": 0.4078, "step": 911}, {"epoch": 1.3, "learning_rate": 0.00012667127574748986, "loss": 0.4264, "step": 912}, {"epoch": 1.3, "learning_rate": 0.0001265234030285512, "loss": 0.4215, "step": 913}, {"epoch": 1.3, "learning_rate": 0.00012637546789748315, "loss": 0.4241, "step": 914}, {"epoch": 1.3, "learning_rate": 0.00012622747070239137, "loss": 0.4014, "step": 915}, {"epoch": 1.3, "learning_rate": 0.00012607941179152755, "loss": 0.4103, "step": 916}, {"epoch": 1.3, "learning_rate": 0.00012593129151328863, "loss": 0.4083, "step": 917}, {"epoch": 1.3, "learning_rate": 0.0001257831102162159, "loss": 0.3992, "step": 918}, {"epoch": 1.31, "learning_rate": 0.00012563486824899428, "loss": 0.3826, "step": 919}, {"epoch": 1.31, "learning_rate": 0.00012548656596045148, "loss": 0.4221, "step": 920}, {"epoch": 1.31, "learning_rate": 0.00012533820369955704, "loss": 0.3915, "step": 921}, {"epoch": 1.31, "learning_rate": 0.0001251897818154217, "loss": 0.3916, "step": 922}, {"epoch": 1.31, "learning_rate": 0.00012504130065729653, "loss": 0.3814, "step": 923}, {"epoch": 1.31, "learning_rate": 0.00012489276057457206, "loss": 0.4163, "step": 924}, {"epoch": 1.31, "learning_rate": 0.00012474416191677736, "loss": 0.3402, "step": 925}, {"epoch": 1.32, "learning_rate": 0.00012459550503357946, "loss": 0.4056, "step": 926}, {"epoch": 1.32, "learning_rate": 0.00012444679027478244, "loss": 0.4399, "step": 927}, {"epoch": 1.32, "learning_rate": 0.0001242980179903264, "loss": 0.4256, "step": 928}, {"epoch": 1.32, "learning_rate": 0.00012414918853028694, "loss": 0.4423, "step": 929}, {"epoch": 1.32, "learning_rate": 0.00012400030224487415, "loss": 0.4162, "step": 930}, {"epoch": 1.32, "learning_rate": 0.00012385135948443185, "loss": 0.3877, "step": 931}, {"epoch": 1.32, "learning_rate": 0.00012370236059943673, "loss": 0.4243, "step": 932}, {"epoch": 1.33, "learning_rate": 0.00012355330594049753, "loss": 0.4207, "step": 933}, {"epoch": 1.33, "learning_rate": 0.00012340419585835436, "loss": 0.3894, "step": 934}, {"epoch": 1.33, "learning_rate": 0.00012325503070387755, "loss": 0.4306, "step": 935}, {"epoch": 1.33, "learning_rate": 0.00012310581082806713, "loss": 0.427, "step": 936}, {"epoch": 1.33, "learning_rate": 0.0001229565365820519, "loss": 0.3926, "step": 937}, {"epoch": 1.33, "learning_rate": 0.00012280720831708857, "loss": 0.4352, "step": 938}, {"epoch": 1.33, "learning_rate": 0.00012265782638456102, "loss": 0.4006, "step": 939}, {"epoch": 1.34, "learning_rate": 0.00012250839113597928, "loss": 0.4246, "step": 940}, {"epoch": 1.34, "learning_rate": 0.000122358902922979, "loss": 0.4292, "step": 941}, {"epoch": 1.34, "learning_rate": 0.00012220936209732036, "loss": 0.4227, "step": 942}, {"epoch": 1.34, "learning_rate": 0.00012205976901088738, "loss": 0.4245, "step": 943}, {"epoch": 1.34, "learning_rate": 0.00012191012401568698, "loss": 0.4125, "step": 944}, {"epoch": 1.34, "learning_rate": 0.00012176042746384838, "loss": 0.4201, "step": 945}, {"epoch": 1.34, "learning_rate": 0.00012161067970762197, "loss": 0.3993, "step": 946}, {"epoch": 1.35, "learning_rate": 0.0001214608810993787, "loss": 0.4491, "step": 947}, {"epoch": 1.35, "learning_rate": 0.00012131103199160914, "loss": 0.4013, "step": 948}, {"epoch": 1.35, "learning_rate": 0.00012116113273692277, "loss": 0.3495, "step": 949}, {"epoch": 1.35, "learning_rate": 0.00012101118368804698, "loss": 0.4454, "step": 950}, {"epoch": 1.35, "learning_rate": 0.00012086118519782635, "loss": 0.4066, "step": 951}, {"epoch": 1.35, "learning_rate": 0.00012071113761922186, "loss": 0.4444, "step": 952}, {"epoch": 1.35, "learning_rate": 0.00012056104130530993, "loss": 0.4424, "step": 953}, {"epoch": 1.36, "learning_rate": 0.00012041089660928171, "loss": 0.4238, "step": 954}, {"epoch": 1.36, "learning_rate": 0.00012026070388444211, "loss": 0.4258, "step": 955}, {"epoch": 1.36, "learning_rate": 0.0001201104634842092, "loss": 0.4099, "step": 956}, {"epoch": 1.36, "learning_rate": 0.00011996017576211311, "loss": 0.4055, "step": 957}, {"epoch": 1.36, "learning_rate": 0.00011980984107179538, "loss": 0.444, "step": 958}, {"epoch": 1.36, "learning_rate": 0.00011965945976700804, "loss": 0.4517, "step": 959}, {"epoch": 1.36, "learning_rate": 0.00011950903220161285, "loss": 0.4361, "step": 960}, {"epoch": 1.37, "learning_rate": 0.00011935855872958037, "loss": 0.4046, "step": 961}, {"epoch": 1.37, "learning_rate": 0.00011920803970498924, "loss": 0.3581, "step": 962}, {"epoch": 1.37, "learning_rate": 0.00011905747548202528, "loss": 0.3859, "step": 963}, {"epoch": 1.37, "learning_rate": 0.00011890686641498063, "loss": 0.3944, "step": 964}, {"epoch": 1.37, "learning_rate": 0.00011875621285825298, "loss": 0.4101, "step": 965}, {"epoch": 1.37, "learning_rate": 0.00011860551516634466, "loss": 0.406, "step": 966}, {"epoch": 1.37, "learning_rate": 0.00011845477369386198, "loss": 0.4185, "step": 967}, {"epoch": 1.38, "learning_rate": 0.00011830398879551412, "loss": 0.4205, "step": 968}, {"epoch": 1.38, "learning_rate": 0.00011815316082611252, "loss": 0.4083, "step": 969}, {"epoch": 1.38, "learning_rate": 0.00011800229014056996, "loss": 0.417, "step": 970}, {"epoch": 1.38, "learning_rate": 0.00011785137709389977, "loss": 0.3937, "step": 971}, {"epoch": 1.38, "learning_rate": 0.0001177004220412149, "loss": 0.4102, "step": 972}, {"epoch": 1.38, "learning_rate": 0.00011754942533772715, "loss": 0.3717, "step": 973}, {"epoch": 1.38, "learning_rate": 0.0001173983873387464, "loss": 0.4249, "step": 974}, {"epoch": 1.38, "learning_rate": 0.00011724730839967961, "loss": 0.4198, "step": 975}, {"epoch": 1.39, "learning_rate": 0.00011709618887603014, "loss": 0.3871, "step": 976}, {"epoch": 1.39, "learning_rate": 0.0001169450291233968, "loss": 0.4101, "step": 977}, {"epoch": 1.39, "learning_rate": 0.00011679382949747313, "loss": 0.4295, "step": 978}, {"epoch": 1.39, "learning_rate": 0.00011664259035404644, "loss": 0.3667, "step": 979}, {"epoch": 1.39, "learning_rate": 0.00011649131204899701, "loss": 0.377, "step": 980}, {"epoch": 1.39, "learning_rate": 0.00011633999493829733, "loss": 0.3799, "step": 981}, {"epoch": 1.39, "learning_rate": 0.0001161886393780112, "loss": 0.4373, "step": 982}, {"epoch": 1.4, "learning_rate": 0.00011603724572429284, "loss": 0.3868, "step": 983}, {"epoch": 1.4, "learning_rate": 0.00011588581433338614, "loss": 0.3891, "step": 984}, {"epoch": 1.4, "learning_rate": 0.00011573434556162383, "loss": 0.3732, "step": 985}, {"epoch": 1.4, "learning_rate": 0.00011558283976542654, "loss": 0.4141, "step": 986}, {"epoch": 1.4, "learning_rate": 0.00011543129730130203, "loss": 0.4122, "step": 987}, {"epoch": 1.4, "learning_rate": 0.00011527971852584434, "loss": 0.3985, "step": 988}, {"epoch": 1.4, "learning_rate": 0.00011512810379573302, "loss": 0.4247, "step": 989}, {"epoch": 1.41, "learning_rate": 0.00011497645346773216, "loss": 0.441, "step": 990}, {"epoch": 1.41, "learning_rate": 0.00011482476789868963, "loss": 0.4245, "step": 991}, {"epoch": 1.41, "learning_rate": 0.00011467304744553618, "loss": 0.4373, "step": 992}, {"epoch": 1.41, "learning_rate": 0.00011452129246528476, "loss": 0.4282, "step": 993}, {"epoch": 1.41, "learning_rate": 0.00011436950331502947, "loss": 0.3612, "step": 994}, {"epoch": 1.41, "learning_rate": 0.0001142176803519448, "loss": 0.4033, "step": 995}, {"epoch": 1.41, "learning_rate": 0.00011406582393328493, "loss": 0.3662, "step": 996}, {"epoch": 1.42, "learning_rate": 0.00011391393441638263, "loss": 0.3708, "step": 997}, {"epoch": 1.42, "learning_rate": 0.00011376201215864864, "loss": 0.4105, "step": 998}, {"epoch": 1.42, "learning_rate": 0.00011361005751757064, "loss": 0.437, "step": 999}, {"epoch": 1.42, "learning_rate": 0.00011345807085071262, "loss": 0.4037, "step": 1000}], "logging_steps": 1.0, "max_steps": 2112, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 1000, "total_flos": 1.4570212465699717e+18, "train_batch_size": 16, "trial_name": null, "trial_params": null}