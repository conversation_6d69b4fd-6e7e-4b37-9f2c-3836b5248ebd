{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.840909090909091, "eval_steps": 500, "global_step": 2000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0, "learning_rate": 3.125e-06, "loss": 1.879, "step": 1}, {"epoch": 0.0, "learning_rate": 6.25e-06, "loss": 2.0201, "step": 2}, {"epoch": 0.0, "learning_rate": 9.375000000000001e-06, "loss": 1.9607, "step": 3}, {"epoch": 0.01, "learning_rate": 1.25e-05, "loss": 1.9719, "step": 4}, {"epoch": 0.01, "learning_rate": 1.5625e-05, "loss": 1.9403, "step": 5}, {"epoch": 0.01, "learning_rate": 1.8750000000000002e-05, "loss": 2.0394, "step": 6}, {"epoch": 0.01, "learning_rate": 2.1875e-05, "loss": 1.9894, "step": 7}, {"epoch": 0.01, "learning_rate": 2.5e-05, "loss": 1.9953, "step": 8}, {"epoch": 0.01, "learning_rate": 2.8125000000000003e-05, "loss": 1.9032, "step": 9}, {"epoch": 0.01, "learning_rate": 3.125e-05, "loss": 1.8875, "step": 10}, {"epoch": 0.02, "learning_rate": 3.4375e-05, "loss": 1.7733, "step": 11}, {"epoch": 0.02, "learning_rate": 3.7500000000000003e-05, "loss": 1.7575, "step": 12}, {"epoch": 0.02, "learning_rate": 4.0625000000000005e-05, "loss": 1.6313, "step": 13}, {"epoch": 0.02, "learning_rate": 4.375e-05, "loss": 1.6614, "step": 14}, {"epoch": 0.02, "learning_rate": 4.6875e-05, "loss": 1.3717, "step": 15}, {"epoch": 0.02, "learning_rate": 5e-05, "loss": 1.4089, "step": 16}, {"epoch": 0.02, "learning_rate": 5.3125000000000004e-05, "loss": 1.5035, "step": 17}, {"epoch": 0.03, "learning_rate": 5.6250000000000005e-05, "loss": 1.4314, "step": 18}, {"epoch": 0.03, "learning_rate": 5.9375e-05, "loss": 1.3397, "step": 19}, {"epoch": 0.03, "learning_rate": 6.25e-05, "loss": 1.2895, "step": 20}, {"epoch": 0.03, "learning_rate": 6.562500000000001e-05, "loss": 1.2703, "step": 21}, {"epoch": 0.03, "learning_rate": 6.875e-05, "loss": 1.2024, "step": 22}, {"epoch": 0.03, "learning_rate": 7.1875e-05, "loss": 1.1181, "step": 23}, {"epoch": 0.03, "learning_rate": 7.500000000000001e-05, "loss": 1.0621, "step": 24}, {"epoch": 0.04, "learning_rate": 7.8125e-05, "loss": 1.1552, "step": 25}, {"epoch": 0.04, "learning_rate": 8.125000000000001e-05, "loss": 1.072, "step": 26}, {"epoch": 0.04, "learning_rate": 8.4375e-05, "loss": 1.0174, "step": 27}, {"epoch": 0.04, "learning_rate": 8.75e-05, "loss": 1.0041, "step": 28}, {"epoch": 0.04, "learning_rate": 9.062500000000001e-05, "loss": 0.9927, "step": 29}, {"epoch": 0.04, "learning_rate": 9.375e-05, "loss": 1.0451, "step": 30}, {"epoch": 0.04, "learning_rate": 9.687500000000001e-05, "loss": 0.9676, "step": 31}, {"epoch": 0.05, "learning_rate": 0.0001, "loss": 1.0218, "step": 32}, {"epoch": 0.05, "learning_rate": 0.000103125, "loss": 0.9371, "step": 33}, {"epoch": 0.05, "learning_rate": 0.00010625000000000001, "loss": 1.0346, "step": 34}, {"epoch": 0.05, "learning_rate": 0.000109375, "loss": 0.9525, "step": 35}, {"epoch": 0.05, "learning_rate": 0.00011250000000000001, "loss": 0.9403, "step": 36}, {"epoch": 0.05, "learning_rate": 0.000115625, "loss": 0.9365, "step": 37}, {"epoch": 0.05, "learning_rate": 0.00011875, "loss": 0.873, "step": 38}, {"epoch": 0.06, "learning_rate": 0.00012187500000000001, "loss": 0.9355, "step": 39}, {"epoch": 0.06, "learning_rate": 0.000125, "loss": 0.8364, "step": 40}, {"epoch": 0.06, "learning_rate": 0.000128125, "loss": 0.8648, "step": 41}, {"epoch": 0.06, "learning_rate": 0.00013125000000000002, "loss": 0.794, "step": 42}, {"epoch": 0.06, "learning_rate": 0.000134375, "loss": 0.8647, "step": 43}, {"epoch": 0.06, "learning_rate": 0.0001375, "loss": 0.86, "step": 44}, {"epoch": 0.06, "learning_rate": 0.00014062500000000002, "loss": 0.8198, "step": 45}, {"epoch": 0.07, "learning_rate": 0.00014375, "loss": 0.7834, "step": 46}, {"epoch": 0.07, "learning_rate": 0.000146875, "loss": 0.784, "step": 47}, {"epoch": 0.07, "learning_rate": 0.00015000000000000001, "loss": 0.7656, "step": 48}, {"epoch": 0.07, "learning_rate": 0.000153125, "loss": 0.7672, "step": 49}, {"epoch": 0.07, "learning_rate": 0.00015625, "loss": 0.757, "step": 50}, {"epoch": 0.07, "learning_rate": 0.000159375, "loss": 0.6981, "step": 51}, {"epoch": 0.07, "learning_rate": 0.00016250000000000002, "loss": 0.7089, "step": 52}, {"epoch": 0.08, "learning_rate": 0.000165625, "loss": 0.7049, "step": 53}, {"epoch": 0.08, "learning_rate": 0.00016875, "loss": 0.686, "step": 54}, {"epoch": 0.08, "learning_rate": 0.00017187500000000002, "loss": 0.682, "step": 55}, {"epoch": 0.08, "learning_rate": 0.000175, "loss": 0.6788, "step": 56}, {"epoch": 0.08, "learning_rate": 0.000178125, "loss": 0.7012, "step": 57}, {"epoch": 0.08, "learning_rate": 0.00018125000000000001, "loss": 0.7304, "step": 58}, {"epoch": 0.08, "learning_rate": 0.000184375, "loss": 0.7093, "step": 59}, {"epoch": 0.09, "learning_rate": 0.0001875, "loss": 0.71, "step": 60}, {"epoch": 0.09, "learning_rate": 0.000190625, "loss": 0.7122, "step": 61}, {"epoch": 0.09, "learning_rate": 0.00019375000000000002, "loss": 0.6775, "step": 62}, {"epoch": 0.09, "learning_rate": 0.000196875, "loss": 0.6428, "step": 63}, {"epoch": 0.09, "learning_rate": 0.0002, "loss": 0.6753, "step": 64}, {"epoch": 0.09, "learning_rate": 0.0001999998823451702, "loss": 0.6821, "step": 65}, {"epoch": 0.09, "learning_rate": 0.00019999952938095763, "loss": 0.6813, "step": 66}, {"epoch": 0.1, "learning_rate": 0.00019999894110819285, "loss": 0.6462, "step": 67}, {"epoch": 0.1, "learning_rate": 0.00019999811752826013, "loss": 0.6671, "step": 68}, {"epoch": 0.1, "learning_rate": 0.00019999705864309745, "loss": 0.6121, "step": 69}, {"epoch": 0.1, "learning_rate": 0.00019999576445519642, "loss": 0.6966, "step": 70}, {"epoch": 0.1, "learning_rate": 0.00019999423496760242, "loss": 0.6414, "step": 71}, {"epoch": 0.1, "learning_rate": 0.00019999247018391447, "loss": 0.6587, "step": 72}, {"epoch": 0.1, "learning_rate": 0.00019999047010828532, "loss": 0.7012, "step": 73}, {"epoch": 0.11, "learning_rate": 0.00019998823474542125, "loss": 0.6356, "step": 74}, {"epoch": 0.11, "learning_rate": 0.0001999857641005824, "loss": 0.6016, "step": 75}, {"epoch": 0.11, "learning_rate": 0.00019998305817958234, "loss": 0.6285, "step": 76}, {"epoch": 0.11, "learning_rate": 0.00019998011698878844, "loss": 0.617, "step": 77}, {"epoch": 0.11, "learning_rate": 0.00019997694053512156, "loss": 0.61, "step": 78}, {"epoch": 0.11, "learning_rate": 0.00019997352882605618, "loss": 0.6156, "step": 79}, {"epoch": 0.11, "learning_rate": 0.00019996988186962041, "loss": 0.5893, "step": 80}, {"epoch": 0.12, "learning_rate": 0.00019996599967439594, "loss": 0.6579, "step": 81}, {"epoch": 0.12, "learning_rate": 0.00019996188224951787, "loss": 0.5918, "step": 82}, {"epoch": 0.12, "learning_rate": 0.00019995752960467493, "loss": 0.5904, "step": 83}, {"epoch": 0.12, "learning_rate": 0.00019995294175010933, "loss": 0.5991, "step": 84}, {"epoch": 0.12, "learning_rate": 0.0001999481186966167, "loss": 0.6022, "step": 85}, {"epoch": 0.12, "learning_rate": 0.00019994306045554617, "loss": 0.6016, "step": 86}, {"epoch": 0.12, "learning_rate": 0.0001999377670388003, "loss": 0.6342, "step": 87}, {"epoch": 0.12, "learning_rate": 0.00019993223845883495, "loss": 0.5841, "step": 88}, {"epoch": 0.13, "learning_rate": 0.00019992647472865947, "loss": 0.5144, "step": 89}, {"epoch": 0.13, "learning_rate": 0.0001999204758618364, "loss": 0.5578, "step": 90}, {"epoch": 0.13, "learning_rate": 0.00019991424187248172, "loss": 0.5883, "step": 91}, {"epoch": 0.13, "learning_rate": 0.00019990777277526455, "loss": 0.5013, "step": 92}, {"epoch": 0.13, "learning_rate": 0.00019990106858540734, "loss": 0.5896, "step": 93}, {"epoch": 0.13, "learning_rate": 0.0001998941293186857, "loss": 0.6137, "step": 94}, {"epoch": 0.13, "learning_rate": 0.00019988695499142836, "loss": 0.5656, "step": 95}, {"epoch": 0.14, "learning_rate": 0.00019987954562051725, "loss": 0.546, "step": 96}, {"epoch": 0.14, "learning_rate": 0.0001998719012233873, "loss": 0.5499, "step": 97}, {"epoch": 0.14, "learning_rate": 0.00019986402181802653, "loss": 0.6248, "step": 98}, {"epoch": 0.14, "learning_rate": 0.00019985590742297596, "loss": 0.6231, "step": 99}, {"epoch": 0.14, "learning_rate": 0.0001998475580573295, "loss": 0.5057, "step": 100}, {"epoch": 0.14, "learning_rate": 0.00019983897374073402, "loss": 0.5691, "step": 101}, {"epoch": 0.14, "learning_rate": 0.0001998301544933893, "loss": 0.5485, "step": 102}, {"epoch": 0.15, "learning_rate": 0.00019982110033604783, "loss": 0.5586, "step": 103}, {"epoch": 0.15, "learning_rate": 0.00019981181129001492, "loss": 0.5331, "step": 104}, {"epoch": 0.15, "learning_rate": 0.00019980228737714864, "loss": 0.584, "step": 105}, {"epoch": 0.15, "learning_rate": 0.0001997925286198596, "loss": 0.5505, "step": 106}, {"epoch": 0.15, "learning_rate": 0.00019978253504111118, "loss": 0.5311, "step": 107}, {"epoch": 0.15, "learning_rate": 0.00019977230666441917, "loss": 0.5739, "step": 108}, {"epoch": 0.15, "learning_rate": 0.00019976184351385196, "loss": 0.5513, "step": 109}, {"epoch": 0.16, "learning_rate": 0.00019975114561403037, "loss": 0.5849, "step": 110}, {"epoch": 0.16, "learning_rate": 0.00019974021299012752, "loss": 0.5628, "step": 111}, {"epoch": 0.16, "learning_rate": 0.00019972904566786903, "loss": 0.5164, "step": 112}, {"epoch": 0.16, "learning_rate": 0.00019971764367353263, "loss": 0.5575, "step": 113}, {"epoch": 0.16, "learning_rate": 0.0001997060070339483, "loss": 0.5807, "step": 114}, {"epoch": 0.16, "learning_rate": 0.00019969413577649822, "loss": 0.5523, "step": 115}, {"epoch": 0.16, "learning_rate": 0.00019968202992911658, "loss": 0.6021, "step": 116}, {"epoch": 0.17, "learning_rate": 0.0001996696895202896, "loss": 0.5406, "step": 117}, {"epoch": 0.17, "learning_rate": 0.0001996571145790555, "loss": 0.543, "step": 118}, {"epoch": 0.17, "learning_rate": 0.00019964430513500427, "loss": 0.6027, "step": 119}, {"epoch": 0.17, "learning_rate": 0.0001996312612182778, "loss": 0.5268, "step": 120}, {"epoch": 0.17, "learning_rate": 0.00019961798285956972, "loss": 0.544, "step": 121}, {"epoch": 0.17, "learning_rate": 0.00019960447009012523, "loss": 0.4913, "step": 122}, {"epoch": 0.17, "learning_rate": 0.00019959072294174117, "loss": 0.5317, "step": 123}, {"epoch": 0.18, "learning_rate": 0.000199576741446766, "loss": 0.5181, "step": 124}, {"epoch": 0.18, "learning_rate": 0.00019956252563809942, "loss": 0.5023, "step": 125}, {"epoch": 0.18, "learning_rate": 0.0001995480755491927, "loss": 0.5209, "step": 126}, {"epoch": 0.18, "learning_rate": 0.00019953339121404825, "loss": 0.5343, "step": 127}, {"epoch": 0.18, "learning_rate": 0.0001995184726672197, "loss": 0.5149, "step": 128}, {"epoch": 0.18, "learning_rate": 0.00019950331994381189, "loss": 0.5383, "step": 129}, {"epoch": 0.18, "learning_rate": 0.00019948793307948058, "loss": 0.5396, "step": 130}, {"epoch": 0.19, "learning_rate": 0.00019947231211043257, "loss": 0.4913, "step": 131}, {"epoch": 0.19, "learning_rate": 0.00019945645707342556, "loss": 0.5484, "step": 132}, {"epoch": 0.19, "learning_rate": 0.00019944036800576792, "loss": 0.55, "step": 133}, {"epoch": 0.19, "learning_rate": 0.0001994240449453188, "loss": 0.522, "step": 134}, {"epoch": 0.19, "learning_rate": 0.00019940748793048795, "loss": 0.5363, "step": 135}, {"epoch": 0.19, "learning_rate": 0.00019939069700023563, "loss": 0.5315, "step": 136}, {"epoch": 0.19, "learning_rate": 0.0001993736721940725, "loss": 0.5267, "step": 137}, {"epoch": 0.2, "learning_rate": 0.00019935641355205955, "loss": 0.5188, "step": 138}, {"epoch": 0.2, "learning_rate": 0.00019933892111480807, "loss": 0.529, "step": 139}, {"epoch": 0.2, "learning_rate": 0.00019932119492347945, "loss": 0.5441, "step": 140}, {"epoch": 0.2, "learning_rate": 0.00019930323501978517, "loss": 0.5566, "step": 141}, {"epoch": 0.2, "learning_rate": 0.0001992850414459865, "loss": 0.5829, "step": 142}, {"epoch": 0.2, "learning_rate": 0.0001992666142448948, "loss": 0.5148, "step": 143}, {"epoch": 0.2, "learning_rate": 0.000199247953459871, "loss": 0.5074, "step": 144}, {"epoch": 0.21, "learning_rate": 0.00019922905913482574, "loss": 0.5473, "step": 145}, {"epoch": 0.21, "learning_rate": 0.00019920993131421918, "loss": 0.5488, "step": 146}, {"epoch": 0.21, "learning_rate": 0.00019919057004306095, "loss": 0.546, "step": 147}, {"epoch": 0.21, "learning_rate": 0.00019917097536690997, "loss": 0.4565, "step": 148}, {"epoch": 0.21, "learning_rate": 0.00019915114733187438, "loss": 0.5145, "step": 149}, {"epoch": 0.21, "learning_rate": 0.00019913108598461156, "loss": 0.5753, "step": 150}, {"epoch": 0.21, "learning_rate": 0.0001991107913723277, "loss": 0.4592, "step": 151}, {"epoch": 0.22, "learning_rate": 0.000199090263542778, "loss": 0.5338, "step": 152}, {"epoch": 0.22, "learning_rate": 0.00019906950254426647, "loss": 0.6067, "step": 153}, {"epoch": 0.22, "learning_rate": 0.0001990485084256457, "loss": 0.5154, "step": 154}, {"epoch": 0.22, "learning_rate": 0.00019902728123631693, "loss": 0.5296, "step": 155}, {"epoch": 0.22, "learning_rate": 0.00019900582102622973, "loss": 0.5191, "step": 156}, {"epoch": 0.22, "learning_rate": 0.00019898412784588208, "loss": 0.5353, "step": 157}, {"epoch": 0.22, "learning_rate": 0.0001989622017463201, "loss": 0.4864, "step": 158}, {"epoch": 0.23, "learning_rate": 0.00019894004277913805, "loss": 0.531, "step": 159}, {"epoch": 0.23, "learning_rate": 0.0001989176509964781, "loss": 0.7777, "step": 160}, {"epoch": 0.23, "learning_rate": 0.00019889502645103032, "loss": 0.5556, "step": 161}, {"epoch": 0.23, "learning_rate": 0.00019887216919603238, "loss": 0.4751, "step": 162}, {"epoch": 0.23, "learning_rate": 0.00019884907928526967, "loss": 0.5465, "step": 163}, {"epoch": 0.23, "learning_rate": 0.00019882575677307495, "loss": 0.5302, "step": 164}, {"epoch": 0.23, "learning_rate": 0.00019880220171432839, "loss": 0.5246, "step": 165}, {"epoch": 0.24, "learning_rate": 0.00019877841416445722, "loss": 0.4843, "step": 166}, {"epoch": 0.24, "learning_rate": 0.00019875439417943593, "loss": 0.4844, "step": 167}, {"epoch": 0.24, "learning_rate": 0.00019873014181578586, "loss": 0.5731, "step": 168}, {"epoch": 0.24, "learning_rate": 0.0001987056571305751, "loss": 0.481, "step": 169}, {"epoch": 0.24, "learning_rate": 0.00019868094018141857, "loss": 0.5164, "step": 170}, {"epoch": 0.24, "learning_rate": 0.00019865599102647754, "loss": 0.4877, "step": 171}, {"epoch": 0.24, "learning_rate": 0.0001986308097244599, "loss": 0.452, "step": 172}, {"epoch": 0.25, "learning_rate": 0.00019860539633461953, "loss": 0.4933, "step": 173}, {"epoch": 0.25, "learning_rate": 0.00019857975091675675, "loss": 0.5214, "step": 174}, {"epoch": 0.25, "learning_rate": 0.00019855387353121762, "loss": 0.5485, "step": 175}, {"epoch": 0.25, "learning_rate": 0.0001985277642388941, "loss": 0.5629, "step": 176}, {"epoch": 0.25, "learning_rate": 0.000198501423101224, "loss": 0.5514, "step": 177}, {"epoch": 0.25, "learning_rate": 0.00019847485018019043, "loss": 0.475, "step": 178}, {"epoch": 0.25, "learning_rate": 0.0001984480455383221, "loss": 0.5007, "step": 179}, {"epoch": 0.26, "learning_rate": 0.0001984210092386929, "loss": 0.473, "step": 180}, {"epoch": 0.26, "learning_rate": 0.0001983937413449219, "loss": 0.5216, "step": 181}, {"epoch": 0.26, "learning_rate": 0.00019836624192117304, "loss": 0.4694, "step": 182}, {"epoch": 0.26, "learning_rate": 0.00019833851103215512, "loss": 0.5611, "step": 183}, {"epoch": 0.26, "learning_rate": 0.00019831054874312165, "loss": 0.5268, "step": 184}, {"epoch": 0.26, "learning_rate": 0.00019828235511987053, "loss": 0.4794, "step": 185}, {"epoch": 0.26, "learning_rate": 0.00019825393022874415, "loss": 0.5055, "step": 186}, {"epoch": 0.27, "learning_rate": 0.00019822527413662896, "loss": 0.4928, "step": 187}, {"epoch": 0.27, "learning_rate": 0.00019819638691095554, "loss": 0.5166, "step": 188}, {"epoch": 0.27, "learning_rate": 0.00019816726861969833, "loss": 0.4815, "step": 189}, {"epoch": 0.27, "learning_rate": 0.00019813791933137546, "loss": 0.4745, "step": 190}, {"epoch": 0.27, "learning_rate": 0.00019810833911504866, "loss": 0.52, "step": 191}, {"epoch": 0.27, "learning_rate": 0.00019807852804032305, "loss": 0.4522, "step": 192}, {"epoch": 0.27, "learning_rate": 0.00019804848617734696, "loss": 0.5077, "step": 193}, {"epoch": 0.28, "learning_rate": 0.00019801821359681173, "loss": 0.5345, "step": 194}, {"epoch": 0.28, "learning_rate": 0.00019798771036995177, "loss": 0.4586, "step": 195}, {"epoch": 0.28, "learning_rate": 0.00019795697656854405, "loss": 0.5182, "step": 196}, {"epoch": 0.28, "learning_rate": 0.0001979260122649082, "loss": 0.5526, "step": 197}, {"epoch": 0.28, "learning_rate": 0.00019789481753190624, "loss": 0.5176, "step": 198}, {"epoch": 0.28, "learning_rate": 0.00019786339244294232, "loss": 0.5162, "step": 199}, {"epoch": 0.28, "learning_rate": 0.00019783173707196277, "loss": 0.5386, "step": 200}, {"epoch": 0.29, "learning_rate": 0.00019779985149345574, "loss": 0.4674, "step": 201}, {"epoch": 0.29, "learning_rate": 0.000197767735782451, "loss": 0.5029, "step": 202}, {"epoch": 0.29, "learning_rate": 0.00019773539001452002, "loss": 0.4387, "step": 203}, {"epoch": 0.29, "learning_rate": 0.00019770281426577545, "loss": 0.4421, "step": 204}, {"epoch": 0.29, "learning_rate": 0.00019767000861287118, "loss": 0.5304, "step": 205}, {"epoch": 0.29, "learning_rate": 0.0001976369731330021, "loss": 0.5094, "step": 206}, {"epoch": 0.29, "learning_rate": 0.00019760370790390392, "loss": 0.4549, "step": 207}, {"epoch": 0.3, "learning_rate": 0.00019757021300385286, "loss": 0.4701, "step": 208}, {"epoch": 0.3, "learning_rate": 0.00019753648851166572, "loss": 0.5286, "step": 209}, {"epoch": 0.3, "learning_rate": 0.00019750253450669943, "loss": 0.4868, "step": 210}, {"epoch": 0.3, "learning_rate": 0.0001974683510688511, "loss": 0.4631, "step": 211}, {"epoch": 0.3, "learning_rate": 0.00019743393827855758, "loss": 0.5099, "step": 212}, {"epoch": 0.3, "learning_rate": 0.0001973992962167956, "loss": 0.5604, "step": 213}, {"epoch": 0.3, "learning_rate": 0.0001973644249650812, "loss": 0.52, "step": 214}, {"epoch": 0.31, "learning_rate": 0.00019732932460546986, "loss": 0.457, "step": 215}, {"epoch": 0.31, "learning_rate": 0.00019729399522055603, "loss": 0.5672, "step": 216}, {"epoch": 0.31, "learning_rate": 0.00019725843689347324, "loss": 0.5077, "step": 217}, {"epoch": 0.31, "learning_rate": 0.00019722264970789365, "loss": 0.483, "step": 218}, {"epoch": 0.31, "learning_rate": 0.00019718663374802795, "loss": 0.5057, "step": 219}, {"epoch": 0.31, "learning_rate": 0.00019715038909862517, "loss": 0.4997, "step": 220}, {"epoch": 0.31, "learning_rate": 0.00019711391584497251, "loss": 0.4265, "step": 221}, {"epoch": 0.32, "learning_rate": 0.00019707721407289505, "loss": 0.4523, "step": 222}, {"epoch": 0.32, "learning_rate": 0.00019704028386875554, "loss": 0.4835, "step": 223}, {"epoch": 0.32, "learning_rate": 0.00019700312531945442, "loss": 0.5532, "step": 224}, {"epoch": 0.32, "learning_rate": 0.00019696573851242925, "loss": 0.4967, "step": 225}, {"epoch": 0.32, "learning_rate": 0.00019692812353565487, "loss": 0.4931, "step": 226}, {"epoch": 0.32, "learning_rate": 0.0001968902804776429, "loss": 0.5019, "step": 227}, {"epoch": 0.32, "learning_rate": 0.00019685220942744174, "loss": 0.5212, "step": 228}, {"epoch": 0.33, "learning_rate": 0.00019681391047463628, "loss": 0.5012, "step": 229}, {"epoch": 0.33, "learning_rate": 0.00019677538370934755, "loss": 0.461, "step": 230}, {"epoch": 0.33, "learning_rate": 0.00019673662922223287, "loss": 0.4767, "step": 231}, {"epoch": 0.33, "learning_rate": 0.00019669764710448522, "loss": 0.4702, "step": 232}, {"epoch": 0.33, "learning_rate": 0.00019665843744783333, "loss": 0.4367, "step": 233}, {"epoch": 0.33, "learning_rate": 0.00019661900034454127, "loss": 0.4437, "step": 234}, {"epoch": 0.33, "learning_rate": 0.00019657933588740838, "loss": 0.4674, "step": 235}, {"epoch": 0.34, "learning_rate": 0.00019653944416976894, "loss": 0.4347, "step": 236}, {"epoch": 0.34, "learning_rate": 0.00019649932528549205, "loss": 0.4794, "step": 237}, {"epoch": 0.34, "learning_rate": 0.00019645897932898127, "loss": 0.45, "step": 238}, {"epoch": 0.34, "learning_rate": 0.00019641840639517458, "loss": 0.4798, "step": 239}, {"epoch": 0.34, "learning_rate": 0.000196377606579544, "loss": 0.5001, "step": 240}, {"epoch": 0.34, "learning_rate": 0.00019633657997809541, "loss": 0.4986, "step": 241}, {"epoch": 0.34, "learning_rate": 0.00019629532668736838, "loss": 0.4708, "step": 242}, {"epoch": 0.35, "learning_rate": 0.00019625384680443592, "loss": 0.5421, "step": 243}, {"epoch": 0.35, "learning_rate": 0.00019621214042690418, "loss": 0.5062, "step": 244}, {"epoch": 0.35, "learning_rate": 0.00019617020765291224, "loss": 0.5255, "step": 245}, {"epoch": 0.35, "learning_rate": 0.00019612804858113207, "loss": 0.4295, "step": 246}, {"epoch": 0.35, "learning_rate": 0.000196085663310768, "loss": 0.4757, "step": 247}, {"epoch": 0.35, "learning_rate": 0.0001960430519415566, "loss": 0.4842, "step": 248}, {"epoch": 0.35, "learning_rate": 0.0001960002145737666, "loss": 0.5124, "step": 249}, {"epoch": 0.36, "learning_rate": 0.00019595715130819844, "loss": 0.4752, "step": 250}, {"epoch": 0.36, "learning_rate": 0.0001959138622461842, "loss": 0.5261, "step": 251}, {"epoch": 0.36, "learning_rate": 0.00019587034748958716, "loss": 0.4542, "step": 252}, {"epoch": 0.36, "learning_rate": 0.0001958266071408018, "loss": 0.4972, "step": 253}, {"epoch": 0.36, "learning_rate": 0.0001957826413027533, "loss": 0.5065, "step": 254}, {"epoch": 0.36, "learning_rate": 0.0001957384500788976, "loss": 0.4704, "step": 255}, {"epoch": 0.36, "learning_rate": 0.0001956940335732209, "loss": 0.6936, "step": 256}, {"epoch": 0.37, "learning_rate": 0.00019564939189023952, "loss": 0.6363, "step": 257}, {"epoch": 0.37, "learning_rate": 0.00019560452513499966, "loss": 0.4575, "step": 258}, {"epoch": 0.37, "learning_rate": 0.00019555943341307712, "loss": 0.4495, "step": 259}, {"epoch": 0.37, "learning_rate": 0.0001955141168305771, "loss": 0.4952, "step": 260}, {"epoch": 0.37, "learning_rate": 0.00019546857549413384, "loss": 0.4597, "step": 261}, {"epoch": 0.37, "learning_rate": 0.00019542280951091056, "loss": 0.4649, "step": 262}, {"epoch": 0.37, "learning_rate": 0.00019537681898859903, "loss": 0.4672, "step": 263}, {"epoch": 0.38, "learning_rate": 0.00019533060403541938, "loss": 0.5061, "step": 264}, {"epoch": 0.38, "learning_rate": 0.00019528416476011988, "loss": 0.4712, "step": 265}, {"epoch": 0.38, "learning_rate": 0.0001952375012719766, "loss": 0.5007, "step": 266}, {"epoch": 0.38, "learning_rate": 0.00019519061368079323, "loss": 0.5047, "step": 267}, {"epoch": 0.38, "learning_rate": 0.00019514350209690084, "loss": 0.4631, "step": 268}, {"epoch": 0.38, "learning_rate": 0.00019509616663115754, "loss": 0.4445, "step": 269}, {"epoch": 0.38, "learning_rate": 0.0001950486073949482, "loss": 0.4956, "step": 270}, {"epoch": 0.38, "learning_rate": 0.0001950008245001843, "loss": 0.4738, "step": 271}, {"epoch": 0.39, "learning_rate": 0.00019495281805930367, "loss": 0.4975, "step": 272}, {"epoch": 0.39, "learning_rate": 0.00019490458818527008, "loss": 0.4929, "step": 273}, {"epoch": 0.39, "learning_rate": 0.00019485613499157305, "loss": 0.4764, "step": 274}, {"epoch": 0.39, "learning_rate": 0.0001948074585922276, "loss": 0.6595, "step": 275}, {"epoch": 0.39, "learning_rate": 0.0001947585591017741, "loss": 0.4671, "step": 276}, {"epoch": 0.39, "learning_rate": 0.00019470943663527773, "loss": 0.4385, "step": 277}, {"epoch": 0.39, "learning_rate": 0.00019466009130832837, "loss": 0.4875, "step": 278}, {"epoch": 0.4, "learning_rate": 0.00019461052323704037, "loss": 0.47, "step": 279}, {"epoch": 0.4, "learning_rate": 0.00019456073253805214, "loss": 0.4543, "step": 280}, {"epoch": 0.4, "learning_rate": 0.00019451071932852606, "loss": 0.4534, "step": 281}, {"epoch": 0.4, "learning_rate": 0.00019446048372614804, "loss": 0.5182, "step": 282}, {"epoch": 0.4, "learning_rate": 0.00019441002584912726, "loss": 0.4929, "step": 283}, {"epoch": 0.4, "learning_rate": 0.00019435934581619603, "loss": 0.479, "step": 284}, {"epoch": 0.4, "learning_rate": 0.00019430844374660936, "loss": 0.422, "step": 285}, {"epoch": 0.41, "learning_rate": 0.0001942573197601447, "loss": 0.489, "step": 286}, {"epoch": 0.41, "learning_rate": 0.00019420597397710174, "loss": 0.4499, "step": 287}, {"epoch": 0.41, "learning_rate": 0.00019415440651830208, "loss": 0.5082, "step": 288}, {"epoch": 0.41, "learning_rate": 0.00019410261750508892, "loss": 0.496, "step": 289}, {"epoch": 0.41, "learning_rate": 0.00019405060705932683, "loss": 0.518, "step": 290}, {"epoch": 0.41, "learning_rate": 0.00019399837530340142, "loss": 0.4779, "step": 291}, {"epoch": 0.41, "learning_rate": 0.000193945922360219, "loss": 0.4337, "step": 292}, {"epoch": 0.42, "learning_rate": 0.00019389324835320646, "loss": 0.4693, "step": 293}, {"epoch": 0.42, "learning_rate": 0.0001938403534063108, "loss": 0.4474, "step": 294}, {"epoch": 0.42, "learning_rate": 0.000193787237643999, "loss": 0.4901, "step": 295}, {"epoch": 0.42, "learning_rate": 0.00019373390119125752, "loss": 0.455, "step": 296}, {"epoch": 0.42, "learning_rate": 0.00019368034417359216, "loss": 0.4534, "step": 297}, {"epoch": 0.42, "learning_rate": 0.00019362656671702784, "loss": 0.511, "step": 298}, {"epoch": 0.42, "learning_rate": 0.00019357256894810804, "loss": 0.4405, "step": 299}, {"epoch": 0.43, "learning_rate": 0.00019351835099389478, "loss": 0.5082, "step": 300}, {"epoch": 0.43, "learning_rate": 0.00019346391298196808, "loss": 0.4824, "step": 301}, {"epoch": 0.43, "learning_rate": 0.00019340925504042592, "loss": 0.4905, "step": 302}, {"epoch": 0.43, "learning_rate": 0.00019335437729788363, "loss": 0.4913, "step": 303}, {"epoch": 0.43, "learning_rate": 0.0001932992798834739, "loss": 0.425, "step": 304}, {"epoch": 0.43, "learning_rate": 0.00019324396292684623, "loss": 0.446, "step": 305}, {"epoch": 0.43, "learning_rate": 0.00019318842655816683, "loss": 0.4836, "step": 306}, {"epoch": 0.44, "learning_rate": 0.00019313267090811804, "loss": 0.4826, "step": 307}, {"epoch": 0.44, "learning_rate": 0.00019307669610789838, "loss": 0.4565, "step": 308}, {"epoch": 0.44, "learning_rate": 0.00019302050228922193, "loss": 0.4743, "step": 309}, {"epoch": 0.44, "learning_rate": 0.00019296408958431816, "loss": 0.4451, "step": 310}, {"epoch": 0.44, "learning_rate": 0.0001929074581259316, "loss": 0.4836, "step": 311}, {"epoch": 0.44, "learning_rate": 0.00019285060804732158, "loss": 0.4596, "step": 312}, {"epoch": 0.44, "learning_rate": 0.0001927935394822618, "loss": 0.4414, "step": 313}, {"epoch": 0.45, "learning_rate": 0.0001927362525650401, "loss": 0.4205, "step": 314}, {"epoch": 0.45, "learning_rate": 0.00019267874743045818, "loss": 0.467, "step": 315}, {"epoch": 0.45, "learning_rate": 0.00019262102421383116, "loss": 0.4648, "step": 316}, {"epoch": 0.45, "learning_rate": 0.0001925630830509873, "loss": 0.4922, "step": 317}, {"epoch": 0.45, "learning_rate": 0.00019250492407826776, "loss": 0.4716, "step": 318}, {"epoch": 0.45, "learning_rate": 0.0001924465474325263, "loss": 0.4912, "step": 319}, {"epoch": 0.45, "learning_rate": 0.0001923879532511287, "loss": 0.465, "step": 320}, {"epoch": 0.46, "learning_rate": 0.00019232914167195277, "loss": 0.4518, "step": 321}, {"epoch": 0.46, "learning_rate": 0.00019227011283338787, "loss": 0.4569, "step": 322}, {"epoch": 0.46, "learning_rate": 0.00019221086687433453, "loss": 0.4797, "step": 323}, {"epoch": 0.46, "learning_rate": 0.0001921514039342042, "loss": 0.4831, "step": 324}, {"epoch": 0.46, "learning_rate": 0.00019209172415291897, "loss": 0.5159, "step": 325}, {"epoch": 0.46, "learning_rate": 0.0001920318276709111, "loss": 0.4666, "step": 326}, {"epoch": 0.46, "learning_rate": 0.00019197171462912276, "loss": 0.4557, "step": 327}, {"epoch": 0.47, "learning_rate": 0.0001919113851690058, "loss": 0.4493, "step": 328}, {"epoch": 0.47, "learning_rate": 0.00019185083943252122, "loss": 0.5566, "step": 329}, {"epoch": 0.47, "learning_rate": 0.00019179007756213906, "loss": 0.4992, "step": 330}, {"epoch": 0.47, "learning_rate": 0.0001917290997008378, "loss": 0.5923, "step": 331}, {"epoch": 0.47, "learning_rate": 0.00019166790599210428, "loss": 0.4521, "step": 332}, {"epoch": 0.47, "learning_rate": 0.00019160649657993316, "loss": 0.452, "step": 333}, {"epoch": 0.47, "learning_rate": 0.0001915448716088268, "loss": 0.4689, "step": 334}, {"epoch": 0.48, "learning_rate": 0.00019148303122379462, "loss": 0.469, "step": 335}, {"epoch": 0.48, "learning_rate": 0.00019142097557035308, "loss": 0.4638, "step": 336}, {"epoch": 0.48, "learning_rate": 0.0001913587047945251, "loss": 0.6187, "step": 337}, {"epoch": 0.48, "learning_rate": 0.00019129621904283983, "loss": 0.4396, "step": 338}, {"epoch": 0.48, "learning_rate": 0.00019123351846233227, "loss": 0.4665, "step": 339}, {"epoch": 0.48, "learning_rate": 0.000191170603200543, "loss": 0.4498, "step": 340}, {"epoch": 0.48, "learning_rate": 0.00019110747340551765, "loss": 0.483, "step": 341}, {"epoch": 0.49, "learning_rate": 0.00019104412922580673, "loss": 0.4782, "step": 342}, {"epoch": 0.49, "learning_rate": 0.00019098057081046524, "loss": 0.4919, "step": 343}, {"epoch": 0.49, "learning_rate": 0.00019091679830905226, "loss": 0.4522, "step": 344}, {"epoch": 0.49, "learning_rate": 0.00019085281187163061, "loss": 0.4862, "step": 345}, {"epoch": 0.49, "learning_rate": 0.00019078861164876663, "loss": 0.4256, "step": 346}, {"epoch": 0.49, "learning_rate": 0.0001907241977915296, "loss": 0.4699, "step": 347}, {"epoch": 0.49, "learning_rate": 0.00019065957045149155, "loss": 0.4685, "step": 348}, {"epoch": 0.5, "learning_rate": 0.00019059472978072685, "loss": 0.4591, "step": 349}, {"epoch": 0.5, "learning_rate": 0.0001905296759318119, "loss": 0.5427, "step": 350}, {"epoch": 0.5, "learning_rate": 0.00019046440905782464, "loss": 0.4739, "step": 351}, {"epoch": 0.5, "learning_rate": 0.00019039892931234435, "loss": 0.455, "step": 352}, {"epoch": 0.5, "learning_rate": 0.00019033323684945118, "loss": 0.4879, "step": 353}, {"epoch": 0.5, "learning_rate": 0.0001902673318237259, "loss": 0.4165, "step": 354}, {"epoch": 0.5, "learning_rate": 0.00019020121439024932, "loss": 0.4527, "step": 355}, {"epoch": 0.51, "learning_rate": 0.0001901348847046022, "loss": 0.5734, "step": 356}, {"epoch": 0.51, "learning_rate": 0.00019006834292286472, "loss": 0.4861, "step": 357}, {"epoch": 0.51, "learning_rate": 0.00019000158920161604, "loss": 0.4878, "step": 358}, {"epoch": 0.51, "learning_rate": 0.00018993462369793415, "loss": 0.4038, "step": 359}, {"epoch": 0.51, "learning_rate": 0.0001898674465693954, "loss": 0.4888, "step": 360}, {"epoch": 0.51, "learning_rate": 0.000189800057974074, "loss": 0.4435, "step": 361}, {"epoch": 0.51, "learning_rate": 0.00018973245807054183, "loss": 0.477, "step": 362}, {"epoch": 0.52, "learning_rate": 0.00018966464701786803, "loss": 0.4694, "step": 363}, {"epoch": 0.52, "learning_rate": 0.00018959662497561853, "loss": 0.5219, "step": 364}, {"epoch": 0.52, "learning_rate": 0.00018952839210385576, "loss": 0.4658, "step": 365}, {"epoch": 0.52, "learning_rate": 0.00018945994856313828, "loss": 0.4935, "step": 366}, {"epoch": 0.52, "learning_rate": 0.00018939129451452035, "loss": 0.463, "step": 367}, {"epoch": 0.52, "learning_rate": 0.00018932243011955154, "loss": 0.4348, "step": 368}, {"epoch": 0.52, "learning_rate": 0.00018925335554027646, "loss": 0.5122, "step": 369}, {"epoch": 0.53, "learning_rate": 0.0001891840709392343, "loss": 0.4088, "step": 370}, {"epoch": 0.53, "learning_rate": 0.00018911457647945832, "loss": 0.5123, "step": 371}, {"epoch": 0.53, "learning_rate": 0.0001890448723244758, "loss": 0.4706, "step": 372}, {"epoch": 0.53, "learning_rate": 0.0001889749586383073, "loss": 0.4223, "step": 373}, {"epoch": 0.53, "learning_rate": 0.00018890483558546648, "loss": 0.4806, "step": 374}, {"epoch": 0.53, "learning_rate": 0.00018883450333095965, "loss": 0.438, "step": 375}, {"epoch": 0.53, "learning_rate": 0.0001887639620402854, "loss": 0.4521, "step": 376}, {"epoch": 0.54, "learning_rate": 0.00018869321187943422, "loss": 0.4752, "step": 377}, {"epoch": 0.54, "learning_rate": 0.00018862225301488806, "loss": 0.4203, "step": 378}, {"epoch": 0.54, "learning_rate": 0.00018855108561362, "loss": 0.4235, "step": 379}, {"epoch": 0.54, "learning_rate": 0.0001884797098430938, "loss": 0.503, "step": 380}, {"epoch": 0.54, "learning_rate": 0.00018840812587126352, "loss": 0.4616, "step": 381}, {"epoch": 0.54, "learning_rate": 0.00018833633386657316, "loss": 0.4529, "step": 382}, {"epoch": 0.54, "learning_rate": 0.0001882643339979563, "loss": 0.4528, "step": 383}, {"epoch": 0.55, "learning_rate": 0.0001881921264348355, "loss": 0.461, "step": 384}, {"epoch": 0.55, "learning_rate": 0.0001881197113471222, "loss": 0.4955, "step": 385}, {"epoch": 0.55, "learning_rate": 0.00018804708890521609, "loss": 0.4452, "step": 386}, {"epoch": 0.55, "learning_rate": 0.00018797425928000476, "loss": 0.4247, "step": 387}, {"epoch": 0.55, "learning_rate": 0.00018790122264286335, "loss": 0.4939, "step": 388}, {"epoch": 0.55, "learning_rate": 0.0001878279791656542, "loss": 0.4524, "step": 389}, {"epoch": 0.55, "learning_rate": 0.00018775452902072614, "loss": 0.4602, "step": 390}, {"epoch": 0.56, "learning_rate": 0.00018768087238091457, "loss": 0.4751, "step": 391}, {"epoch": 0.56, "learning_rate": 0.00018760700941954065, "loss": 0.521, "step": 392}, {"epoch": 0.56, "learning_rate": 0.0001875329403104111, "loss": 0.4092, "step": 393}, {"epoch": 0.56, "learning_rate": 0.00018745866522781762, "loss": 0.4534, "step": 394}, {"epoch": 0.56, "learning_rate": 0.0001873841843465367, "loss": 0.465, "step": 395}, {"epoch": 0.56, "learning_rate": 0.00018730949784182902, "loss": 0.457, "step": 396}, {"epoch": 0.56, "learning_rate": 0.00018723460588943914, "loss": 0.4319, "step": 397}, {"epoch": 0.57, "learning_rate": 0.0001871595086655951, "loss": 0.4778, "step": 398}, {"epoch": 0.57, "learning_rate": 0.0001870842063470079, "loss": 0.4857, "step": 399}, {"epoch": 0.57, "learning_rate": 0.00018700869911087115, "loss": 0.4247, "step": 400}, {"epoch": 0.57, "learning_rate": 0.0001869329871348607, "loss": 0.4665, "step": 401}, {"epoch": 0.57, "learning_rate": 0.0001868570705971341, "loss": 0.4857, "step": 402}, {"epoch": 0.57, "learning_rate": 0.00018678094967633033, "loss": 0.4664, "step": 403}, {"epoch": 0.57, "learning_rate": 0.00018670462455156928, "loss": 0.4938, "step": 404}, {"epoch": 0.58, "learning_rate": 0.0001866280954024513, "loss": 0.4675, "step": 405}, {"epoch": 0.58, "learning_rate": 0.00018655136240905692, "loss": 0.4623, "step": 406}, {"epoch": 0.58, "learning_rate": 0.00018647442575194623, "loss": 0.4516, "step": 407}, {"epoch": 0.58, "learning_rate": 0.0001863972856121587, "loss": 0.3981, "step": 408}, {"epoch": 0.58, "learning_rate": 0.00018631994217121242, "loss": 0.4271, "step": 409}, {"epoch": 0.58, "learning_rate": 0.00018624239561110408, "loss": 0.4125, "step": 410}, {"epoch": 0.58, "learning_rate": 0.00018616464611430816, "loss": 0.4884, "step": 411}, {"epoch": 0.59, "learning_rate": 0.00018608669386377673, "loss": 0.4218, "step": 412}, {"epoch": 0.59, "learning_rate": 0.000186008539042939, "loss": 0.4757, "step": 413}, {"epoch": 0.59, "learning_rate": 0.00018593018183570085, "loss": 0.4201, "step": 414}, {"epoch": 0.59, "learning_rate": 0.0001858516224264443, "loss": 0.4402, "step": 415}, {"epoch": 0.59, "learning_rate": 0.00018577286100002723, "loss": 0.4789, "step": 416}, {"epoch": 0.59, "learning_rate": 0.0001856938977417829, "loss": 0.4673, "step": 417}, {"epoch": 0.59, "learning_rate": 0.00018561473283751947, "loss": 0.4476, "step": 418}, {"epoch": 0.6, "learning_rate": 0.0001855353664735196, "loss": 0.4431, "step": 419}, {"epoch": 0.6, "learning_rate": 0.00018545579883654005, "loss": 0.4942, "step": 420}, {"epoch": 0.6, "learning_rate": 0.00018537603011381114, "loss": 0.4461, "step": 421}, {"epoch": 0.6, "learning_rate": 0.00018529606049303636, "loss": 0.4979, "step": 422}, {"epoch": 0.6, "learning_rate": 0.00018521589016239198, "loss": 0.463, "step": 423}, {"epoch": 0.6, "learning_rate": 0.00018513551931052653, "loss": 0.4645, "step": 424}, {"epoch": 0.6, "learning_rate": 0.00018505494812656034, "loss": 0.4075, "step": 425}, {"epoch": 0.61, "learning_rate": 0.00018497417680008525, "loss": 0.4329, "step": 426}, {"epoch": 0.61, "learning_rate": 0.000184893205521164, "loss": 0.4695, "step": 427}, {"epoch": 0.61, "learning_rate": 0.00018481203448032974, "loss": 0.4838, "step": 428}, {"epoch": 0.61, "learning_rate": 0.00018473066386858584, "loss": 0.4408, "step": 429}, {"epoch": 0.61, "learning_rate": 0.0001846490938774052, "loss": 0.4197, "step": 430}, {"epoch": 0.61, "learning_rate": 0.00018456732469872994, "loss": 0.439, "step": 431}, {"epoch": 0.61, "learning_rate": 0.00018448535652497073, "loss": 0.458, "step": 432}, {"epoch": 0.62, "learning_rate": 0.00018440318954900665, "loss": 0.4499, "step": 433}, {"epoch": 0.62, "learning_rate": 0.00018432082396418456, "loss": 0.4741, "step": 434}, {"epoch": 0.62, "learning_rate": 0.0001842382599643186, "loss": 0.4451, "step": 435}, {"epoch": 0.62, "learning_rate": 0.00018415549774368985, "loss": 0.4426, "step": 436}, {"epoch": 0.62, "learning_rate": 0.00018407253749704584, "loss": 0.4665, "step": 437}, {"epoch": 0.62, "learning_rate": 0.00018398937941959998, "loss": 0.4931, "step": 438}, {"epoch": 0.62, "learning_rate": 0.00018390602370703128, "loss": 0.4571, "step": 439}, {"epoch": 0.62, "learning_rate": 0.0001838224705554838, "loss": 0.4264, "step": 440}, {"epoch": 0.63, "learning_rate": 0.00018373872016156623, "loss": 0.4644, "step": 441}, {"epoch": 0.63, "learning_rate": 0.00018365477272235122, "loss": 0.4795, "step": 442}, {"epoch": 0.63, "learning_rate": 0.00018357062843537528, "loss": 0.4284, "step": 443}, {"epoch": 0.63, "learning_rate": 0.000183486287498638, "loss": 0.4793, "step": 444}, {"epoch": 0.63, "learning_rate": 0.00018340175011060183, "loss": 0.4722, "step": 445}, {"epoch": 0.63, "learning_rate": 0.00018331701647019132, "loss": 0.5024, "step": 446}, {"epoch": 0.63, "learning_rate": 0.00018323208677679297, "loss": 0.4455, "step": 447}, {"epoch": 0.64, "learning_rate": 0.00018314696123025454, "loss": 0.4505, "step": 448}, {"epoch": 0.64, "learning_rate": 0.00018306164003088464, "loss": 0.4523, "step": 449}, {"epoch": 0.64, "learning_rate": 0.0001829761233794523, "loss": 0.4436, "step": 450}, {"epoch": 0.64, "learning_rate": 0.0001828904114771865, "loss": 0.479, "step": 451}, {"epoch": 0.64, "learning_rate": 0.00018280450452577558, "loss": 0.4258, "step": 452}, {"epoch": 0.64, "learning_rate": 0.0001827184027273669, "loss": 0.5084, "step": 453}, {"epoch": 0.64, "learning_rate": 0.00018263210628456636, "loss": 0.3654, "step": 454}, {"epoch": 0.65, "learning_rate": 0.00018254561540043777, "loss": 0.4657, "step": 455}, {"epoch": 0.65, "learning_rate": 0.00018245893027850254, "loss": 0.4425, "step": 456}, {"epoch": 0.65, "learning_rate": 0.00018237205112273913, "loss": 0.4492, "step": 457}, {"epoch": 0.65, "learning_rate": 0.00018228497813758265, "loss": 0.4533, "step": 458}, {"epoch": 0.65, "learning_rate": 0.00018219771152792416, "loss": 0.4402, "step": 459}, {"epoch": 0.65, "learning_rate": 0.00018211025149911047, "loss": 0.493, "step": 460}, {"epoch": 0.65, "learning_rate": 0.00018202259825694347, "loss": 0.4115, "step": 461}, {"epoch": 0.66, "learning_rate": 0.00018193475200767968, "loss": 0.4405, "step": 462}, {"epoch": 0.66, "learning_rate": 0.00018184671295802987, "loss": 0.4409, "step": 463}, {"epoch": 0.66, "learning_rate": 0.00018175848131515837, "loss": 0.4532, "step": 464}, {"epoch": 0.66, "learning_rate": 0.0001816700572866828, "loss": 0.4806, "step": 465}, {"epoch": 0.66, "learning_rate": 0.0001815814410806734, "loss": 0.4715, "step": 466}, {"epoch": 0.66, "learning_rate": 0.0001814926329056527, "loss": 0.4594, "step": 467}, {"epoch": 0.66, "learning_rate": 0.00018140363297059487, "loss": 0.4643, "step": 468}, {"epoch": 0.67, "learning_rate": 0.00018131444148492535, "loss": 0.4859, "step": 469}, {"epoch": 0.67, "learning_rate": 0.0001812250586585204, "loss": 0.4558, "step": 470}, {"epoch": 0.67, "learning_rate": 0.0001811354847017064, "loss": 0.4371, "step": 471}, {"epoch": 0.67, "learning_rate": 0.0001810457198252595, "loss": 0.455, "step": 472}, {"epoch": 0.67, "learning_rate": 0.00018095576424040512, "loss": 0.4637, "step": 473}, {"epoch": 0.67, "learning_rate": 0.0001808656181588175, "loss": 0.4467, "step": 474}, {"epoch": 0.67, "learning_rate": 0.00018077528179261904, "loss": 0.4424, "step": 475}, {"epoch": 0.68, "learning_rate": 0.00018068475535437995, "loss": 0.4285, "step": 476}, {"epoch": 0.68, "learning_rate": 0.00018059403905711766, "loss": 0.4268, "step": 477}, {"epoch": 0.68, "learning_rate": 0.00018050313311429638, "loss": 0.4629, "step": 478}, {"epoch": 0.68, "learning_rate": 0.00018041203773982658, "loss": 0.4742, "step": 479}, {"epoch": 0.68, "learning_rate": 0.00018032075314806448, "loss": 0.4159, "step": 480}, {"epoch": 0.68, "learning_rate": 0.0001802292795538116, "loss": 0.4704, "step": 481}, {"epoch": 0.68, "learning_rate": 0.00018013761717231404, "loss": 0.4822, "step": 482}, {"epoch": 0.69, "learning_rate": 0.0001800457662192623, "loss": 0.507, "step": 483}, {"epoch": 0.69, "learning_rate": 0.00017995372691079052, "loss": 0.4542, "step": 484}, {"epoch": 0.69, "learning_rate": 0.0001798614994634761, "loss": 0.4403, "step": 485}, {"epoch": 0.69, "learning_rate": 0.00017976908409433914, "loss": 0.4284, "step": 486}, {"epoch": 0.69, "learning_rate": 0.0001796764810208419, "loss": 0.447, "step": 487}, {"epoch": 0.69, "learning_rate": 0.00017958369046088837, "loss": 0.4431, "step": 488}, {"epoch": 0.69, "learning_rate": 0.00017949071263282371, "loss": 0.4354, "step": 489}, {"epoch": 0.7, "learning_rate": 0.0001793975477554337, "loss": 0.4625, "step": 490}, {"epoch": 0.7, "learning_rate": 0.00017930419604794437, "loss": 0.4768, "step": 491}, {"epoch": 0.7, "learning_rate": 0.00017921065773002126, "loss": 0.4491, "step": 492}, {"epoch": 0.7, "learning_rate": 0.00017911693302176903, "loss": 0.4644, "step": 493}, {"epoch": 0.7, "learning_rate": 0.00017902302214373102, "loss": 0.4529, "step": 494}, {"epoch": 0.7, "learning_rate": 0.00017892892531688856, "loss": 0.4509, "step": 495}, {"epoch": 0.7, "learning_rate": 0.00017883464276266064, "loss": 0.4286, "step": 496}, {"epoch": 0.71, "learning_rate": 0.00017874017470290317, "loss": 0.4048, "step": 497}, {"epoch": 0.71, "learning_rate": 0.00017864552135990857, "loss": 0.4381, "step": 498}, {"epoch": 0.71, "learning_rate": 0.0001785506829564054, "loss": 0.4189, "step": 499}, {"epoch": 0.71, "learning_rate": 0.00017845565971555754, "loss": 0.4501, "step": 500}, {"epoch": 0.71, "learning_rate": 0.00017836045186096384, "loss": 0.4677, "step": 501}, {"epoch": 0.71, "learning_rate": 0.00017826505961665757, "loss": 0.4617, "step": 502}, {"epoch": 0.71, "learning_rate": 0.00017816948320710597, "loss": 0.4449, "step": 503}, {"epoch": 0.72, "learning_rate": 0.00017807372285720945, "loss": 0.4813, "step": 504}, {"epoch": 0.72, "learning_rate": 0.00017797777879230146, "loss": 0.44, "step": 505}, {"epoch": 0.72, "learning_rate": 0.0001778816512381476, "loss": 0.4505, "step": 506}, {"epoch": 0.72, "learning_rate": 0.00017778534042094533, "loss": 0.4302, "step": 507}, {"epoch": 0.72, "learning_rate": 0.00017768884656732325, "loss": 0.4325, "step": 508}, {"epoch": 0.72, "learning_rate": 0.00017759216990434078, "loss": 0.4797, "step": 509}, {"epoch": 0.72, "learning_rate": 0.0001774953106594874, "loss": 0.4742, "step": 510}, {"epoch": 0.73, "learning_rate": 0.00017739826906068231, "loss": 0.4571, "step": 511}, {"epoch": 0.73, "learning_rate": 0.0001773010453362737, "loss": 0.4343, "step": 512}, {"epoch": 0.73, "learning_rate": 0.00017720363971503847, "loss": 0.4415, "step": 513}, {"epoch": 0.73, "learning_rate": 0.00017710605242618138, "loss": 0.4113, "step": 514}, {"epoch": 0.73, "learning_rate": 0.0001770082836993348, "loss": 0.4455, "step": 515}, {"epoch": 0.73, "learning_rate": 0.000176910333764558, "loss": 0.4252, "step": 516}, {"epoch": 0.73, "learning_rate": 0.00017681220285233656, "loss": 0.3949, "step": 517}, {"epoch": 0.74, "learning_rate": 0.00017671389119358204, "loss": 0.4582, "step": 518}, {"epoch": 0.74, "learning_rate": 0.0001766153990196313, "loss": 0.454, "step": 519}, {"epoch": 0.74, "learning_rate": 0.0001765167265622459, "loss": 0.4582, "step": 520}, {"epoch": 0.74, "learning_rate": 0.0001764178740536117, "loss": 0.4177, "step": 521}, {"epoch": 0.74, "learning_rate": 0.0001763188417263381, "loss": 0.4639, "step": 522}, {"epoch": 0.74, "learning_rate": 0.0001762196298134579, "loss": 0.4606, "step": 523}, {"epoch": 0.74, "learning_rate": 0.0001761202385484262, "loss": 0.5176, "step": 524}, {"epoch": 0.75, "learning_rate": 0.00017602066816512025, "loss": 0.4649, "step": 525}, {"epoch": 0.75, "learning_rate": 0.00017592091889783882, "loss": 0.4851, "step": 526}, {"epoch": 0.75, "learning_rate": 0.00017582099098130153, "loss": 0.4828, "step": 527}, {"epoch": 0.75, "learning_rate": 0.00017572088465064848, "loss": 0.4342, "step": 528}, {"epoch": 0.75, "learning_rate": 0.00017562060014143945, "loss": 0.4098, "step": 529}, {"epoch": 0.75, "learning_rate": 0.00017552013768965368, "loss": 0.4463, "step": 530}, {"epoch": 0.75, "learning_rate": 0.00017541949753168893, "loss": 0.4211, "step": 531}, {"epoch": 0.76, "learning_rate": 0.00017531867990436126, "loss": 0.4405, "step": 532}, {"epoch": 0.76, "learning_rate": 0.00017521768504490427, "loss": 0.4318, "step": 533}, {"epoch": 0.76, "learning_rate": 0.00017511651319096868, "loss": 0.4462, "step": 534}, {"epoch": 0.76, "learning_rate": 0.0001750151645806215, "loss": 0.5177, "step": 535}, {"epoch": 0.76, "learning_rate": 0.00017491363945234593, "loss": 0.4321, "step": 536}, {"epoch": 0.76, "learning_rate": 0.00017481193804504036, "loss": 0.3963, "step": 537}, {"epoch": 0.76, "learning_rate": 0.00017471006059801802, "loss": 0.4681, "step": 538}, {"epoch": 0.77, "learning_rate": 0.0001746080073510064, "loss": 0.4857, "step": 539}, {"epoch": 0.77, "learning_rate": 0.00017450577854414662, "loss": 0.4452, "step": 540}, {"epoch": 0.77, "learning_rate": 0.00017440337441799292, "loss": 0.4741, "step": 541}, {"epoch": 0.77, "learning_rate": 0.00017430079521351218, "loss": 0.4632, "step": 542}, {"epoch": 0.77, "learning_rate": 0.0001741980411720831, "loss": 0.439, "step": 543}, {"epoch": 0.77, "learning_rate": 0.00017409511253549593, "loss": 0.4582, "step": 544}, {"epoch": 0.77, "learning_rate": 0.00017399200954595163, "loss": 0.4306, "step": 545}, {"epoch": 0.78, "learning_rate": 0.0001738887324460615, "loss": 0.5483, "step": 546}, {"epoch": 0.78, "learning_rate": 0.0001737852814788466, "loss": 0.4887, "step": 547}, {"epoch": 0.78, "learning_rate": 0.000173681656887737, "loss": 0.4178, "step": 548}, {"epoch": 0.78, "learning_rate": 0.00017357785891657137, "loss": 0.4463, "step": 549}, {"epoch": 0.78, "learning_rate": 0.00017347388780959637, "loss": 0.466, "step": 550}, {"epoch": 0.78, "learning_rate": 0.00017336974381146605, "loss": 0.4817, "step": 551}, {"epoch": 0.78, "learning_rate": 0.00017326542716724128, "loss": 0.4384, "step": 552}, {"epoch": 0.79, "learning_rate": 0.00017316093812238926, "loss": 0.4909, "step": 553}, {"epoch": 0.79, "learning_rate": 0.00017305627692278276, "loss": 0.4894, "step": 554}, {"epoch": 0.79, "learning_rate": 0.0001729514438146997, "loss": 0.4007, "step": 555}, {"epoch": 0.79, "learning_rate": 0.00017284643904482252, "loss": 0.4482, "step": 556}, {"epoch": 0.79, "learning_rate": 0.00017274126286023758, "loss": 0.4351, "step": 557}, {"epoch": 0.79, "learning_rate": 0.0001726359155084346, "loss": 0.4254, "step": 558}, {"epoch": 0.79, "learning_rate": 0.0001725303972373061, "loss": 0.4956, "step": 559}, {"epoch": 0.8, "learning_rate": 0.00017242470829514672, "loss": 0.4096, "step": 560}, {"epoch": 0.8, "learning_rate": 0.00017231884893065274, "loss": 0.4895, "step": 561}, {"epoch": 0.8, "learning_rate": 0.00017221281939292155, "loss": 0.4549, "step": 562}, {"epoch": 0.8, "learning_rate": 0.00017210661993145081, "loss": 0.4387, "step": 563}, {"epoch": 0.8, "learning_rate": 0.00017200025079613818, "loss": 0.4346, "step": 564}, {"epoch": 0.8, "learning_rate": 0.00017189371223728047, "loss": 0.4233, "step": 565}, {"epoch": 0.8, "learning_rate": 0.00017178700450557317, "loss": 0.4277, "step": 566}, {"epoch": 0.81, "learning_rate": 0.00017168012785210996, "loss": 0.4231, "step": 567}, {"epoch": 0.81, "learning_rate": 0.00017157308252838187, "loss": 0.462, "step": 568}, {"epoch": 0.81, "learning_rate": 0.0001714658687862769, "loss": 0.4627, "step": 569}, {"epoch": 0.81, "learning_rate": 0.00017135848687807937, "loss": 0.4287, "step": 570}, {"epoch": 0.81, "learning_rate": 0.00017125093705646925, "loss": 0.4558, "step": 571}, {"epoch": 0.81, "learning_rate": 0.00017114321957452163, "loss": 0.454, "step": 572}, {"epoch": 0.81, "learning_rate": 0.00017103533468570625, "loss": 0.4784, "step": 573}, {"epoch": 0.82, "learning_rate": 0.00017092728264388657, "loss": 0.4383, "step": 574}, {"epoch": 0.82, "learning_rate": 0.00017081906370331956, "loss": 0.4056, "step": 575}, {"epoch": 0.82, "learning_rate": 0.00017071067811865476, "loss": 0.4805, "step": 576}, {"epoch": 0.82, "learning_rate": 0.000170602126144934, "loss": 0.4377, "step": 577}, {"epoch": 0.82, "learning_rate": 0.0001704934080375905, "loss": 0.46, "step": 578}, {"epoch": 0.82, "learning_rate": 0.0001703845240524485, "loss": 0.4276, "step": 579}, {"epoch": 0.82, "learning_rate": 0.00017027547444572254, "loss": 0.4166, "step": 580}, {"epoch": 0.83, "learning_rate": 0.00017016625947401684, "loss": 0.4613, "step": 581}, {"epoch": 0.83, "learning_rate": 0.00017005687939432486, "loss": 0.3957, "step": 582}, {"epoch": 0.83, "learning_rate": 0.00016994733446402838, "loss": 0.3944, "step": 583}, {"epoch": 0.83, "learning_rate": 0.0001698376249408973, "loss": 0.4443, "step": 584}, {"epoch": 0.83, "learning_rate": 0.00016972775108308868, "loss": 0.4439, "step": 585}, {"epoch": 0.83, "learning_rate": 0.00016961771314914632, "loss": 0.4919, "step": 586}, {"epoch": 0.83, "learning_rate": 0.0001695075113980001, "loss": 0.4287, "step": 587}, {"epoch": 0.84, "learning_rate": 0.0001693971460889654, "loss": 0.5257, "step": 588}, {"epoch": 0.84, "learning_rate": 0.0001692866174817425, "loss": 0.4454, "step": 589}, {"epoch": 0.84, "learning_rate": 0.00016917592583641578, "loss": 0.465, "step": 590}, {"epoch": 0.84, "learning_rate": 0.0001690650714134535, "loss": 0.4698, "step": 591}, {"epoch": 0.84, "learning_rate": 0.0001689540544737067, "loss": 0.4295, "step": 592}, {"epoch": 0.84, "learning_rate": 0.00016884287527840907, "loss": 0.429, "step": 593}, {"epoch": 0.84, "learning_rate": 0.00016873153408917592, "loss": 0.4479, "step": 594}, {"epoch": 0.85, "learning_rate": 0.00016862003116800386, "loss": 0.4298, "step": 595}, {"epoch": 0.85, "learning_rate": 0.00016850836677727003, "loss": 0.4071, "step": 596}, {"epoch": 0.85, "learning_rate": 0.00016839654117973155, "loss": 0.3825, "step": 597}, {"epoch": 0.85, "learning_rate": 0.00016828455463852482, "loss": 0.4215, "step": 598}, {"epoch": 0.85, "learning_rate": 0.000168172407417165, "loss": 0.4908, "step": 599}, {"epoch": 0.85, "learning_rate": 0.00016806009977954532, "loss": 0.4401, "step": 600}, {"epoch": 0.85, "learning_rate": 0.0001679476319899365, "loss": 0.4489, "step": 601}, {"epoch": 0.86, "learning_rate": 0.00016783500431298616, "loss": 0.4765, "step": 602}, {"epoch": 0.86, "learning_rate": 0.00016772221701371804, "loss": 0.4356, "step": 603}, {"epoch": 0.86, "learning_rate": 0.0001676092703575316, "loss": 0.477, "step": 604}, {"epoch": 0.86, "learning_rate": 0.0001674961646102012, "loss": 0.4364, "step": 605}, {"epoch": 0.86, "learning_rate": 0.00016738290003787563, "loss": 0.3916, "step": 606}, {"epoch": 0.86, "learning_rate": 0.0001672694769070773, "loss": 0.4597, "step": 607}, {"epoch": 0.86, "learning_rate": 0.00016715589548470185, "loss": 0.4425, "step": 608}, {"epoch": 0.87, "learning_rate": 0.0001670421560380173, "loss": 0.4282, "step": 609}, {"epoch": 0.87, "learning_rate": 0.0001669282588346636, "loss": 0.464, "step": 610}, {"epoch": 0.87, "learning_rate": 0.00016681420414265189, "loss": 0.4171, "step": 611}, {"epoch": 0.87, "learning_rate": 0.00016669999223036376, "loss": 0.4492, "step": 612}, {"epoch": 0.87, "learning_rate": 0.000166585623366551, "loss": 0.425, "step": 613}, {"epoch": 0.87, "learning_rate": 0.0001664710978203345, "loss": 0.3864, "step": 614}, {"epoch": 0.87, "learning_rate": 0.000166356415861204, "loss": 0.4664, "step": 615}, {"epoch": 0.88, "learning_rate": 0.0001662415777590172, "loss": 0.4473, "step": 616}, {"epoch": 0.88, "learning_rate": 0.00016612658378399922, "loss": 0.4797, "step": 617}, {"epoch": 0.88, "learning_rate": 0.00016601143420674205, "loss": 0.4619, "step": 618}, {"epoch": 0.88, "learning_rate": 0.00016589612929820375, "loss": 0.4391, "step": 619}, {"epoch": 0.88, "learning_rate": 0.00016578066932970787, "loss": 0.4766, "step": 620}, {"epoch": 0.88, "learning_rate": 0.00016566505457294293, "loss": 0.4503, "step": 621}, {"epoch": 0.88, "learning_rate": 0.00016554928529996158, "loss": 0.4594, "step": 622}, {"epoch": 0.88, "learning_rate": 0.0001654333617831801, "loss": 0.4332, "step": 623}, {"epoch": 0.89, "learning_rate": 0.00016531728429537766, "loss": 0.4636, "step": 624}, {"epoch": 0.89, "learning_rate": 0.00016520105310969597, "loss": 0.4079, "step": 625}, {"epoch": 0.89, "learning_rate": 0.0001650846684996381, "loss": 0.4276, "step": 626}, {"epoch": 0.89, "learning_rate": 0.00016496813073906834, "loss": 0.4673, "step": 627}, {"epoch": 0.89, "learning_rate": 0.00016485144010221125, "loss": 0.3915, "step": 628}, {"epoch": 0.89, "learning_rate": 0.0001647345968636512, "loss": 0.4426, "step": 629}, {"epoch": 0.89, "learning_rate": 0.00016461760129833164, "loss": 0.4439, "step": 630}, {"epoch": 0.9, "learning_rate": 0.00016450045368155442, "loss": 0.4521, "step": 631}, {"epoch": 0.9, "learning_rate": 0.00016438315428897915, "loss": 0.3904, "step": 632}, {"epoch": 0.9, "learning_rate": 0.0001642657033966227, "loss": 0.433, "step": 633}, {"epoch": 0.9, "learning_rate": 0.00016414810128085835, "loss": 0.4739, "step": 634}, {"epoch": 0.9, "learning_rate": 0.00016403034821841516, "loss": 0.4387, "step": 635}, {"epoch": 0.9, "learning_rate": 0.00016391244448637758, "loss": 0.4523, "step": 636}, {"epoch": 0.9, "learning_rate": 0.00016379439036218443, "loss": 0.4365, "step": 637}, {"epoch": 0.91, "learning_rate": 0.00016367618612362841, "loss": 0.428, "step": 638}, {"epoch": 0.91, "learning_rate": 0.00016355783204885564, "loss": 0.4267, "step": 639}, {"epoch": 0.91, "learning_rate": 0.00016343932841636456, "loss": 0.4077, "step": 640}, {"epoch": 0.91, "learning_rate": 0.00016332067550500572, "loss": 0.4975, "step": 641}, {"epoch": 0.91, "learning_rate": 0.00016320187359398093, "loss": 0.4763, "step": 642}, {"epoch": 0.91, "learning_rate": 0.00016308292296284246, "loss": 0.4626, "step": 643}, {"epoch": 0.91, "learning_rate": 0.00016296382389149273, "loss": 0.4687, "step": 644}, {"epoch": 0.92, "learning_rate": 0.0001628445766601833, "loss": 0.4464, "step": 645}, {"epoch": 0.92, "learning_rate": 0.00016272518154951442, "loss": 0.441, "step": 646}, {"epoch": 0.92, "learning_rate": 0.00016260563884043435, "loss": 0.4616, "step": 647}, {"epoch": 0.92, "learning_rate": 0.00016248594881423863, "loss": 0.4154, "step": 648}, {"epoch": 0.92, "learning_rate": 0.0001623661117525695, "loss": 0.4394, "step": 649}, {"epoch": 0.92, "learning_rate": 0.000162246127937415, "loss": 0.4268, "step": 650}, {"epoch": 0.92, "learning_rate": 0.00016212599765110878, "loss": 0.4666, "step": 651}, {"epoch": 0.93, "learning_rate": 0.00016200572117632892, "loss": 0.5111, "step": 652}, {"epoch": 0.93, "learning_rate": 0.00016188529879609763, "loss": 0.4185, "step": 653}, {"epoch": 0.93, "learning_rate": 0.0001617647307937804, "loss": 0.4591, "step": 654}, {"epoch": 0.93, "learning_rate": 0.00016164401745308538, "loss": 0.4424, "step": 655}, {"epoch": 0.93, "learning_rate": 0.00016152315905806268, "loss": 0.4703, "step": 656}, {"epoch": 0.93, "learning_rate": 0.00016140215589310387, "loss": 0.4362, "step": 657}, {"epoch": 0.93, "learning_rate": 0.00016128100824294096, "loss": 0.4535, "step": 658}, {"epoch": 0.94, "learning_rate": 0.0001611597163926462, "loss": 0.3978, "step": 659}, {"epoch": 0.94, "learning_rate": 0.00016103828062763095, "loss": 0.4227, "step": 660}, {"epoch": 0.94, "learning_rate": 0.00016091670123364533, "loss": 0.4205, "step": 661}, {"epoch": 0.94, "learning_rate": 0.00016079497849677738, "loss": 0.4922, "step": 662}, {"epoch": 0.94, "learning_rate": 0.00016067311270345246, "loss": 0.495, "step": 663}, {"epoch": 0.94, "learning_rate": 0.00016055110414043257, "loss": 0.4208, "step": 664}, {"epoch": 0.94, "learning_rate": 0.00016042895309481564, "loss": 0.4792, "step": 665}, {"epoch": 0.95, "learning_rate": 0.00016030665985403484, "loss": 0.4339, "step": 666}, {"epoch": 0.95, "learning_rate": 0.00016018422470585802, "loss": 0.4481, "step": 667}, {"epoch": 0.95, "learning_rate": 0.00016006164793838692, "loss": 0.421, "step": 668}, {"epoch": 0.95, "learning_rate": 0.00015993892984005647, "loss": 0.4393, "step": 669}, {"epoch": 0.95, "learning_rate": 0.00015981607069963423, "loss": 0.4285, "step": 670}, {"epoch": 0.95, "learning_rate": 0.00015969307080621964, "loss": 0.4056, "step": 671}, {"epoch": 0.95, "learning_rate": 0.00015956993044924334, "loss": 0.4445, "step": 672}, {"epoch": 0.96, "learning_rate": 0.00015944664991846645, "loss": 0.4333, "step": 673}, {"epoch": 0.96, "learning_rate": 0.00015932322950397998, "loss": 0.433, "step": 674}, {"epoch": 0.96, "learning_rate": 0.0001591996694962041, "loss": 0.4198, "step": 675}, {"epoch": 0.96, "learning_rate": 0.00015907597018588744, "loss": 0.4408, "step": 676}, {"epoch": 0.96, "learning_rate": 0.0001589521318641064, "loss": 0.4394, "step": 677}, {"epoch": 0.96, "learning_rate": 0.00015882815482226454, "loss": 0.4289, "step": 678}, {"epoch": 0.96, "learning_rate": 0.0001587040393520918, "loss": 0.4423, "step": 679}, {"epoch": 0.97, "learning_rate": 0.0001585797857456439, "loss": 0.389, "step": 680}, {"epoch": 0.97, "learning_rate": 0.00015845539429530154, "loss": 0.4046, "step": 681}, {"epoch": 0.97, "learning_rate": 0.00015833086529376983, "loss": 0.4582, "step": 682}, {"epoch": 0.97, "learning_rate": 0.00015820619903407756, "loss": 0.4417, "step": 683}, {"epoch": 0.97, "learning_rate": 0.00015808139580957646, "loss": 0.4271, "step": 684}, {"epoch": 0.97, "learning_rate": 0.00015795645591394058, "loss": 0.4616, "step": 685}, {"epoch": 0.97, "learning_rate": 0.00015783137964116558, "loss": 0.4429, "step": 686}, {"epoch": 0.98, "learning_rate": 0.00015770616728556796, "loss": 0.4487, "step": 687}, {"epoch": 0.98, "learning_rate": 0.00015758081914178456, "loss": 0.398, "step": 688}, {"epoch": 0.98, "learning_rate": 0.0001574553355047716, "loss": 0.4445, "step": 689}, {"epoch": 0.98, "learning_rate": 0.00015732971666980423, "loss": 0.4666, "step": 690}, {"epoch": 0.98, "learning_rate": 0.0001572039629324757, "loss": 0.4086, "step": 691}, {"epoch": 0.98, "learning_rate": 0.00015707807458869674, "loss": 0.4649, "step": 692}, {"epoch": 0.98, "learning_rate": 0.00015695205193469474, "loss": 0.6108, "step": 693}, {"epoch": 0.99, "learning_rate": 0.00015682589526701314, "loss": 0.4175, "step": 694}, {"epoch": 0.99, "learning_rate": 0.00015669960488251089, "loss": 0.4439, "step": 695}, {"epoch": 0.99, "learning_rate": 0.0001565731810783613, "loss": 0.4699, "step": 696}, {"epoch": 0.99, "learning_rate": 0.00015644662415205195, "loss": 0.3735, "step": 697}, {"epoch": 0.99, "learning_rate": 0.00015631993440138342, "loss": 0.4114, "step": 698}, {"epoch": 0.99, "learning_rate": 0.00015619311212446894, "loss": 0.4655, "step": 699}, {"epoch": 0.99, "learning_rate": 0.00015606615761973362, "loss": 0.4351, "step": 700}, {"epoch": 1.0, "learning_rate": 0.00015593907118591362, "loss": 0.4509, "step": 701}, {"epoch": 1.0, "learning_rate": 0.00015581185312205561, "loss": 0.4406, "step": 702}, {"epoch": 1.0, "learning_rate": 0.000155684503727516, "loss": 0.4414, "step": 703}, {"epoch": 1.0, "learning_rate": 0.00015555702330196023, "loss": 0.434, "step": 704}, {"epoch": 1.0, "learning_rate": 0.000155429412145362, "loss": 0.4325, "step": 705}, {"epoch": 1.0, "learning_rate": 0.00015530167055800278, "loss": 0.4252, "step": 706}, {"epoch": 1.0, "learning_rate": 0.00015517379884047076, "loss": 0.3996, "step": 707}, {"epoch": 1.01, "learning_rate": 0.00015504579729366049, "loss": 0.3998, "step": 708}, {"epoch": 1.01, "learning_rate": 0.00015491766621877198, "loss": 0.4178, "step": 709}, {"epoch": 1.01, "learning_rate": 0.00015478940591731, "loss": 0.4122, "step": 710}, {"epoch": 1.01, "learning_rate": 0.0001546610166910835, "loss": 0.4285, "step": 711}, {"epoch": 1.01, "learning_rate": 0.00015453249884220464, "loss": 0.3736, "step": 712}, {"epoch": 1.01, "learning_rate": 0.0001544038526730884, "loss": 0.3948, "step": 713}, {"epoch": 1.01, "learning_rate": 0.0001542750784864516, "loss": 0.423, "step": 714}, {"epoch": 1.02, "learning_rate": 0.00015414617658531237, "loss": 0.4592, "step": 715}, {"epoch": 1.02, "learning_rate": 0.0001540171472729893, "loss": 0.4262, "step": 716}, {"epoch": 1.02, "learning_rate": 0.00015388799085310083, "loss": 0.4042, "step": 717}, {"epoch": 1.02, "learning_rate": 0.00015375870762956458, "loss": 0.397, "step": 718}, {"epoch": 1.02, "learning_rate": 0.00015362929790659633, "loss": 0.3892, "step": 719}, {"epoch": 1.02, "learning_rate": 0.00015349976198870973, "loss": 0.4161, "step": 720}, {"epoch": 1.02, "learning_rate": 0.0001533701001807153, "loss": 0.4406, "step": 721}, {"epoch": 1.03, "learning_rate": 0.00015324031278771981, "loss": 0.4453, "step": 722}, {"epoch": 1.03, "learning_rate": 0.00015311040011512552, "loss": 0.4193, "step": 723}, {"epoch": 1.03, "learning_rate": 0.0001529803624686295, "loss": 0.4083, "step": 724}, {"epoch": 1.03, "learning_rate": 0.00015285020015422287, "loss": 0.3725, "step": 725}, {"epoch": 1.03, "learning_rate": 0.00015271991347819014, "loss": 0.3998, "step": 726}, {"epoch": 1.03, "learning_rate": 0.00015258950274710847, "loss": 0.4367, "step": 727}, {"epoch": 1.03, "learning_rate": 0.00015245896826784688, "loss": 0.399, "step": 728}, {"epoch": 1.04, "learning_rate": 0.00015232831034756565, "loss": 0.4117, "step": 729}, {"epoch": 1.04, "learning_rate": 0.00015219752929371546, "loss": 0.4264, "step": 730}, {"epoch": 1.04, "learning_rate": 0.00015206662541403674, "loss": 0.3732, "step": 731}, {"epoch": 1.04, "learning_rate": 0.00015193559901655897, "loss": 0.3802, "step": 732}, {"epoch": 1.04, "learning_rate": 0.00015180445040959993, "loss": 0.4598, "step": 733}, {"epoch": 1.04, "learning_rate": 0.000151673179901765, "loss": 0.4048, "step": 734}, {"epoch": 1.04, "learning_rate": 0.0001515417878019463, "loss": 0.4352, "step": 735}, {"epoch": 1.05, "learning_rate": 0.00015141027441932216, "loss": 0.3924, "step": 736}, {"epoch": 1.05, "learning_rate": 0.0001512786400633563, "loss": 0.4068, "step": 737}, {"epoch": 1.05, "learning_rate": 0.00015114688504379707, "loss": 0.4051, "step": 738}, {"epoch": 1.05, "learning_rate": 0.00015101500967067667, "loss": 0.4605, "step": 739}, {"epoch": 1.05, "learning_rate": 0.00015088301425431072, "loss": 0.4516, "step": 740}, {"epoch": 1.05, "learning_rate": 0.00015075089910529708, "loss": 0.4011, "step": 741}, {"epoch": 1.05, "learning_rate": 0.00015061866453451556, "loss": 0.4353, "step": 742}, {"epoch": 1.06, "learning_rate": 0.00015048631085312674, "loss": 0.4364, "step": 743}, {"epoch": 1.06, "learning_rate": 0.00015035383837257177, "loss": 0.408, "step": 744}, {"epoch": 1.06, "learning_rate": 0.00015022124740457108, "loss": 0.4459, "step": 745}, {"epoch": 1.06, "learning_rate": 0.0001500885382611241, "loss": 0.4384, "step": 746}, {"epoch": 1.06, "learning_rate": 0.0001499557112545082, "loss": 0.4372, "step": 747}, {"epoch": 1.06, "learning_rate": 0.0001498227666972782, "loss": 0.4279, "step": 748}, {"epoch": 1.06, "learning_rate": 0.00014968970490226546, "loss": 0.4485, "step": 749}, {"epoch": 1.07, "learning_rate": 0.00014955652618257726, "loss": 0.3987, "step": 750}, {"epoch": 1.07, "learning_rate": 0.00014942323085159599, "loss": 0.4479, "step": 751}, {"epoch": 1.07, "learning_rate": 0.00014928981922297842, "loss": 0.4484, "step": 752}, {"epoch": 1.07, "learning_rate": 0.000149156291610655, "loss": 0.4383, "step": 753}, {"epoch": 1.07, "learning_rate": 0.0001490226483288291, "loss": 0.4382, "step": 754}, {"epoch": 1.07, "learning_rate": 0.00014888888969197633, "loss": 0.3951, "step": 755}, {"epoch": 1.07, "learning_rate": 0.0001487550160148436, "loss": 0.4319, "step": 756}, {"epoch": 1.08, "learning_rate": 0.00014862102761244866, "loss": 0.4292, "step": 757}, {"epoch": 1.08, "learning_rate": 0.00014848692480007913, "loss": 0.3879, "step": 758}, {"epoch": 1.08, "learning_rate": 0.00014835270789329187, "loss": 0.4003, "step": 759}, {"epoch": 1.08, "learning_rate": 0.0001482183772079123, "loss": 0.4049, "step": 760}, {"epoch": 1.08, "learning_rate": 0.0001480839330600334, "loss": 0.4168, "step": 761}, {"epoch": 1.08, "learning_rate": 0.0001479493757660153, "loss": 0.446, "step": 762}, {"epoch": 1.08, "learning_rate": 0.00014781470564248432, "loss": 0.443, "step": 763}, {"epoch": 1.09, "learning_rate": 0.00014767992300633223, "loss": 0.4157, "step": 764}, {"epoch": 1.09, "learning_rate": 0.00014754502817471558, "loss": 0.4392, "step": 765}, {"epoch": 1.09, "learning_rate": 0.000147410021465055, "loss": 0.42, "step": 766}, {"epoch": 1.09, "learning_rate": 0.0001472749031950343, "loss": 0.3767, "step": 767}, {"epoch": 1.09, "learning_rate": 0.0001471396736825998, "loss": 0.4104, "step": 768}, {"epoch": 1.09, "learning_rate": 0.00014700433324595956, "loss": 0.4187, "step": 769}, {"epoch": 1.09, "learning_rate": 0.00014686888220358281, "loss": 0.4336, "step": 770}, {"epoch": 1.1, "learning_rate": 0.00014673332087419887, "loss": 0.426, "step": 771}, {"epoch": 1.1, "learning_rate": 0.00014659764957679661, "loss": 0.4437, "step": 772}, {"epoch": 1.1, "learning_rate": 0.0001464618686306238, "loss": 0.3988, "step": 773}, {"epoch": 1.1, "learning_rate": 0.00014632597835518603, "loss": 0.4154, "step": 774}, {"epoch": 1.1, "learning_rate": 0.0001461899790702463, "loss": 0.4045, "step": 775}, {"epoch": 1.1, "learning_rate": 0.000146053871095824, "loss": 0.39, "step": 776}, {"epoch": 1.1, "learning_rate": 0.0001459176547521944, "loss": 0.3987, "step": 777}, {"epoch": 1.11, "learning_rate": 0.00014578133035988774, "loss": 0.5726, "step": 778}, {"epoch": 1.11, "learning_rate": 0.0001456448982396884, "loss": 0.4045, "step": 779}, {"epoch": 1.11, "learning_rate": 0.0001455083587126344, "loss": 0.4001, "step": 780}, {"epoch": 1.11, "learning_rate": 0.0001453717121000164, "loss": 0.4144, "step": 781}, {"epoch": 1.11, "learning_rate": 0.0001452349587233771, "loss": 0.4408, "step": 782}, {"epoch": 1.11, "learning_rate": 0.0001450980989045104, "loss": 0.3858, "step": 783}, {"epoch": 1.11, "learning_rate": 0.00014496113296546067, "loss": 0.3967, "step": 784}, {"epoch": 1.12, "learning_rate": 0.000144824061228522, "loss": 0.391, "step": 785}, {"epoch": 1.12, "learning_rate": 0.00014468688401623745, "loss": 0.4353, "step": 786}, {"epoch": 1.12, "learning_rate": 0.00014454960165139816, "loss": 0.3972, "step": 787}, {"epoch": 1.12, "learning_rate": 0.00014441221445704293, "loss": 0.4304, "step": 788}, {"epoch": 1.12, "learning_rate": 0.00014427472275645701, "loss": 0.4106, "step": 789}, {"epoch": 1.12, "learning_rate": 0.00014413712687317166, "loss": 0.432, "step": 790}, {"epoch": 1.12, "learning_rate": 0.00014399942713096332, "loss": 0.389, "step": 791}, {"epoch": 1.12, "learning_rate": 0.00014386162385385278, "loss": 0.4027, "step": 792}, {"epoch": 1.13, "learning_rate": 0.00014372371736610443, "loss": 0.4072, "step": 793}, {"epoch": 1.13, "learning_rate": 0.00014358570799222556, "loss": 0.4049, "step": 794}, {"epoch": 1.13, "learning_rate": 0.00014344759605696558, "loss": 0.4175, "step": 795}, {"epoch": 1.13, "learning_rate": 0.0001433093818853152, "loss": 0.4245, "step": 796}, {"epoch": 1.13, "learning_rate": 0.00014317106580250574, "loss": 0.4201, "step": 797}, {"epoch": 1.13, "learning_rate": 0.00014303264813400826, "loss": 0.3911, "step": 798}, {"epoch": 1.13, "learning_rate": 0.00014289412920553296, "loss": 0.4039, "step": 799}, {"epoch": 1.14, "learning_rate": 0.00014275550934302823, "loss": 0.4192, "step": 800}, {"epoch": 1.14, "learning_rate": 0.00014261678887267997, "loss": 0.4243, "step": 801}, {"epoch": 1.14, "learning_rate": 0.00014247796812091087, "loss": 0.3688, "step": 802}, {"epoch": 1.14, "learning_rate": 0.0001423390474143796, "loss": 0.4015, "step": 803}, {"epoch": 1.14, "learning_rate": 0.00014220002707997998, "loss": 0.4013, "step": 804}, {"epoch": 1.14, "learning_rate": 0.00014206090744484025, "loss": 0.4671, "step": 805}, {"epoch": 1.14, "learning_rate": 0.00014192168883632239, "loss": 0.4071, "step": 806}, {"epoch": 1.15, "learning_rate": 0.00014178237158202122, "loss": 0.4279, "step": 807}, {"epoch": 1.15, "learning_rate": 0.00014164295600976374, "loss": 0.4615, "step": 808}, {"epoch": 1.15, "learning_rate": 0.0001415034424476082, "loss": 0.449, "step": 809}, {"epoch": 1.15, "learning_rate": 0.00014136383122384348, "loss": 0.4099, "step": 810}, {"epoch": 1.15, "learning_rate": 0.0001412241226669883, "loss": 0.3829, "step": 811}, {"epoch": 1.15, "learning_rate": 0.0001410843171057904, "loss": 0.4473, "step": 812}, {"epoch": 1.15, "learning_rate": 0.00014094441486922575, "loss": 0.4383, "step": 813}, {"epoch": 1.16, "learning_rate": 0.00014080441628649788, "loss": 0.3896, "step": 814}, {"epoch": 1.16, "learning_rate": 0.0001406643216870369, "loss": 0.4177, "step": 815}, {"epoch": 1.16, "learning_rate": 0.000140524131400499, "loss": 0.4231, "step": 816}, {"epoch": 1.16, "learning_rate": 0.00014038384575676543, "loss": 0.4516, "step": 817}, {"epoch": 1.16, "learning_rate": 0.00014024346508594186, "loss": 0.4104, "step": 818}, {"epoch": 1.16, "learning_rate": 0.00014010298971835758, "loss": 0.4053, "step": 819}, {"epoch": 1.16, "learning_rate": 0.0001399624199845647, "loss": 0.4291, "step": 820}, {"epoch": 1.17, "learning_rate": 0.00013982175621533738, "loss": 0.3989, "step": 821}, {"epoch": 1.17, "learning_rate": 0.00013968099874167103, "loss": 0.4068, "step": 822}, {"epoch": 1.17, "learning_rate": 0.00013954014789478164, "loss": 0.459, "step": 823}, {"epoch": 1.17, "learning_rate": 0.00013939920400610483, "loss": 0.359, "step": 824}, {"epoch": 1.17, "learning_rate": 0.00013925816740729515, "loss": 0.4649, "step": 825}, {"epoch": 1.17, "learning_rate": 0.0001391170384302254, "loss": 0.382, "step": 826}, {"epoch": 1.17, "learning_rate": 0.00013897581740698563, "loss": 0.4205, "step": 827}, {"epoch": 1.18, "learning_rate": 0.00013883450466988263, "loss": 0.4317, "step": 828}, {"epoch": 1.18, "learning_rate": 0.00013869310055143887, "loss": 0.4234, "step": 829}, {"epoch": 1.18, "learning_rate": 0.00013855160538439192, "loss": 0.4281, "step": 830}, {"epoch": 1.18, "learning_rate": 0.00013841001950169353, "loss": 0.3767, "step": 831}, {"epoch": 1.18, "learning_rate": 0.000138268343236509, "loss": 0.3827, "step": 832}, {"epoch": 1.18, "learning_rate": 0.00013812657692221624, "loss": 0.4484, "step": 833}, {"epoch": 1.18, "learning_rate": 0.00013798472089240514, "loss": 0.4008, "step": 834}, {"epoch": 1.19, "learning_rate": 0.00013784277548087656, "loss": 0.4308, "step": 835}, {"epoch": 1.19, "learning_rate": 0.00013770074102164182, "loss": 0.4361, "step": 836}, {"epoch": 1.19, "learning_rate": 0.00013755861784892174, "loss": 0.402, "step": 837}, {"epoch": 1.19, "learning_rate": 0.00013741640629714582, "loss": 0.4096, "step": 838}, {"epoch": 1.19, "learning_rate": 0.00013727410670095157, "loss": 0.3915, "step": 839}, {"epoch": 1.19, "learning_rate": 0.00013713171939518378, "loss": 0.3981, "step": 840}, {"epoch": 1.19, "learning_rate": 0.00013698924471489344, "loss": 0.4532, "step": 841}, {"epoch": 1.2, "learning_rate": 0.00013684668299533725, "loss": 0.3731, "step": 842}, {"epoch": 1.2, "learning_rate": 0.00013670403457197674, "loss": 0.4293, "step": 843}, {"epoch": 1.2, "learning_rate": 0.0001365612997804774, "loss": 0.3686, "step": 844}, {"epoch": 1.2, "learning_rate": 0.00013641847895670797, "loss": 0.3929, "step": 845}, {"epoch": 1.2, "learning_rate": 0.0001362755724367397, "loss": 0.406, "step": 846}, {"epoch": 1.2, "learning_rate": 0.00013613258055684543, "loss": 0.4374, "step": 847}, {"epoch": 1.2, "learning_rate": 0.00013598950365349883, "loss": 0.3927, "step": 848}, {"epoch": 1.21, "learning_rate": 0.00013584634206337365, "loss": 0.4987, "step": 849}, {"epoch": 1.21, "learning_rate": 0.00013570309612334302, "loss": 0.3969, "step": 850}, {"epoch": 1.21, "learning_rate": 0.0001355597661704784, "loss": 0.3967, "step": 851}, {"epoch": 1.21, "learning_rate": 0.00013541635254204904, "loss": 0.3941, "step": 852}, {"epoch": 1.21, "learning_rate": 0.00013527285557552108, "loss": 0.4225, "step": 853}, {"epoch": 1.21, "learning_rate": 0.00013512927560855673, "loss": 0.3797, "step": 854}, {"epoch": 1.21, "learning_rate": 0.0001349856129790135, "loss": 0.3962, "step": 855}, {"epoch": 1.22, "learning_rate": 0.00013484186802494345, "loss": 0.3833, "step": 856}, {"epoch": 1.22, "learning_rate": 0.0001346980410845924, "loss": 0.4169, "step": 857}, {"epoch": 1.22, "learning_rate": 0.00013455413249639892, "loss": 0.3726, "step": 858}, {"epoch": 1.22, "learning_rate": 0.00013441014259899393, "loss": 0.4145, "step": 859}, {"epoch": 1.22, "learning_rate": 0.00013426607173119945, "loss": 0.4359, "step": 860}, {"epoch": 1.22, "learning_rate": 0.00013412192023202826, "loss": 0.3711, "step": 861}, {"epoch": 1.22, "learning_rate": 0.0001339776884406827, "loss": 0.3983, "step": 862}, {"epoch": 1.23, "learning_rate": 0.00013383337669655414, "loss": 0.4237, "step": 863}, {"epoch": 1.23, "learning_rate": 0.000133688985339222, "loss": 0.4024, "step": 864}, {"epoch": 1.23, "learning_rate": 0.00013354451470845317, "loss": 0.4493, "step": 865}, {"epoch": 1.23, "learning_rate": 0.00013339996514420097, "loss": 0.4122, "step": 866}, {"epoch": 1.23, "learning_rate": 0.00013325533698660443, "loss": 0.3898, "step": 867}, {"epoch": 1.23, "learning_rate": 0.00013311063057598764, "loss": 0.4276, "step": 868}, {"epoch": 1.23, "learning_rate": 0.00013296584625285878, "loss": 0.4249, "step": 869}, {"epoch": 1.24, "learning_rate": 0.00013282098435790925, "loss": 0.41, "step": 870}, {"epoch": 1.24, "learning_rate": 0.00013267604523201318, "loss": 0.4588, "step": 871}, {"epoch": 1.24, "learning_rate": 0.0001325310292162263, "loss": 0.421, "step": 872}, {"epoch": 1.24, "learning_rate": 0.00013238593665178532, "loss": 0.3721, "step": 873}, {"epoch": 1.24, "learning_rate": 0.00013224076788010701, "loss": 0.3855, "step": 874}, {"epoch": 1.24, "learning_rate": 0.00013209552324278755, "loss": 0.4029, "step": 875}, {"epoch": 1.24, "learning_rate": 0.00013195020308160157, "loss": 0.3955, "step": 876}, {"epoch": 1.25, "learning_rate": 0.0001318048077385015, "loss": 0.4154, "step": 877}, {"epoch": 1.25, "learning_rate": 0.0001316593375556166, "loss": 0.3752, "step": 878}, {"epoch": 1.25, "learning_rate": 0.00013151379287525226, "loss": 0.4036, "step": 879}, {"epoch": 1.25, "learning_rate": 0.00013136817403988917, "loss": 0.3992, "step": 880}, {"epoch": 1.25, "learning_rate": 0.00013122248139218253, "loss": 0.3875, "step": 881}, {"epoch": 1.25, "learning_rate": 0.00013107671527496116, "loss": 0.4642, "step": 882}, {"epoch": 1.25, "learning_rate": 0.00013093087603122688, "loss": 0.3934, "step": 883}, {"epoch": 1.26, "learning_rate": 0.0001307849640041535, "loss": 0.5233, "step": 884}, {"epoch": 1.26, "learning_rate": 0.0001306389795370861, "loss": 0.3582, "step": 885}, {"epoch": 1.26, "learning_rate": 0.00013049292297354024, "loss": 0.4285, "step": 886}, {"epoch": 1.26, "learning_rate": 0.00013034679465720114, "loss": 0.3679, "step": 887}, {"epoch": 1.26, "learning_rate": 0.00013020059493192284, "loss": 0.3927, "step": 888}, {"epoch": 1.26, "learning_rate": 0.00013005432414172735, "loss": 0.4005, "step": 889}, {"epoch": 1.26, "learning_rate": 0.00012990798263080405, "loss": 0.3745, "step": 890}, {"epoch": 1.27, "learning_rate": 0.00012976157074350863, "loss": 0.3826, "step": 891}, {"epoch": 1.27, "learning_rate": 0.0001296150888243624, "loss": 0.473, "step": 892}, {"epoch": 1.27, "learning_rate": 0.00012946853721805144, "loss": 0.4093, "step": 893}, {"epoch": 1.27, "learning_rate": 0.00012932191626942587, "loss": 0.3669, "step": 894}, {"epoch": 1.27, "learning_rate": 0.00012917522632349893, "loss": 0.4707, "step": 895}, {"epoch": 1.27, "learning_rate": 0.00012902846772544624, "loss": 0.5148, "step": 896}, {"epoch": 1.27, "learning_rate": 0.00012888164082060496, "loss": 0.4013, "step": 897}, {"epoch": 1.28, "learning_rate": 0.00012873474595447297, "loss": 0.43, "step": 898}, {"epoch": 1.28, "learning_rate": 0.0001285877834727081, "loss": 0.3571, "step": 899}, {"epoch": 1.28, "learning_rate": 0.0001284407537211272, "loss": 0.4232, "step": 900}, {"epoch": 1.28, "learning_rate": 0.00012829365704570555, "loss": 0.4251, "step": 901}, {"epoch": 1.28, "learning_rate": 0.00012814649379257583, "loss": 0.3636, "step": 902}, {"epoch": 1.28, "learning_rate": 0.00012799926430802735, "loss": 0.4205, "step": 903}, {"epoch": 1.28, "learning_rate": 0.0001278519689385053, "loss": 0.3794, "step": 904}, {"epoch": 1.29, "learning_rate": 0.00012770460803061, "loss": 0.3792, "step": 905}, {"epoch": 1.29, "learning_rate": 0.00012755718193109582, "loss": 0.4513, "step": 906}, {"epoch": 1.29, "learning_rate": 0.00012740969098687063, "loss": 0.4218, "step": 907}, {"epoch": 1.29, "learning_rate": 0.0001272621355449949, "loss": 0.4323, "step": 908}, {"epoch": 1.29, "learning_rate": 0.0001271145159526808, "loss": 0.3712, "step": 909}, {"epoch": 1.29, "learning_rate": 0.0001269668325572915, "loss": 0.4144, "step": 910}, {"epoch": 1.29, "learning_rate": 0.00012681908570634032, "loss": 0.4078, "step": 911}, {"epoch": 1.3, "learning_rate": 0.00012667127574748986, "loss": 0.4264, "step": 912}, {"epoch": 1.3, "learning_rate": 0.0001265234030285512, "loss": 0.4215, "step": 913}, {"epoch": 1.3, "learning_rate": 0.00012637546789748315, "loss": 0.4241, "step": 914}, {"epoch": 1.3, "learning_rate": 0.00012622747070239137, "loss": 0.4014, "step": 915}, {"epoch": 1.3, "learning_rate": 0.00012607941179152755, "loss": 0.4103, "step": 916}, {"epoch": 1.3, "learning_rate": 0.00012593129151328863, "loss": 0.4083, "step": 917}, {"epoch": 1.3, "learning_rate": 0.0001257831102162159, "loss": 0.3992, "step": 918}, {"epoch": 1.31, "learning_rate": 0.00012563486824899428, "loss": 0.3826, "step": 919}, {"epoch": 1.31, "learning_rate": 0.00012548656596045148, "loss": 0.4221, "step": 920}, {"epoch": 1.31, "learning_rate": 0.00012533820369955704, "loss": 0.3915, "step": 921}, {"epoch": 1.31, "learning_rate": 0.0001251897818154217, "loss": 0.3916, "step": 922}, {"epoch": 1.31, "learning_rate": 0.00012504130065729653, "loss": 0.3814, "step": 923}, {"epoch": 1.31, "learning_rate": 0.00012489276057457206, "loss": 0.4163, "step": 924}, {"epoch": 1.31, "learning_rate": 0.00012474416191677736, "loss": 0.3402, "step": 925}, {"epoch": 1.32, "learning_rate": 0.00012459550503357946, "loss": 0.4056, "step": 926}, {"epoch": 1.32, "learning_rate": 0.00012444679027478244, "loss": 0.4399, "step": 927}, {"epoch": 1.32, "learning_rate": 0.0001242980179903264, "loss": 0.4256, "step": 928}, {"epoch": 1.32, "learning_rate": 0.00012414918853028694, "loss": 0.4423, "step": 929}, {"epoch": 1.32, "learning_rate": 0.00012400030224487415, "loss": 0.4162, "step": 930}, {"epoch": 1.32, "learning_rate": 0.00012385135948443185, "loss": 0.3877, "step": 931}, {"epoch": 1.32, "learning_rate": 0.00012370236059943673, "loss": 0.4243, "step": 932}, {"epoch": 1.33, "learning_rate": 0.00012355330594049753, "loss": 0.4207, "step": 933}, {"epoch": 1.33, "learning_rate": 0.00012340419585835436, "loss": 0.3894, "step": 934}, {"epoch": 1.33, "learning_rate": 0.00012325503070387755, "loss": 0.4306, "step": 935}, {"epoch": 1.33, "learning_rate": 0.00012310581082806713, "loss": 0.427, "step": 936}, {"epoch": 1.33, "learning_rate": 0.0001229565365820519, "loss": 0.3926, "step": 937}, {"epoch": 1.33, "learning_rate": 0.00012280720831708857, "loss": 0.4352, "step": 938}, {"epoch": 1.33, "learning_rate": 0.00012265782638456102, "loss": 0.4006, "step": 939}, {"epoch": 1.34, "learning_rate": 0.00012250839113597928, "loss": 0.4246, "step": 940}, {"epoch": 1.34, "learning_rate": 0.000122358902922979, "loss": 0.4292, "step": 941}, {"epoch": 1.34, "learning_rate": 0.00012220936209732036, "loss": 0.4227, "step": 942}, {"epoch": 1.34, "learning_rate": 0.00012205976901088738, "loss": 0.4245, "step": 943}, {"epoch": 1.34, "learning_rate": 0.00012191012401568698, "loss": 0.4125, "step": 944}, {"epoch": 1.34, "learning_rate": 0.00012176042746384838, "loss": 0.4201, "step": 945}, {"epoch": 1.34, "learning_rate": 0.00012161067970762197, "loss": 0.3993, "step": 946}, {"epoch": 1.35, "learning_rate": 0.0001214608810993787, "loss": 0.4491, "step": 947}, {"epoch": 1.35, "learning_rate": 0.00012131103199160914, "loss": 0.4013, "step": 948}, {"epoch": 1.35, "learning_rate": 0.00012116113273692277, "loss": 0.3495, "step": 949}, {"epoch": 1.35, "learning_rate": 0.00012101118368804698, "loss": 0.4454, "step": 950}, {"epoch": 1.35, "learning_rate": 0.00012086118519782635, "loss": 0.4066, "step": 951}, {"epoch": 1.35, "learning_rate": 0.00012071113761922186, "loss": 0.4444, "step": 952}, {"epoch": 1.35, "learning_rate": 0.00012056104130530993, "loss": 0.4424, "step": 953}, {"epoch": 1.36, "learning_rate": 0.00012041089660928171, "loss": 0.4238, "step": 954}, {"epoch": 1.36, "learning_rate": 0.00012026070388444211, "loss": 0.4258, "step": 955}, {"epoch": 1.36, "learning_rate": 0.0001201104634842092, "loss": 0.4099, "step": 956}, {"epoch": 1.36, "learning_rate": 0.00011996017576211311, "loss": 0.4055, "step": 957}, {"epoch": 1.36, "learning_rate": 0.00011980984107179538, "loss": 0.444, "step": 958}, {"epoch": 1.36, "learning_rate": 0.00011965945976700804, "loss": 0.4517, "step": 959}, {"epoch": 1.36, "learning_rate": 0.00011950903220161285, "loss": 0.4361, "step": 960}, {"epoch": 1.37, "learning_rate": 0.00011935855872958037, "loss": 0.4046, "step": 961}, {"epoch": 1.37, "learning_rate": 0.00011920803970498924, "loss": 0.3581, "step": 962}, {"epoch": 1.37, "learning_rate": 0.00011905747548202528, "loss": 0.3859, "step": 963}, {"epoch": 1.37, "learning_rate": 0.00011890686641498063, "loss": 0.3944, "step": 964}, {"epoch": 1.37, "learning_rate": 0.00011875621285825298, "loss": 0.4101, "step": 965}, {"epoch": 1.37, "learning_rate": 0.00011860551516634466, "loss": 0.406, "step": 966}, {"epoch": 1.37, "learning_rate": 0.00011845477369386198, "loss": 0.4185, "step": 967}, {"epoch": 1.38, "learning_rate": 0.00011830398879551412, "loss": 0.4205, "step": 968}, {"epoch": 1.38, "learning_rate": 0.00011815316082611252, "loss": 0.4083, "step": 969}, {"epoch": 1.38, "learning_rate": 0.00011800229014056996, "loss": 0.417, "step": 970}, {"epoch": 1.38, "learning_rate": 0.00011785137709389977, "loss": 0.3937, "step": 971}, {"epoch": 1.38, "learning_rate": 0.0001177004220412149, "loss": 0.4102, "step": 972}, {"epoch": 1.38, "learning_rate": 0.00011754942533772715, "loss": 0.3717, "step": 973}, {"epoch": 1.38, "learning_rate": 0.0001173983873387464, "loss": 0.4249, "step": 974}, {"epoch": 1.38, "learning_rate": 0.00011724730839967961, "loss": 0.4198, "step": 975}, {"epoch": 1.39, "learning_rate": 0.00011709618887603014, "loss": 0.3871, "step": 976}, {"epoch": 1.39, "learning_rate": 0.0001169450291233968, "loss": 0.4101, "step": 977}, {"epoch": 1.39, "learning_rate": 0.00011679382949747313, "loss": 0.4295, "step": 978}, {"epoch": 1.39, "learning_rate": 0.00011664259035404644, "loss": 0.3667, "step": 979}, {"epoch": 1.39, "learning_rate": 0.00011649131204899701, "loss": 0.377, "step": 980}, {"epoch": 1.39, "learning_rate": 0.00011633999493829733, "loss": 0.3799, "step": 981}, {"epoch": 1.39, "learning_rate": 0.0001161886393780112, "loss": 0.4373, "step": 982}, {"epoch": 1.4, "learning_rate": 0.00011603724572429284, "loss": 0.3868, "step": 983}, {"epoch": 1.4, "learning_rate": 0.00011588581433338614, "loss": 0.3891, "step": 984}, {"epoch": 1.4, "learning_rate": 0.00011573434556162383, "loss": 0.3732, "step": 985}, {"epoch": 1.4, "learning_rate": 0.00011558283976542654, "loss": 0.4141, "step": 986}, {"epoch": 1.4, "learning_rate": 0.00011543129730130203, "loss": 0.4122, "step": 987}, {"epoch": 1.4, "learning_rate": 0.00011527971852584434, "loss": 0.3985, "step": 988}, {"epoch": 1.4, "learning_rate": 0.00011512810379573302, "loss": 0.4247, "step": 989}, {"epoch": 1.41, "learning_rate": 0.00011497645346773216, "loss": 0.441, "step": 990}, {"epoch": 1.41, "learning_rate": 0.00011482476789868963, "loss": 0.4245, "step": 991}, {"epoch": 1.41, "learning_rate": 0.00011467304744553618, "loss": 0.4373, "step": 992}, {"epoch": 1.41, "learning_rate": 0.00011452129246528476, "loss": 0.4282, "step": 993}, {"epoch": 1.41, "learning_rate": 0.00011436950331502947, "loss": 0.3612, "step": 994}, {"epoch": 1.41, "learning_rate": 0.0001142176803519448, "loss": 0.4033, "step": 995}, {"epoch": 1.41, "learning_rate": 0.00011406582393328493, "loss": 0.3662, "step": 996}, {"epoch": 1.42, "learning_rate": 0.00011391393441638263, "loss": 0.3708, "step": 997}, {"epoch": 1.42, "learning_rate": 0.00011376201215864864, "loss": 0.4105, "step": 998}, {"epoch": 1.42, "learning_rate": 0.00011361005751757064, "loss": 0.437, "step": 999}, {"epoch": 1.42, "learning_rate": 0.00011345807085071262, "loss": 0.4037, "step": 1000}, {"epoch": 1.42, "learning_rate": 0.00011330605251571391, "loss": 0.4158, "step": 1001}, {"epoch": 1.42, "learning_rate": 0.00011315400287028834, "loss": 0.4049, "step": 1002}, {"epoch": 1.42, "learning_rate": 0.00011300192227222335, "loss": 0.3879, "step": 1003}, {"epoch": 1.43, "learning_rate": 0.00011284981107937934, "loss": 0.4292, "step": 1004}, {"epoch": 1.43, "learning_rate": 0.0001126976696496886, "loss": 0.4157, "step": 1005}, {"epoch": 1.43, "learning_rate": 0.00011254549834115462, "loss": 0.3903, "step": 1006}, {"epoch": 1.43, "learning_rate": 0.00011239329751185122, "loss": 0.476, "step": 1007}, {"epoch": 1.43, "learning_rate": 0.00011224106751992163, "loss": 0.4249, "step": 1008}, {"epoch": 1.43, "learning_rate": 0.00011208880872357772, "loss": 0.4204, "step": 1009}, {"epoch": 1.43, "learning_rate": 0.00011193652148109914, "loss": 0.4082, "step": 1010}, {"epoch": 1.44, "learning_rate": 0.00011178420615083251, "loss": 0.4374, "step": 1011}, {"epoch": 1.44, "learning_rate": 0.00011163186309119049, "loss": 0.4176, "step": 1012}, {"epoch": 1.44, "learning_rate": 0.00011147949266065103, "loss": 0.394, "step": 1013}, {"epoch": 1.44, "learning_rate": 0.00011132709521775644, "loss": 0.395, "step": 1014}, {"epoch": 1.44, "learning_rate": 0.00011117467112111267, "loss": 0.3737, "step": 1015}, {"epoch": 1.44, "learning_rate": 0.00011102222072938832, "loss": 0.4129, "step": 1016}, {"epoch": 1.44, "learning_rate": 0.00011086974440131388, "loss": 0.4457, "step": 1017}, {"epoch": 1.45, "learning_rate": 0.0001107172424956809, "loss": 0.4034, "step": 1018}, {"epoch": 1.45, "learning_rate": 0.00011056471537134107, "loss": 0.3645, "step": 1019}, {"epoch": 1.45, "learning_rate": 0.00011041216338720548, "loss": 0.3546, "step": 1020}, {"epoch": 1.45, "learning_rate": 0.00011025958690224363, "loss": 0.4112, "step": 1021}, {"epoch": 1.45, "learning_rate": 0.0001101069862754828, "loss": 0.5535, "step": 1022}, {"epoch": 1.45, "learning_rate": 0.00010995436186600697, "loss": 0.3949, "step": 1023}, {"epoch": 1.45, "learning_rate": 0.0001098017140329561, "loss": 0.3828, "step": 1024}, {"epoch": 1.46, "learning_rate": 0.00010964904313552527, "loss": 0.3848, "step": 1025}, {"epoch": 1.46, "learning_rate": 0.0001094963495329639, "loss": 0.4061, "step": 1026}, {"epoch": 1.46, "learning_rate": 0.0001093436335845748, "loss": 0.4095, "step": 1027}, {"epoch": 1.46, "learning_rate": 0.00010919089564971328, "loss": 0.4551, "step": 1028}, {"epoch": 1.46, "learning_rate": 0.0001090381360877865, "loss": 0.4407, "step": 1029}, {"epoch": 1.46, "learning_rate": 0.00010888535525825247, "loss": 0.4295, "step": 1030}, {"epoch": 1.46, "learning_rate": 0.00010873255352061922, "loss": 0.396, "step": 1031}, {"epoch": 1.47, "learning_rate": 0.000108579731234444, "loss": 0.4277, "step": 1032}, {"epoch": 1.47, "learning_rate": 0.00010842688875933243, "loss": 0.4075, "step": 1033}, {"epoch": 1.47, "learning_rate": 0.00010827402645493758, "loss": 0.4119, "step": 1034}, {"epoch": 1.47, "learning_rate": 0.00010812114468095925, "loss": 0.3898, "step": 1035}, {"epoch": 1.47, "learning_rate": 0.000107968243797143, "loss": 0.3878, "step": 1036}, {"epoch": 1.47, "learning_rate": 0.00010781532416327944, "loss": 0.4284, "step": 1037}, {"epoch": 1.47, "learning_rate": 0.00010766238613920316, "loss": 0.4213, "step": 1038}, {"epoch": 1.48, "learning_rate": 0.00010750943008479212, "loss": 0.3651, "step": 1039}, {"epoch": 1.48, "learning_rate": 0.00010735645635996676, "loss": 0.4047, "step": 1040}, {"epoch": 1.48, "learning_rate": 0.00010720346532468895, "loss": 0.4119, "step": 1041}, {"epoch": 1.48, "learning_rate": 0.00010705045733896139, "loss": 0.4237, "step": 1042}, {"epoch": 1.48, "learning_rate": 0.00010689743276282669, "loss": 0.4054, "step": 1043}, {"epoch": 1.48, "learning_rate": 0.0001067443919563664, "loss": 0.3762, "step": 1044}, {"epoch": 1.48, "learning_rate": 0.0001065913352797004, "loss": 0.3789, "step": 1045}, {"epoch": 1.49, "learning_rate": 0.00010643826309298575, "loss": 0.4126, "step": 1046}, {"epoch": 1.49, "learning_rate": 0.00010628517575641614, "loss": 0.4194, "step": 1047}, {"epoch": 1.49, "learning_rate": 0.00010613207363022086, "loss": 0.3996, "step": 1048}, {"epoch": 1.49, "learning_rate": 0.00010597895707466401, "loss": 0.4054, "step": 1049}, {"epoch": 1.49, "learning_rate": 0.00010582582645004357, "loss": 0.4273, "step": 1050}, {"epoch": 1.49, "learning_rate": 0.0001056726821166908, "loss": 0.4172, "step": 1051}, {"epoch": 1.49, "learning_rate": 0.00010551952443496902, "loss": 0.3949, "step": 1052}, {"epoch": 1.5, "learning_rate": 0.00010536635376527306, "loss": 0.3763, "step": 1053}, {"epoch": 1.5, "learning_rate": 0.00010521317046802835, "loss": 0.4143, "step": 1054}, {"epoch": 1.5, "learning_rate": 0.00010505997490368993, "loss": 0.5755, "step": 1055}, {"epoch": 1.5, "learning_rate": 0.00010490676743274181, "loss": 0.4546, "step": 1056}, {"epoch": 1.5, "learning_rate": 0.00010475354841569593, "loss": 0.4032, "step": 1057}, {"epoch": 1.5, "learning_rate": 0.00010460031821309146, "loss": 0.4004, "step": 1058}, {"epoch": 1.5, "learning_rate": 0.00010444707718549387, "loss": 0.4065, "step": 1059}, {"epoch": 1.51, "learning_rate": 0.00010429382569349411, "loss": 0.4051, "step": 1060}, {"epoch": 1.51, "learning_rate": 0.00010414056409770767, "loss": 0.4379, "step": 1061}, {"epoch": 1.51, "learning_rate": 0.00010398729275877399, "loss": 0.4169, "step": 1062}, {"epoch": 1.51, "learning_rate": 0.00010383401203735528, "loss": 0.4594, "step": 1063}, {"epoch": 1.51, "learning_rate": 0.0001036807222941359, "loss": 0.3659, "step": 1064}, {"epoch": 1.51, "learning_rate": 0.0001035274238898214, "loss": 0.4128, "step": 1065}, {"epoch": 1.51, "learning_rate": 0.00010337411718513776, "loss": 0.4112, "step": 1066}, {"epoch": 1.52, "learning_rate": 0.00010322080254083048, "loss": 0.4239, "step": 1067}, {"epoch": 1.52, "learning_rate": 0.00010306748031766366, "loss": 0.4326, "step": 1068}, {"epoch": 1.52, "learning_rate": 0.00010291415087641937, "loss": 0.4068, "step": 1069}, {"epoch": 1.52, "learning_rate": 0.00010276081457789659, "loss": 0.4, "step": 1070}, {"epoch": 1.52, "learning_rate": 0.00010260747178291042, "loss": 0.4199, "step": 1071}, {"epoch": 1.52, "learning_rate": 0.00010245412285229124, "loss": 0.3935, "step": 1072}, {"epoch": 1.52, "learning_rate": 0.00010230076814688394, "loss": 0.424, "step": 1073}, {"epoch": 1.53, "learning_rate": 0.00010214740802754697, "loss": 0.4243, "step": 1074}, {"epoch": 1.53, "learning_rate": 0.00010199404285515146, "loss": 0.4119, "step": 1075}, {"epoch": 1.53, "learning_rate": 0.00010184067299058049, "loss": 0.3864, "step": 1076}, {"epoch": 1.53, "learning_rate": 0.00010168729879472818, "loss": 0.4157, "step": 1077}, {"epoch": 1.53, "learning_rate": 0.00010153392062849883, "loss": 0.4086, "step": 1078}, {"epoch": 1.53, "learning_rate": 0.00010138053885280605, "loss": 0.3992, "step": 1079}, {"epoch": 1.53, "learning_rate": 0.000101227153828572, "loss": 0.3901, "step": 1080}, {"epoch": 1.54, "learning_rate": 0.00010107376591672646, "loss": 0.3877, "step": 1081}, {"epoch": 1.54, "learning_rate": 0.00010092037547820601, "loss": 0.4214, "step": 1082}, {"epoch": 1.54, "learning_rate": 0.00010076698287395311, "loss": 0.3553, "step": 1083}, {"epoch": 1.54, "learning_rate": 0.00010061358846491546, "loss": 0.426, "step": 1084}, {"epoch": 1.54, "learning_rate": 0.00010046019261204487, "loss": 0.4093, "step": 1085}, {"epoch": 1.54, "learning_rate": 0.00010030679567629662, "loss": 0.4368, "step": 1086}, {"epoch": 1.54, "learning_rate": 0.00010015339801862849, "loss": 0.364, "step": 1087}, {"epoch": 1.55, "learning_rate": 0.0001, "loss": 0.3957, "step": 1088}, {"epoch": 1.55, "learning_rate": 9.984660198137155e-05, "loss": 0.4108, "step": 1089}, {"epoch": 1.55, "learning_rate": 9.96932043237034e-05, "loss": 0.4276, "step": 1090}, {"epoch": 1.55, "learning_rate": 9.953980738795516e-05, "loss": 0.4067, "step": 1091}, {"epoch": 1.55, "learning_rate": 9.938641153508456e-05, "loss": 0.4006, "step": 1092}, {"epoch": 1.55, "learning_rate": 9.923301712604691e-05, "loss": 0.3672, "step": 1093}, {"epoch": 1.55, "learning_rate": 9.907962452179403e-05, "loss": 0.5289, "step": 1094}, {"epoch": 1.56, "learning_rate": 9.892623408327357e-05, "loss": 0.4449, "step": 1095}, {"epoch": 1.56, "learning_rate": 9.877284617142802e-05, "loss": 0.4187, "step": 1096}, {"epoch": 1.56, "learning_rate": 9.861946114719398e-05, "loss": 0.4135, "step": 1097}, {"epoch": 1.56, "learning_rate": 9.84660793715012e-05, "loss": 0.4604, "step": 1098}, {"epoch": 1.56, "learning_rate": 9.831270120527184e-05, "loss": 0.4111, "step": 1099}, {"epoch": 1.56, "learning_rate": 9.815932700941954e-05, "loss": 0.5149, "step": 1100}, {"epoch": 1.56, "learning_rate": 9.800595714484856e-05, "loss": 0.3904, "step": 1101}, {"epoch": 1.57, "learning_rate": 9.785259197245306e-05, "loss": 0.3934, "step": 1102}, {"epoch": 1.57, "learning_rate": 9.769923185311608e-05, "loss": 0.4191, "step": 1103}, {"epoch": 1.57, "learning_rate": 9.754587714770878e-05, "loss": 0.4157, "step": 1104}, {"epoch": 1.57, "learning_rate": 9.739252821708962e-05, "loss": 0.402, "step": 1105}, {"epoch": 1.57, "learning_rate": 9.723918542210343e-05, "loss": 0.3925, "step": 1106}, {"epoch": 1.57, "learning_rate": 9.708584912358064e-05, "loss": 0.4424, "step": 1107}, {"epoch": 1.57, "learning_rate": 9.693251968233635e-05, "loss": 0.3709, "step": 1108}, {"epoch": 1.58, "learning_rate": 9.677919745916954e-05, "loss": 0.3987, "step": 1109}, {"epoch": 1.58, "learning_rate": 9.662588281486226e-05, "loss": 0.4296, "step": 1110}, {"epoch": 1.58, "learning_rate": 9.647257611017863e-05, "loss": 0.4292, "step": 1111}, {"epoch": 1.58, "learning_rate": 9.631927770586412e-05, "loss": 0.3891, "step": 1112}, {"epoch": 1.58, "learning_rate": 9.616598796264473e-05, "loss": 0.4153, "step": 1113}, {"epoch": 1.58, "learning_rate": 9.601270724122603e-05, "loss": 0.5066, "step": 1114}, {"epoch": 1.58, "learning_rate": 9.585943590229235e-05, "loss": 0.384, "step": 1115}, {"epoch": 1.59, "learning_rate": 9.570617430650592e-05, "loss": 0.4011, "step": 1116}, {"epoch": 1.59, "learning_rate": 9.555292281450613e-05, "loss": 0.4241, "step": 1117}, {"epoch": 1.59, "learning_rate": 9.539968178690856e-05, "loss": 0.4052, "step": 1118}, {"epoch": 1.59, "learning_rate": 9.524645158430409e-05, "loss": 0.3847, "step": 1119}, {"epoch": 1.59, "learning_rate": 9.509323256725821e-05, "loss": 0.3474, "step": 1120}, {"epoch": 1.59, "learning_rate": 9.494002509631008e-05, "loss": 0.4315, "step": 1121}, {"epoch": 1.59, "learning_rate": 9.478682953197169e-05, "loss": 0.3647, "step": 1122}, {"epoch": 1.6, "learning_rate": 9.463364623472695e-05, "loss": 0.3933, "step": 1123}, {"epoch": 1.6, "learning_rate": 9.448047556503102e-05, "loss": 0.3724, "step": 1124}, {"epoch": 1.6, "learning_rate": 9.432731788330924e-05, "loss": 0.4674, "step": 1125}, {"epoch": 1.6, "learning_rate": 9.417417354995645e-05, "loss": 0.3867, "step": 1126}, {"epoch": 1.6, "learning_rate": 9.402104292533602e-05, "loss": 0.3946, "step": 1127}, {"epoch": 1.6, "learning_rate": 9.386792636977915e-05, "loss": 0.4441, "step": 1128}, {"epoch": 1.6, "learning_rate": 9.371482424358387e-05, "loss": 0.4224, "step": 1129}, {"epoch": 1.61, "learning_rate": 9.356173690701427e-05, "loss": 0.3972, "step": 1130}, {"epoch": 1.61, "learning_rate": 9.340866472029962e-05, "loss": 0.423, "step": 1131}, {"epoch": 1.61, "learning_rate": 9.32556080436336e-05, "loss": 0.3659, "step": 1132}, {"epoch": 1.61, "learning_rate": 9.310256723717335e-05, "loss": 0.3423, "step": 1133}, {"epoch": 1.61, "learning_rate": 9.294954266103862e-05, "loss": 0.3951, "step": 1134}, {"epoch": 1.61, "learning_rate": 9.279653467531108e-05, "loss": 0.408, "step": 1135}, {"epoch": 1.61, "learning_rate": 9.264354364003327e-05, "loss": 0.4369, "step": 1136}, {"epoch": 1.62, "learning_rate": 9.24905699152079e-05, "loss": 0.3661, "step": 1137}, {"epoch": 1.62, "learning_rate": 9.233761386079685e-05, "loss": 0.4085, "step": 1138}, {"epoch": 1.62, "learning_rate": 9.21846758367206e-05, "loss": 0.3892, "step": 1139}, {"epoch": 1.62, "learning_rate": 9.2031756202857e-05, "loss": 0.4244, "step": 1140}, {"epoch": 1.62, "learning_rate": 9.187885531904078e-05, "loss": 0.4143, "step": 1141}, {"epoch": 1.62, "learning_rate": 9.172597354506244e-05, "loss": 0.4308, "step": 1142}, {"epoch": 1.62, "learning_rate": 9.157311124066761e-05, "loss": 0.3737, "step": 1143}, {"epoch": 1.62, "learning_rate": 9.142026876555602e-05, "loss": 0.4267, "step": 1144}, {"epoch": 1.63, "learning_rate": 9.126744647938079e-05, "loss": 0.3933, "step": 1145}, {"epoch": 1.63, "learning_rate": 9.111464474174755e-05, "loss": 0.4493, "step": 1146}, {"epoch": 1.63, "learning_rate": 9.096186391221352e-05, "loss": 0.3972, "step": 1147}, {"epoch": 1.63, "learning_rate": 9.080910435028674e-05, "loss": 0.4294, "step": 1148}, {"epoch": 1.63, "learning_rate": 9.065636641542523e-05, "loss": 0.4226, "step": 1149}, {"epoch": 1.63, "learning_rate": 9.050365046703611e-05, "loss": 0.4019, "step": 1150}, {"epoch": 1.63, "learning_rate": 9.035095686447475e-05, "loss": 0.3789, "step": 1151}, {"epoch": 1.64, "learning_rate": 9.019828596704394e-05, "loss": 0.4017, "step": 1152}, {"epoch": 1.64, "learning_rate": 9.004563813399307e-05, "loss": 0.4154, "step": 1153}, {"epoch": 1.64, "learning_rate": 8.989301372451722e-05, "loss": 0.4179, "step": 1154}, {"epoch": 1.64, "learning_rate": 8.974041309775639e-05, "loss": 0.3483, "step": 1155}, {"epoch": 1.64, "learning_rate": 8.958783661279454e-05, "loss": 0.4131, "step": 1156}, {"epoch": 1.64, "learning_rate": 8.943528462865894e-05, "loss": 0.3595, "step": 1157}, {"epoch": 1.64, "learning_rate": 8.928275750431912e-05, "loss": 0.3619, "step": 1158}, {"epoch": 1.65, "learning_rate": 8.913025559868615e-05, "loss": 0.3621, "step": 1159}, {"epoch": 1.65, "learning_rate": 8.897777927061169e-05, "loss": 0.4198, "step": 1160}, {"epoch": 1.65, "learning_rate": 8.882532887888734e-05, "loss": 0.3743, "step": 1161}, {"epoch": 1.65, "learning_rate": 8.867290478224359e-05, "loss": 0.4347, "step": 1162}, {"epoch": 1.65, "learning_rate": 8.852050733934898e-05, "loss": 0.3998, "step": 1163}, {"epoch": 1.65, "learning_rate": 8.836813690880953e-05, "loss": 0.3571, "step": 1164}, {"epoch": 1.65, "learning_rate": 8.821579384916752e-05, "loss": 0.412, "step": 1165}, {"epoch": 1.66, "learning_rate": 8.806347851890088e-05, "loss": 0.4451, "step": 1166}, {"epoch": 1.66, "learning_rate": 8.791119127642229e-05, "loss": 0.3567, "step": 1167}, {"epoch": 1.66, "learning_rate": 8.775893248007839e-05, "loss": 0.3911, "step": 1168}, {"epoch": 1.66, "learning_rate": 8.760670248814879e-05, "loss": 0.4193, "step": 1169}, {"epoch": 1.66, "learning_rate": 8.74545016588454e-05, "loss": 0.3816, "step": 1170}, {"epoch": 1.66, "learning_rate": 8.730233035031141e-05, "loss": 0.4197, "step": 1171}, {"epoch": 1.66, "learning_rate": 8.715018892062069e-05, "loss": 0.4178, "step": 1172}, {"epoch": 1.67, "learning_rate": 8.699807772777669e-05, "loss": 0.3908, "step": 1173}, {"epoch": 1.67, "learning_rate": 8.684599712971168e-05, "loss": 0.3629, "step": 1174}, {"epoch": 1.67, "learning_rate": 8.66939474842861e-05, "loss": 0.3783, "step": 1175}, {"epoch": 1.67, "learning_rate": 8.654192914928739e-05, "loss": 0.3933, "step": 1176}, {"epoch": 1.67, "learning_rate": 8.63899424824294e-05, "loss": 0.4159, "step": 1177}, {"epoch": 1.67, "learning_rate": 8.62379878413514e-05, "loss": 0.3795, "step": 1178}, {"epoch": 1.67, "learning_rate": 8.608606558361738e-05, "loss": 0.4329, "step": 1179}, {"epoch": 1.68, "learning_rate": 8.593417606671509e-05, "loss": 0.3958, "step": 1180}, {"epoch": 1.68, "learning_rate": 8.57823196480552e-05, "loss": 0.3647, "step": 1181}, {"epoch": 1.68, "learning_rate": 8.563049668497057e-05, "loss": 0.3921, "step": 1182}, {"epoch": 1.68, "learning_rate": 8.547870753471526e-05, "loss": 0.36, "step": 1183}, {"epoch": 1.68, "learning_rate": 8.532695255446383e-05, "loss": 0.3932, "step": 1184}, {"epoch": 1.68, "learning_rate": 8.51752321013104e-05, "loss": 0.401, "step": 1185}, {"epoch": 1.68, "learning_rate": 8.502354653226785e-05, "loss": 0.4286, "step": 1186}, {"epoch": 1.69, "learning_rate": 8.487189620426699e-05, "loss": 0.4141, "step": 1187}, {"epoch": 1.69, "learning_rate": 8.472028147415567e-05, "loss": 0.4246, "step": 1188}, {"epoch": 1.69, "learning_rate": 8.456870269869798e-05, "loss": 0.4013, "step": 1189}, {"epoch": 1.69, "learning_rate": 8.44171602345735e-05, "loss": 0.3911, "step": 1190}, {"epoch": 1.69, "learning_rate": 8.426565443837618e-05, "loss": 0.4195, "step": 1191}, {"epoch": 1.69, "learning_rate": 8.411418566661388e-05, "loss": 0.4067, "step": 1192}, {"epoch": 1.69, "learning_rate": 8.396275427570718e-05, "loss": 0.409, "step": 1193}, {"epoch": 1.7, "learning_rate": 8.381136062198881e-05, "loss": 0.4366, "step": 1194}, {"epoch": 1.7, "learning_rate": 8.36600050617027e-05, "loss": 0.3708, "step": 1195}, {"epoch": 1.7, "learning_rate": 8.350868795100301e-05, "loss": 0.4286, "step": 1196}, {"epoch": 1.7, "learning_rate": 8.33574096459536e-05, "loss": 0.3879, "step": 1197}, {"epoch": 1.7, "learning_rate": 8.32061705025269e-05, "loss": 0.4035, "step": 1198}, {"epoch": 1.7, "learning_rate": 8.305497087660323e-05, "loss": 0.4128, "step": 1199}, {"epoch": 1.7, "learning_rate": 8.290381112396987e-05, "loss": 0.406, "step": 1200}, {"epoch": 1.71, "learning_rate": 8.275269160032041e-05, "loss": 0.389, "step": 1201}, {"epoch": 1.71, "learning_rate": 8.260161266125363e-05, "loss": 0.3904, "step": 1202}, {"epoch": 1.71, "learning_rate": 8.245057466227288e-05, "loss": 0.3734, "step": 1203}, {"epoch": 1.71, "learning_rate": 8.229957795878514e-05, "loss": 0.3856, "step": 1204}, {"epoch": 1.71, "learning_rate": 8.214862290610026e-05, "loss": 0.3939, "step": 1205}, {"epoch": 1.71, "learning_rate": 8.199770985943006e-05, "loss": 0.3936, "step": 1206}, {"epoch": 1.71, "learning_rate": 8.18468391738875e-05, "loss": 0.4069, "step": 1207}, {"epoch": 1.72, "learning_rate": 8.16960112044859e-05, "loss": 0.3651, "step": 1208}, {"epoch": 1.72, "learning_rate": 8.154522630613806e-05, "loss": 0.3959, "step": 1209}, {"epoch": 1.72, "learning_rate": 8.139448483365535e-05, "loss": 0.3756, "step": 1210}, {"epoch": 1.72, "learning_rate": 8.124378714174704e-05, "loss": 0.4105, "step": 1211}, {"epoch": 1.72, "learning_rate": 8.10931335850194e-05, "loss": 0.3634, "step": 1212}, {"epoch": 1.72, "learning_rate": 8.094252451797473e-05, "loss": 0.4081, "step": 1213}, {"epoch": 1.72, "learning_rate": 8.079196029501077e-05, "loss": 0.3917, "step": 1214}, {"epoch": 1.73, "learning_rate": 8.064144127041963e-05, "loss": 0.3809, "step": 1215}, {"epoch": 1.73, "learning_rate": 8.049096779838719e-05, "loss": 0.3827, "step": 1216}, {"epoch": 1.73, "learning_rate": 8.034054023299199e-05, "loss": 0.4289, "step": 1217}, {"epoch": 1.73, "learning_rate": 8.019015892820464e-05, "loss": 0.3785, "step": 1218}, {"epoch": 1.73, "learning_rate": 8.003982423788691e-05, "loss": 0.4183, "step": 1219}, {"epoch": 1.73, "learning_rate": 7.988953651579081e-05, "loss": 0.4153, "step": 1220}, {"epoch": 1.73, "learning_rate": 7.97392961155579e-05, "loss": 0.3791, "step": 1221}, {"epoch": 1.74, "learning_rate": 7.958910339071831e-05, "loss": 0.4011, "step": 1222}, {"epoch": 1.74, "learning_rate": 7.943895869469008e-05, "loss": 0.3837, "step": 1223}, {"epoch": 1.74, "learning_rate": 7.928886238077816e-05, "loss": 0.3387, "step": 1224}, {"epoch": 1.74, "learning_rate": 7.913881480217366e-05, "loss": 0.378, "step": 1225}, {"epoch": 1.74, "learning_rate": 7.898881631195303e-05, "loss": 0.4177, "step": 1226}, {"epoch": 1.74, "learning_rate": 7.883886726307725e-05, "loss": 0.3651, "step": 1227}, {"epoch": 1.74, "learning_rate": 7.868896800839087e-05, "loss": 0.3869, "step": 1228}, {"epoch": 1.75, "learning_rate": 7.853911890062132e-05, "loss": 0.4521, "step": 1229}, {"epoch": 1.75, "learning_rate": 7.838932029237805e-05, "loss": 0.3842, "step": 1230}, {"epoch": 1.75, "learning_rate": 7.823957253615165e-05, "loss": 0.4198, "step": 1231}, {"epoch": 1.75, "learning_rate": 7.808987598431303e-05, "loss": 0.4078, "step": 1232}, {"epoch": 1.75, "learning_rate": 7.794023098911265e-05, "loss": 0.4312, "step": 1233}, {"epoch": 1.75, "learning_rate": 7.779063790267965e-05, "loss": 0.4235, "step": 1234}, {"epoch": 1.75, "learning_rate": 7.764109707702102e-05, "loss": 0.3895, "step": 1235}, {"epoch": 1.76, "learning_rate": 7.749160886402073e-05, "loss": 0.3772, "step": 1236}, {"epoch": 1.76, "learning_rate": 7.734217361543901e-05, "loss": 0.3762, "step": 1237}, {"epoch": 1.76, "learning_rate": 7.719279168291144e-05, "loss": 0.4085, "step": 1238}, {"epoch": 1.76, "learning_rate": 7.704346341794813e-05, "loss": 0.4014, "step": 1239}, {"epoch": 1.76, "learning_rate": 7.689418917193289e-05, "loss": 0.4209, "step": 1240}, {"epoch": 1.76, "learning_rate": 7.674496929612249e-05, "loss": 0.3946, "step": 1241}, {"epoch": 1.76, "learning_rate": 7.659580414164567e-05, "loss": 0.4157, "step": 1242}, {"epoch": 1.77, "learning_rate": 7.644669405950246e-05, "loss": 0.3894, "step": 1243}, {"epoch": 1.77, "learning_rate": 7.629763940056328e-05, "loss": 0.4526, "step": 1244}, {"epoch": 1.77, "learning_rate": 7.614864051556816e-05, "loss": 0.4288, "step": 1245}, {"epoch": 1.77, "learning_rate": 7.599969775512587e-05, "loss": 0.4038, "step": 1246}, {"epoch": 1.77, "learning_rate": 7.58508114697131e-05, "loss": 0.3909, "step": 1247}, {"epoch": 1.77, "learning_rate": 7.570198200967362e-05, "loss": 0.372, "step": 1248}, {"epoch": 1.77, "learning_rate": 7.55532097252176e-05, "loss": 0.4273, "step": 1249}, {"epoch": 1.78, "learning_rate": 7.540449496642056e-05, "loss": 0.3804, "step": 1250}, {"epoch": 1.78, "learning_rate": 7.525583808322268e-05, "loss": 0.4063, "step": 1251}, {"epoch": 1.78, "learning_rate": 7.5107239425428e-05, "loss": 0.4011, "step": 1252}, {"epoch": 1.78, "learning_rate": 7.495869934270348e-05, "loss": 0.3751, "step": 1253}, {"epoch": 1.78, "learning_rate": 7.481021818457831e-05, "loss": 0.3798, "step": 1254}, {"epoch": 1.78, "learning_rate": 7.466179630044299e-05, "loss": 0.3714, "step": 1255}, {"epoch": 1.78, "learning_rate": 7.451343403954856e-05, "loss": 0.4082, "step": 1256}, {"epoch": 1.79, "learning_rate": 7.436513175100572e-05, "loss": 0.3801, "step": 1257}, {"epoch": 1.79, "learning_rate": 7.421688978378411e-05, "loss": 0.3684, "step": 1258}, {"epoch": 1.79, "learning_rate": 7.406870848671138e-05, "loss": 0.4, "step": 1259}, {"epoch": 1.79, "learning_rate": 7.392058820847245e-05, "loss": 0.4072, "step": 1260}, {"epoch": 1.79, "learning_rate": 7.377252929760866e-05, "loss": 0.3919, "step": 1261}, {"epoch": 1.79, "learning_rate": 7.362453210251686e-05, "loss": 0.4402, "step": 1262}, {"epoch": 1.79, "learning_rate": 7.347659697144882e-05, "loss": 0.3751, "step": 1263}, {"epoch": 1.8, "learning_rate": 7.332872425251018e-05, "loss": 0.451, "step": 1264}, {"epoch": 1.8, "learning_rate": 7.31809142936597e-05, "loss": 0.3817, "step": 1265}, {"epoch": 1.8, "learning_rate": 7.303316744270849e-05, "loss": 0.3646, "step": 1266}, {"epoch": 1.8, "learning_rate": 7.288548404731921e-05, "loss": 0.3775, "step": 1267}, {"epoch": 1.8, "learning_rate": 7.273786445500513e-05, "loss": 0.3992, "step": 1268}, {"epoch": 1.8, "learning_rate": 7.259030901312937e-05, "loss": 0.4134, "step": 1269}, {"epoch": 1.8, "learning_rate": 7.244281806890419e-05, "loss": 0.4203, "step": 1270}, {"epoch": 1.81, "learning_rate": 7.229539196939001e-05, "loss": 0.3654, "step": 1271}, {"epoch": 1.81, "learning_rate": 7.21480310614947e-05, "loss": 0.5428, "step": 1272}, {"epoch": 1.81, "learning_rate": 7.200073569197268e-05, "loss": 0.3802, "step": 1273}, {"epoch": 1.81, "learning_rate": 7.185350620742421e-05, "loss": 0.4177, "step": 1274}, {"epoch": 1.81, "learning_rate": 7.170634295429449e-05, "loss": 0.4101, "step": 1275}, {"epoch": 1.81, "learning_rate": 7.155924627887282e-05, "loss": 0.359, "step": 1276}, {"epoch": 1.81, "learning_rate": 7.141221652729195e-05, "loss": 0.4566, "step": 1277}, {"epoch": 1.82, "learning_rate": 7.126525404552706e-05, "loss": 0.3618, "step": 1278}, {"epoch": 1.82, "learning_rate": 7.111835917939507e-05, "loss": 0.3856, "step": 1279}, {"epoch": 1.82, "learning_rate": 7.097153227455379e-05, "loss": 0.3697, "step": 1280}, {"epoch": 1.82, "learning_rate": 7.082477367650108e-05, "loss": 0.4312, "step": 1281}, {"epoch": 1.82, "learning_rate": 7.067808373057414e-05, "loss": 0.3493, "step": 1282}, {"epoch": 1.82, "learning_rate": 7.053146278194858e-05, "loss": 0.3961, "step": 1283}, {"epoch": 1.82, "learning_rate": 7.038491117563761e-05, "loss": 0.3601, "step": 1284}, {"epoch": 1.83, "learning_rate": 7.023842925649138e-05, "loss": 0.4096, "step": 1285}, {"epoch": 1.83, "learning_rate": 7.009201736919597e-05, "loss": 0.3969, "step": 1286}, {"epoch": 1.83, "learning_rate": 6.994567585827268e-05, "loss": 0.3852, "step": 1287}, {"epoch": 1.83, "learning_rate": 6.97994050680772e-05, "loss": 0.3462, "step": 1288}, {"epoch": 1.83, "learning_rate": 6.965320534279888e-05, "loss": 0.356, "step": 1289}, {"epoch": 1.83, "learning_rate": 6.950707702645978e-05, "loss": 0.4215, "step": 1290}, {"epoch": 1.83, "learning_rate": 6.93610204629139e-05, "loss": 0.4068, "step": 1291}, {"epoch": 1.84, "learning_rate": 6.921503599584652e-05, "loss": 0.3987, "step": 1292}, {"epoch": 1.84, "learning_rate": 6.906912396877314e-05, "loss": 0.4037, "step": 1293}, {"epoch": 1.84, "learning_rate": 6.892328472503887e-05, "loss": 0.3639, "step": 1294}, {"epoch": 1.84, "learning_rate": 6.877751860781752e-05, "loss": 0.4076, "step": 1295}, {"epoch": 1.84, "learning_rate": 6.863182596011087e-05, "loss": 0.366, "step": 1296}, {"epoch": 1.84, "learning_rate": 6.848620712474777e-05, "loss": 0.3846, "step": 1297}, {"epoch": 1.84, "learning_rate": 6.834066244438343e-05, "loss": 0.487, "step": 1298}, {"epoch": 1.85, "learning_rate": 6.81951922614985e-05, "loss": 0.4085, "step": 1299}, {"epoch": 1.85, "learning_rate": 6.804979691839843e-05, "loss": 0.3847, "step": 1300}, {"epoch": 1.85, "learning_rate": 6.79044767572125e-05, "loss": 0.3723, "step": 1301}, {"epoch": 1.85, "learning_rate": 6.775923211989301e-05, "loss": 0.3906, "step": 1302}, {"epoch": 1.85, "learning_rate": 6.761406334821472e-05, "loss": 0.4152, "step": 1303}, {"epoch": 1.85, "learning_rate": 6.746897078377372e-05, "loss": 0.4374, "step": 1304}, {"epoch": 1.85, "learning_rate": 6.732395476798685e-05, "loss": 0.3783, "step": 1305}, {"epoch": 1.86, "learning_rate": 6.717901564209075e-05, "loss": 0.3708, "step": 1306}, {"epoch": 1.86, "learning_rate": 6.703415374714126e-05, "loss": 0.3934, "step": 1307}, {"epoch": 1.86, "learning_rate": 6.688936942401237e-05, "loss": 0.4268, "step": 1308}, {"epoch": 1.86, "learning_rate": 6.674466301339559e-05, "loss": 0.387, "step": 1309}, {"epoch": 1.86, "learning_rate": 6.660003485579907e-05, "loss": 0.4018, "step": 1310}, {"epoch": 1.86, "learning_rate": 6.645548529154684e-05, "loss": 0.3612, "step": 1311}, {"epoch": 1.86, "learning_rate": 6.6311014660778e-05, "loss": 0.4195, "step": 1312}, {"epoch": 1.87, "learning_rate": 6.616662330344589e-05, "loss": 0.4253, "step": 1313}, {"epoch": 1.87, "learning_rate": 6.602231155931731e-05, "loss": 0.3785, "step": 1314}, {"epoch": 1.87, "learning_rate": 6.587807976797178e-05, "loss": 0.3573, "step": 1315}, {"epoch": 1.87, "learning_rate": 6.573392826880059e-05, "loss": 0.4619, "step": 1316}, {"epoch": 1.87, "learning_rate": 6.558985740100612e-05, "loss": 0.3747, "step": 1317}, {"epoch": 1.87, "learning_rate": 6.54458675036011e-05, "loss": 0.3896, "step": 1318}, {"epoch": 1.87, "learning_rate": 6.530195891540764e-05, "loss": 0.3693, "step": 1319}, {"epoch": 1.88, "learning_rate": 6.515813197505656e-05, "loss": 0.3575, "step": 1320}, {"epoch": 1.88, "learning_rate": 6.50143870209865e-05, "loss": 0.3834, "step": 1321}, {"epoch": 1.88, "learning_rate": 6.487072439144331e-05, "loss": 0.5198, "step": 1322}, {"epoch": 1.88, "learning_rate": 6.472714442447893e-05, "loss": 0.3978, "step": 1323}, {"epoch": 1.88, "learning_rate": 6.458364745795096e-05, "loss": 0.3598, "step": 1324}, {"epoch": 1.88, "learning_rate": 6.444023382952162e-05, "loss": 0.38, "step": 1325}, {"epoch": 1.88, "learning_rate": 6.429690387665702e-05, "loss": 0.3844, "step": 1326}, {"epoch": 1.88, "learning_rate": 6.415365793662636e-05, "loss": 0.4412, "step": 1327}, {"epoch": 1.89, "learning_rate": 6.401049634650118e-05, "loss": 0.4253, "step": 1328}, {"epoch": 1.89, "learning_rate": 6.386741944315457e-05, "loss": 0.3639, "step": 1329}, {"epoch": 1.89, "learning_rate": 6.37244275632603e-05, "loss": 0.3892, "step": 1330}, {"epoch": 1.89, "learning_rate": 6.358152104329202e-05, "loss": 0.4464, "step": 1331}, {"epoch": 1.89, "learning_rate": 6.343870021952262e-05, "loss": 0.3957, "step": 1332}, {"epoch": 1.89, "learning_rate": 6.32959654280233e-05, "loss": 0.4233, "step": 1333}, {"epoch": 1.89, "learning_rate": 6.315331700466278e-05, "loss": 0.3979, "step": 1334}, {"epoch": 1.9, "learning_rate": 6.301075528510659e-05, "loss": 0.4249, "step": 1335}, {"epoch": 1.9, "learning_rate": 6.286828060481626e-05, "loss": 0.3718, "step": 1336}, {"epoch": 1.9, "learning_rate": 6.272589329904843e-05, "loss": 0.4361, "step": 1337}, {"epoch": 1.9, "learning_rate": 6.258359370285422e-05, "loss": 0.3979, "step": 1338}, {"epoch": 1.9, "learning_rate": 6.244138215107829e-05, "loss": 0.3916, "step": 1339}, {"epoch": 1.9, "learning_rate": 6.229925897835818e-05, "loss": 0.4037, "step": 1340}, {"epoch": 1.9, "learning_rate": 6.215722451912346e-05, "loss": 0.3952, "step": 1341}, {"epoch": 1.91, "learning_rate": 6.20152791075949e-05, "loss": 0.3996, "step": 1342}, {"epoch": 1.91, "learning_rate": 6.187342307778377e-05, "loss": 0.388, "step": 1343}, {"epoch": 1.91, "learning_rate": 6.173165676349103e-05, "loss": 0.3315, "step": 1344}, {"epoch": 1.91, "learning_rate": 6.158998049830651e-05, "loss": 0.3784, "step": 1345}, {"epoch": 1.91, "learning_rate": 6.144839461560812e-05, "loss": 0.4109, "step": 1346}, {"epoch": 1.91, "learning_rate": 6.130689944856114e-05, "loss": 0.3873, "step": 1347}, {"epoch": 1.91, "learning_rate": 6.116549533011738e-05, "loss": 0.3828, "step": 1348}, {"epoch": 1.92, "learning_rate": 6.102418259301437e-05, "loss": 0.4303, "step": 1349}, {"epoch": 1.92, "learning_rate": 6.088296156977462e-05, "loss": 0.3833, "step": 1350}, {"epoch": 1.92, "learning_rate": 6.074183259270486e-05, "loss": 0.3875, "step": 1351}, {"epoch": 1.92, "learning_rate": 6.060079599389521e-05, "loss": 0.3751, "step": 1352}, {"epoch": 1.92, "learning_rate": 6.045985210521838e-05, "loss": 0.396, "step": 1353}, {"epoch": 1.92, "learning_rate": 6.031900125832897e-05, "loss": 0.3831, "step": 1354}, {"epoch": 1.92, "learning_rate": 6.0178243784662656e-05, "loss": 0.3765, "step": 1355}, {"epoch": 1.93, "learning_rate": 6.0037580015435334e-05, "loss": 0.361, "step": 1356}, {"epoch": 1.93, "learning_rate": 5.989701028164243e-05, "loss": 0.3715, "step": 1357}, {"epoch": 1.93, "learning_rate": 5.975653491405816e-05, "loss": 0.4233, "step": 1358}, {"epoch": 1.93, "learning_rate": 5.96161542432346e-05, "loss": 0.3694, "step": 1359}, {"epoch": 1.93, "learning_rate": 5.947586859950103e-05, "loss": 0.3543, "step": 1360}, {"epoch": 1.93, "learning_rate": 5.93356783129631e-05, "loss": 0.4094, "step": 1361}, {"epoch": 1.93, "learning_rate": 5.919558371350213e-05, "loss": 0.4305, "step": 1362}, {"epoch": 1.94, "learning_rate": 5.905558513077425e-05, "loss": 0.4128, "step": 1363}, {"epoch": 1.94, "learning_rate": 5.8915682894209626e-05, "loss": 0.3918, "step": 1364}, {"epoch": 1.94, "learning_rate": 5.8775877333011706e-05, "loss": 0.3731, "step": 1365}, {"epoch": 1.94, "learning_rate": 5.863616877615656e-05, "loss": 0.4071, "step": 1366}, {"epoch": 1.94, "learning_rate": 5.849655755239185e-05, "loss": 0.3878, "step": 1367}, {"epoch": 1.94, "learning_rate": 5.83570439902363e-05, "loss": 0.4186, "step": 1368}, {"epoch": 1.94, "learning_rate": 5.8217628417978775e-05, "loss": 0.4449, "step": 1369}, {"epoch": 1.95, "learning_rate": 5.807831116367759e-05, "loss": 0.3995, "step": 1370}, {"epoch": 1.95, "learning_rate": 5.7939092555159766e-05, "loss": 0.4014, "step": 1371}, {"epoch": 1.95, "learning_rate": 5.779997292002004e-05, "loss": 0.3814, "step": 1372}, {"epoch": 1.95, "learning_rate": 5.766095258562043e-05, "loss": 0.4143, "step": 1373}, {"epoch": 1.95, "learning_rate": 5.7522031879089133e-05, "loss": 0.3881, "step": 1374}, {"epoch": 1.95, "learning_rate": 5.7383211127320036e-05, "loss": 0.3953, "step": 1375}, {"epoch": 1.95, "learning_rate": 5.7244490656971815e-05, "loss": 0.3937, "step": 1376}, {"epoch": 1.96, "learning_rate": 5.7105870794467054e-05, "loss": 0.4281, "step": 1377}, {"epoch": 1.96, "learning_rate": 5.696735186599174e-05, "loss": 0.347, "step": 1378}, {"epoch": 1.96, "learning_rate": 5.682893419749429e-05, "loss": 0.384, "step": 1379}, {"epoch": 1.96, "learning_rate": 5.669061811468481e-05, "loss": 0.4098, "step": 1380}, {"epoch": 1.96, "learning_rate": 5.655240394303443e-05, "loss": 0.4377, "step": 1381}, {"epoch": 1.96, "learning_rate": 5.641429200777446e-05, "loss": 0.4097, "step": 1382}, {"epoch": 1.96, "learning_rate": 5.627628263389559e-05, "loss": 0.3964, "step": 1383}, {"epoch": 1.97, "learning_rate": 5.613837614614727e-05, "loss": 0.3566, "step": 1384}, {"epoch": 1.97, "learning_rate": 5.6000572869036685e-05, "loss": 0.4101, "step": 1385}, {"epoch": 1.97, "learning_rate": 5.586287312682833e-05, "loss": 0.4178, "step": 1386}, {"epoch": 1.97, "learning_rate": 5.5725277243543016e-05, "loss": 0.3876, "step": 1387}, {"epoch": 1.97, "learning_rate": 5.558778554295709e-05, "loss": 0.4239, "step": 1388}, {"epoch": 1.97, "learning_rate": 5.545039834860182e-05, "loss": 0.3649, "step": 1389}, {"epoch": 1.97, "learning_rate": 5.531311598376261e-05, "loss": 0.4081, "step": 1390}, {"epoch": 1.98, "learning_rate": 5.517593877147802e-05, "loss": 0.3913, "step": 1391}, {"epoch": 1.98, "learning_rate": 5.503886703453933e-05, "loss": 0.371, "step": 1392}, {"epoch": 1.98, "learning_rate": 5.490190109548963e-05, "loss": 0.4103, "step": 1393}, {"epoch": 1.98, "learning_rate": 5.476504127662292e-05, "loss": 0.3597, "step": 1394}, {"epoch": 1.98, "learning_rate": 5.4628287899983644e-05, "loss": 0.3938, "step": 1395}, {"epoch": 1.98, "learning_rate": 5.4491641287365635e-05, "loss": 0.389, "step": 1396}, {"epoch": 1.98, "learning_rate": 5.43551017603116e-05, "loss": 0.4188, "step": 1397}, {"epoch": 1.99, "learning_rate": 5.421866964011231e-05, "loss": 0.4311, "step": 1398}, {"epoch": 1.99, "learning_rate": 5.40823452478056e-05, "loss": 0.3708, "step": 1399}, {"epoch": 1.99, "learning_rate": 5.3946128904176e-05, "loss": 0.393, "step": 1400}, {"epoch": 1.99, "learning_rate": 5.381002092975375e-05, "loss": 0.3683, "step": 1401}, {"epoch": 1.99, "learning_rate": 5.367402164481399e-05, "loss": 0.3801, "step": 1402}, {"epoch": 1.99, "learning_rate": 5.353813136937621e-05, "loss": 0.3808, "step": 1403}, {"epoch": 1.99, "learning_rate": 5.340235042320341e-05, "loss": 0.3885, "step": 1404}, {"epoch": 2.0, "learning_rate": 5.326667912580117e-05, "loss": 0.4011, "step": 1405}, {"epoch": 2.0, "learning_rate": 5.313111779641724e-05, "loss": 0.4112, "step": 1406}, {"epoch": 2.0, "learning_rate": 5.299566675404045e-05, "loss": 0.3868, "step": 1407}, {"epoch": 2.0, "learning_rate": 5.286032631740023e-05, "loss": 0.4376, "step": 1408}, {"epoch": 2.0, "learning_rate": 5.2725096804965744e-05, "loss": 0.385, "step": 1409}, {"epoch": 2.0, "learning_rate": 5.258997853494501e-05, "loss": 0.3977, "step": 1410}, {"epoch": 2.0, "learning_rate": 5.2454971825284406e-05, "loss": 0.3588, "step": 1411}, {"epoch": 2.01, "learning_rate": 5.232007699366781e-05, "loss": 0.3213, "step": 1412}, {"epoch": 2.01, "learning_rate": 5.21852943575157e-05, "loss": 0.3383, "step": 1413}, {"epoch": 2.01, "learning_rate": 5.205062423398469e-05, "loss": 0.4535, "step": 1414}, {"epoch": 2.01, "learning_rate": 5.191606693996662e-05, "loss": 0.3953, "step": 1415}, {"epoch": 2.01, "learning_rate": 5.1781622792087735e-05, "loss": 0.4555, "step": 1416}, {"epoch": 2.01, "learning_rate": 5.1647292106708154e-05, "loss": 0.3397, "step": 1417}, {"epoch": 2.01, "learning_rate": 5.15130751999209e-05, "loss": 0.3684, "step": 1418}, {"epoch": 2.02, "learning_rate": 5.137897238755136e-05, "loss": 0.3629, "step": 1419}, {"epoch": 2.02, "learning_rate": 5.124498398515642e-05, "loss": 0.3937, "step": 1420}, {"epoch": 2.02, "learning_rate": 5.1111110308023694e-05, "loss": 0.4014, "step": 1421}, {"epoch": 2.02, "learning_rate": 5.097735167117088e-05, "loss": 0.3812, "step": 1422}, {"epoch": 2.02, "learning_rate": 5.084370838934502e-05, "loss": 0.3539, "step": 1423}, {"epoch": 2.02, "learning_rate": 5.071018077702161e-05, "loss": 0.3718, "step": 1424}, {"epoch": 2.02, "learning_rate": 5.057676914840401e-05, "loss": 0.3719, "step": 1425}, {"epoch": 2.03, "learning_rate": 5.044347381742276e-05, "loss": 0.3894, "step": 1426}, {"epoch": 2.03, "learning_rate": 5.031029509773455e-05, "loss": 0.367, "step": 1427}, {"epoch": 2.03, "learning_rate": 5.017723330272184e-05, "loss": 0.3631, "step": 1428}, {"epoch": 2.03, "learning_rate": 5.004428874549183e-05, "loss": 0.3505, "step": 1429}, {"epoch": 2.03, "learning_rate": 4.991146173887592e-05, "loss": 0.3483, "step": 1430}, {"epoch": 2.03, "learning_rate": 4.9778752595428946e-05, "loss": 0.4175, "step": 1431}, {"epoch": 2.03, "learning_rate": 4.964616162742826e-05, "loss": 0.3771, "step": 1432}, {"epoch": 2.04, "learning_rate": 4.9513689146873246e-05, "loss": 0.378, "step": 1433}, {"epoch": 2.04, "learning_rate": 4.938133546548449e-05, "loss": 0.3484, "step": 1434}, {"epoch": 2.04, "learning_rate": 4.924910089470293e-05, "loss": 0.3341, "step": 1435}, {"epoch": 2.04, "learning_rate": 4.9116985745689294e-05, "loss": 0.3914, "step": 1436}, {"epoch": 2.04, "learning_rate": 4.8984990329323346e-05, "loss": 0.3695, "step": 1437}, {"epoch": 2.04, "learning_rate": 4.885311495620296e-05, "loss": 0.3602, "step": 1438}, {"epoch": 2.04, "learning_rate": 4.8721359936643726e-05, "loss": 0.3866, "step": 1439}, {"epoch": 2.05, "learning_rate": 4.8589725580677835e-05, "loss": 0.3629, "step": 1440}, {"epoch": 2.05, "learning_rate": 4.84582121980537e-05, "loss": 0.3941, "step": 1441}, {"epoch": 2.05, "learning_rate": 4.832682009823504e-05, "loss": 0.3675, "step": 1442}, {"epoch": 2.05, "learning_rate": 4.819554959040008e-05, "loss": 0.3232, "step": 1443}, {"epoch": 2.05, "learning_rate": 4.806440098344104e-05, "loss": 0.3446, "step": 1444}, {"epoch": 2.05, "learning_rate": 4.793337458596331e-05, "loss": 0.4235, "step": 1445}, {"epoch": 2.05, "learning_rate": 4.780247070628457e-05, "loss": 0.3539, "step": 1446}, {"epoch": 2.06, "learning_rate": 4.767168965243435e-05, "loss": 0.3725, "step": 1447}, {"epoch": 2.06, "learning_rate": 4.754103173215313e-05, "loss": 0.4293, "step": 1448}, {"epoch": 2.06, "learning_rate": 4.741049725289154e-05, "loss": 0.4078, "step": 1449}, {"epoch": 2.06, "learning_rate": 4.7280086521809894e-05, "loss": 0.3976, "step": 1450}, {"epoch": 2.06, "learning_rate": 4.714979984577717e-05, "loss": 0.3836, "step": 1451}, {"epoch": 2.06, "learning_rate": 4.701963753137053e-05, "loss": 0.397, "step": 1452}, {"epoch": 2.06, "learning_rate": 4.688959988487453e-05, "loss": 0.3638, "step": 1453}, {"epoch": 2.07, "learning_rate": 4.675968721228021e-05, "loss": 0.4112, "step": 1454}, {"epoch": 2.07, "learning_rate": 4.6629899819284706e-05, "loss": 0.3656, "step": 1455}, {"epoch": 2.07, "learning_rate": 4.6500238011290295e-05, "loss": 0.3823, "step": 1456}, {"epoch": 2.07, "learning_rate": 4.63707020934037e-05, "loss": 0.3652, "step": 1457}, {"epoch": 2.07, "learning_rate": 4.624129237043544e-05, "loss": 0.3766, "step": 1458}, {"epoch": 2.07, "learning_rate": 4.611200914689917e-05, "loss": 0.3746, "step": 1459}, {"epoch": 2.07, "learning_rate": 4.5982852727010715e-05, "loss": 0.3858, "step": 1460}, {"epoch": 2.08, "learning_rate": 4.585382341468768e-05, "loss": 0.3591, "step": 1461}, {"epoch": 2.08, "learning_rate": 4.572492151354842e-05, "loss": 0.3742, "step": 1462}, {"epoch": 2.08, "learning_rate": 4.559614732691161e-05, "loss": 0.3427, "step": 1463}, {"epoch": 2.08, "learning_rate": 4.546750115779538e-05, "loss": 0.3762, "step": 1464}, {"epoch": 2.08, "learning_rate": 4.5338983308916515e-05, "loss": 0.3561, "step": 1465}, {"epoch": 2.08, "learning_rate": 4.5210594082689986e-05, "loss": 0.372, "step": 1466}, {"epoch": 2.08, "learning_rate": 4.5082333781228046e-05, "loss": 0.3947, "step": 1467}, {"epoch": 2.09, "learning_rate": 4.495420270633953e-05, "loss": 0.361, "step": 1468}, {"epoch": 2.09, "learning_rate": 4.4826201159529254e-05, "loss": 0.4011, "step": 1469}, {"epoch": 2.09, "learning_rate": 4.469832944199727e-05, "loss": 0.3988, "step": 1470}, {"epoch": 2.09, "learning_rate": 4.457058785463799e-05, "loss": 0.3918, "step": 1471}, {"epoch": 2.09, "learning_rate": 4.444297669803981e-05, "loss": 0.4062, "step": 1472}, {"epoch": 2.09, "learning_rate": 4.4315496272484e-05, "loss": 0.3627, "step": 1473}, {"epoch": 2.09, "learning_rate": 4.418814687794439e-05, "loss": 0.3138, "step": 1474}, {"epoch": 2.1, "learning_rate": 4.406092881408642e-05, "loss": 0.3679, "step": 1475}, {"epoch": 2.1, "learning_rate": 4.393384238026641e-05, "loss": 0.408, "step": 1476}, {"epoch": 2.1, "learning_rate": 4.380688787553105e-05, "loss": 0.3567, "step": 1477}, {"epoch": 2.1, "learning_rate": 4.36800655986166e-05, "loss": 0.3616, "step": 1478}, {"epoch": 2.1, "learning_rate": 4.355337584794806e-05, "loss": 0.3347, "step": 1479}, {"epoch": 2.1, "learning_rate": 4.342681892163868e-05, "loss": 0.4021, "step": 1480}, {"epoch": 2.1, "learning_rate": 4.330039511748916e-05, "loss": 0.3563, "step": 1481}, {"epoch": 2.11, "learning_rate": 4.3174104732986854e-05, "loss": 0.4089, "step": 1482}, {"epoch": 2.11, "learning_rate": 4.3047948065305274e-05, "loss": 0.402, "step": 1483}, {"epoch": 2.11, "learning_rate": 4.292192541130329e-05, "loss": 0.3976, "step": 1484}, {"epoch": 2.11, "learning_rate": 4.27960370675243e-05, "loss": 0.3815, "step": 1485}, {"epoch": 2.11, "learning_rate": 4.26702833301958e-05, "loss": 0.4277, "step": 1486}, {"epoch": 2.11, "learning_rate": 4.2544664495228425e-05, "loss": 0.3789, "step": 1487}, {"epoch": 2.11, "learning_rate": 4.241918085821547e-05, "loss": 0.3709, "step": 1488}, {"epoch": 2.12, "learning_rate": 4.2293832714432066e-05, "loss": 0.3563, "step": 1489}, {"epoch": 2.12, "learning_rate": 4.216862035883445e-05, "loss": 0.3798, "step": 1490}, {"epoch": 2.12, "learning_rate": 4.204354408605941e-05, "loss": 0.3993, "step": 1491}, {"epoch": 2.12, "learning_rate": 4.1918604190423564e-05, "loss": 0.3891, "step": 1492}, {"epoch": 2.12, "learning_rate": 4.1793800965922444e-05, "loss": 0.3743, "step": 1493}, {"epoch": 2.12, "learning_rate": 4.166913470623016e-05, "loss": 0.3602, "step": 1494}, {"epoch": 2.12, "learning_rate": 4.154460570469848e-05, "loss": 0.3975, "step": 1495}, {"epoch": 2.12, "learning_rate": 4.142021425435612e-05, "loss": 0.3985, "step": 1496}, {"epoch": 2.13, "learning_rate": 4.129596064790823e-05, "loss": 0.3562, "step": 1497}, {"epoch": 2.13, "learning_rate": 4.117184517773548e-05, "loss": 0.3961, "step": 1498}, {"epoch": 2.13, "learning_rate": 4.1047868135893605e-05, "loss": 0.3695, "step": 1499}, {"epoch": 2.13, "learning_rate": 4.0924029814112594e-05, "loss": 0.3826, "step": 1500}, {"epoch": 2.13, "learning_rate": 4.080033050379592e-05, "loss": 0.3782, "step": 1501}, {"epoch": 2.13, "learning_rate": 4.067677049602001e-05, "loss": 0.3793, "step": 1502}, {"epoch": 2.13, "learning_rate": 4.055335008153357e-05, "loss": 0.378, "step": 1503}, {"epoch": 2.14, "learning_rate": 4.0430069550756665e-05, "loss": 0.4013, "step": 1504}, {"epoch": 2.14, "learning_rate": 4.030692919378034e-05, "loss": 0.4008, "step": 1505}, {"epoch": 2.14, "learning_rate": 4.0183929300365783e-05, "loss": 0.3717, "step": 1506}, {"epoch": 2.14, "learning_rate": 4.006107015994355e-05, "loss": 0.326, "step": 1507}, {"epoch": 2.14, "learning_rate": 3.993835206161313e-05, "loss": 0.4081, "step": 1508}, {"epoch": 2.14, "learning_rate": 3.981577529414201e-05, "loss": 0.3783, "step": 1509}, {"epoch": 2.14, "learning_rate": 3.9693340145965174e-05, "loss": 0.335, "step": 1510}, {"epoch": 2.15, "learning_rate": 3.957104690518442e-05, "loss": 0.3887, "step": 1511}, {"epoch": 2.15, "learning_rate": 3.944889585956746e-05, "loss": 0.3723, "step": 1512}, {"epoch": 2.15, "learning_rate": 3.9326887296547545e-05, "loss": 0.3565, "step": 1513}, {"epoch": 2.15, "learning_rate": 3.920502150322265e-05, "loss": 0.3578, "step": 1514}, {"epoch": 2.15, "learning_rate": 3.908329876635468e-05, "loss": 0.4101, "step": 1515}, {"epoch": 2.15, "learning_rate": 3.896171937236904e-05, "loss": 0.4353, "step": 1516}, {"epoch": 2.15, "learning_rate": 3.884028360735382e-05, "loss": 0.3498, "step": 1517}, {"epoch": 2.16, "learning_rate": 3.871899175705903e-05, "loss": 0.4187, "step": 1518}, {"epoch": 2.16, "learning_rate": 3.859784410689619e-05, "loss": 0.3757, "step": 1519}, {"epoch": 2.16, "learning_rate": 3.847684094193733e-05, "loss": 0.3974, "step": 1520}, {"epoch": 2.16, "learning_rate": 3.835598254691464e-05, "loss": 0.3661, "step": 1521}, {"epoch": 2.16, "learning_rate": 3.823526920621963e-05, "loss": 0.3684, "step": 1522}, {"epoch": 2.16, "learning_rate": 3.811470120390238e-05, "loss": 0.3894, "step": 1523}, {"epoch": 2.16, "learning_rate": 3.7994278823671084e-05, "loss": 0.3684, "step": 1524}, {"epoch": 2.17, "learning_rate": 3.787400234889126e-05, "loss": 0.3787, "step": 1525}, {"epoch": 2.17, "learning_rate": 3.7753872062585e-05, "loss": 0.3777, "step": 1526}, {"epoch": 2.17, "learning_rate": 3.763388824743054e-05, "loss": 0.3974, "step": 1527}, {"epoch": 2.17, "learning_rate": 3.751405118576138e-05, "loss": 0.3387, "step": 1528}, {"epoch": 2.17, "learning_rate": 3.739436115956565e-05, "loss": 0.3548, "step": 1529}, {"epoch": 2.17, "learning_rate": 3.727481845048562e-05, "loss": 0.4111, "step": 1530}, {"epoch": 2.17, "learning_rate": 3.715542333981674e-05, "loss": 0.333, "step": 1531}, {"epoch": 2.18, "learning_rate": 3.703617610850729e-05, "loss": 0.4055, "step": 1532}, {"epoch": 2.18, "learning_rate": 3.691707703715756e-05, "loss": 0.3807, "step": 1533}, {"epoch": 2.18, "learning_rate": 3.6798126406019104e-05, "loss": 0.4324, "step": 1534}, {"epoch": 2.18, "learning_rate": 3.667932449499427e-05, "loss": 0.3841, "step": 1535}, {"epoch": 2.18, "learning_rate": 3.6560671583635467e-05, "loss": 0.3351, "step": 1536}, {"epoch": 2.18, "learning_rate": 3.644216795114439e-05, "loss": 0.332, "step": 1537}, {"epoch": 2.18, "learning_rate": 3.632381387637157e-05, "loss": 0.3479, "step": 1538}, {"epoch": 2.19, "learning_rate": 3.620560963781561e-05, "loss": 0.4957, "step": 1539}, {"epoch": 2.19, "learning_rate": 3.608755551362243e-05, "loss": 0.3465, "step": 1540}, {"epoch": 2.19, "learning_rate": 3.5969651781584854e-05, "loss": 0.4053, "step": 1541}, {"epoch": 2.19, "learning_rate": 3.5851898719141695e-05, "loss": 0.3549, "step": 1542}, {"epoch": 2.19, "learning_rate": 3.573429660337732e-05, "loss": 0.3697, "step": 1543}, {"epoch": 2.19, "learning_rate": 3.561684571102087e-05, "loss": 0.3566, "step": 1544}, {"epoch": 2.19, "learning_rate": 3.549954631844561e-05, "loss": 0.3994, "step": 1545}, {"epoch": 2.2, "learning_rate": 3.538239870166836e-05, "loss": 0.4234, "step": 1546}, {"epoch": 2.2, "learning_rate": 3.526540313634881e-05, "loss": 0.3616, "step": 1547}, {"epoch": 2.2, "learning_rate": 3.514855989778876e-05, "loss": 0.3511, "step": 1548}, {"epoch": 2.2, "learning_rate": 3.503186926093167e-05, "loss": 0.3864, "step": 1549}, {"epoch": 2.2, "learning_rate": 3.4915331500361924e-05, "loss": 0.3931, "step": 1550}, {"epoch": 2.2, "learning_rate": 3.4798946890304055e-05, "loss": 0.3609, "step": 1551}, {"epoch": 2.2, "learning_rate": 3.468271570462235e-05, "loss": 0.3803, "step": 1552}, {"epoch": 2.21, "learning_rate": 3.4566638216819956e-05, "loss": 0.4016, "step": 1553}, {"epoch": 2.21, "learning_rate": 3.4450714700038453e-05, "loss": 0.3951, "step": 1554}, {"epoch": 2.21, "learning_rate": 3.433494542705712e-05, "loss": 0.3438, "step": 1555}, {"epoch": 2.21, "learning_rate": 3.421933067029214e-05, "loss": 0.4265, "step": 1556}, {"epoch": 2.21, "learning_rate": 3.410387070179626e-05, "loss": 0.3884, "step": 1557}, {"epoch": 2.21, "learning_rate": 3.398856579325796e-05, "loss": 0.3637, "step": 1558}, {"epoch": 2.21, "learning_rate": 3.3873416216000785e-05, "loss": 0.3623, "step": 1559}, {"epoch": 2.22, "learning_rate": 3.375842224098281e-05, "loss": 0.4013, "step": 1560}, {"epoch": 2.22, "learning_rate": 3.364358413879604e-05, "loss": 0.3705, "step": 1561}, {"epoch": 2.22, "learning_rate": 3.352890217966551e-05, "loss": 0.3579, "step": 1562}, {"epoch": 2.22, "learning_rate": 3.3414376633449054e-05, "loss": 0.4463, "step": 1563}, {"epoch": 2.22, "learning_rate": 3.330000776963626e-05, "loss": 0.3712, "step": 1564}, {"epoch": 2.22, "learning_rate": 3.3185795857348145e-05, "loss": 0.3719, "step": 1565}, {"epoch": 2.22, "learning_rate": 3.307174116533641e-05, "loss": 0.3473, "step": 1566}, {"epoch": 2.23, "learning_rate": 3.295784396198269e-05, "loss": 0.3725, "step": 1567}, {"epoch": 2.23, "learning_rate": 3.2844104515298155e-05, "loss": 0.3585, "step": 1568}, {"epoch": 2.23, "learning_rate": 3.2730523092922724e-05, "loss": 0.3363, "step": 1569}, {"epoch": 2.23, "learning_rate": 3.2617099962124396e-05, "loss": 0.3809, "step": 1570}, {"epoch": 2.23, "learning_rate": 3.250383538979879e-05, "loss": 0.365, "step": 1571}, {"epoch": 2.23, "learning_rate": 3.239072964246842e-05, "loss": 0.4257, "step": 1572}, {"epoch": 2.23, "learning_rate": 3.2277782986281954e-05, "loss": 0.3647, "step": 1573}, {"epoch": 2.24, "learning_rate": 3.2164995687013876e-05, "loss": 0.4153, "step": 1574}, {"epoch": 2.24, "learning_rate": 3.205236801006351e-05, "loss": 0.3647, "step": 1575}, {"epoch": 2.24, "learning_rate": 3.19399002204547e-05, "loss": 0.3934, "step": 1576}, {"epoch": 2.24, "learning_rate": 3.182759258283504e-05, "loss": 0.4146, "step": 1577}, {"epoch": 2.24, "learning_rate": 3.171544536147521e-05, "loss": 0.3907, "step": 1578}, {"epoch": 2.24, "learning_rate": 3.160345882026845e-05, "loss": 0.348, "step": 1579}, {"epoch": 2.24, "learning_rate": 3.1491633222729975e-05, "loss": 0.3529, "step": 1580}, {"epoch": 2.25, "learning_rate": 3.137996883199614e-05, "loss": 0.3539, "step": 1581}, {"epoch": 2.25, "learning_rate": 3.126846591082409e-05, "loss": 0.4146, "step": 1582}, {"epoch": 2.25, "learning_rate": 3.115712472159097e-05, "loss": 0.3693, "step": 1583}, {"epoch": 2.25, "learning_rate": 3.104594552629331e-05, "loss": 0.358, "step": 1584}, {"epoch": 2.25, "learning_rate": 3.093492858654656e-05, "loss": 0.4097, "step": 1585}, {"epoch": 2.25, "learning_rate": 3.082407416358424e-05, "loss": 0.3734, "step": 1586}, {"epoch": 2.25, "learning_rate": 3.071338251825753e-05, "loss": 0.4049, "step": 1587}, {"epoch": 2.26, "learning_rate": 3.0602853911034624e-05, "loss": 0.3526, "step": 1588}, {"epoch": 2.26, "learning_rate": 3.0492488601999926e-05, "loss": 0.3792, "step": 1589}, {"epoch": 2.26, "learning_rate": 3.0382286850853703e-05, "loss": 0.3658, "step": 1590}, {"epoch": 2.26, "learning_rate": 3.0272248916911362e-05, "loss": 0.3925, "step": 1591}, {"epoch": 2.26, "learning_rate": 3.016237505910272e-05, "loss": 0.3526, "step": 1592}, {"epoch": 2.26, "learning_rate": 3.0052665535971612e-05, "loss": 0.3367, "step": 1593}, {"epoch": 2.26, "learning_rate": 2.9943120605675177e-05, "loss": 0.3918, "step": 1594}, {"epoch": 2.27, "learning_rate": 2.9833740525983155e-05, "loss": 0.3692, "step": 1595}, {"epoch": 2.27, "learning_rate": 2.9724525554277495e-05, "loss": 0.3742, "step": 1596}, {"epoch": 2.27, "learning_rate": 2.961547594755152e-05, "loss": 0.3309, "step": 1597}, {"epoch": 2.27, "learning_rate": 2.9506591962409512e-05, "loss": 0.3123, "step": 1598}, {"epoch": 2.27, "learning_rate": 2.939787385506604e-05, "loss": 0.363, "step": 1599}, {"epoch": 2.27, "learning_rate": 2.9289321881345254e-05, "loss": 0.3592, "step": 1600}, {"epoch": 2.27, "learning_rate": 2.918093629668046e-05, "loss": 0.3599, "step": 1601}, {"epoch": 2.28, "learning_rate": 2.9072717356113454e-05, "loss": 0.3358, "step": 1602}, {"epoch": 2.28, "learning_rate": 2.8964665314293772e-05, "loss": 0.3932, "step": 1603}, {"epoch": 2.28, "learning_rate": 2.8856780425478347e-05, "loss": 0.3854, "step": 1604}, {"epoch": 2.28, "learning_rate": 2.874906294353078e-05, "loss": 0.3858, "step": 1605}, {"epoch": 2.28, "learning_rate": 2.8641513121920637e-05, "loss": 0.3631, "step": 1606}, {"epoch": 2.28, "learning_rate": 2.8534131213723113e-05, "loss": 0.4131, "step": 1607}, {"epoch": 2.28, "learning_rate": 2.8426917471618144e-05, "loss": 0.3817, "step": 1608}, {"epoch": 2.29, "learning_rate": 2.8319872147890048e-05, "loss": 0.3705, "step": 1609}, {"epoch": 2.29, "learning_rate": 2.8212995494426842e-05, "loss": 0.3549, "step": 1610}, {"epoch": 2.29, "learning_rate": 2.8106287762719562e-05, "loss": 0.3865, "step": 1611}, {"epoch": 2.29, "learning_rate": 2.7999749203861836e-05, "loss": 0.4002, "step": 1612}, {"epoch": 2.29, "learning_rate": 2.7893380068549202e-05, "loss": 0.3388, "step": 1613}, {"epoch": 2.29, "learning_rate": 2.7787180607078477e-05, "loss": 0.384, "step": 1614}, {"epoch": 2.29, "learning_rate": 2.7681151069347256e-05, "loss": 0.4, "step": 1615}, {"epoch": 2.3, "learning_rate": 2.7575291704853323e-05, "loss": 0.3852, "step": 1616}, {"epoch": 2.3, "learning_rate": 2.7469602762693925e-05, "loss": 0.3819, "step": 1617}, {"epoch": 2.3, "learning_rate": 2.7364084491565424e-05, "loss": 0.3642, "step": 1618}, {"epoch": 2.3, "learning_rate": 2.7258737139762437e-05, "loss": 0.5173, "step": 1619}, {"epoch": 2.3, "learning_rate": 2.7153560955177483e-05, "loss": 0.3791, "step": 1620}, {"epoch": 2.3, "learning_rate": 2.7048556185300323e-05, "loss": 0.3564, "step": 1621}, {"epoch": 2.3, "learning_rate": 2.6943723077217252e-05, "loss": 0.4162, "step": 1622}, {"epoch": 2.31, "learning_rate": 2.6839061877610737e-05, "loss": 0.3621, "step": 1623}, {"epoch": 2.31, "learning_rate": 2.673457283275873e-05, "loss": 0.3729, "step": 1624}, {"epoch": 2.31, "learning_rate": 2.6630256188533973e-05, "loss": 0.3821, "step": 1625}, {"epoch": 2.31, "learning_rate": 2.652611219040365e-05, "loss": 0.4724, "step": 1626}, {"epoch": 2.31, "learning_rate": 2.6422141083428664e-05, "loss": 0.3887, "step": 1627}, {"epoch": 2.31, "learning_rate": 2.6318343112263012e-05, "loss": 0.3851, "step": 1628}, {"epoch": 2.31, "learning_rate": 2.6214718521153424e-05, "loss": 0.3813, "step": 1629}, {"epoch": 2.32, "learning_rate": 2.6111267553938502e-05, "loss": 0.3666, "step": 1630}, {"epoch": 2.32, "learning_rate": 2.600799045404838e-05, "loss": 0.378, "step": 1631}, {"epoch": 2.32, "learning_rate": 2.5904887464504114e-05, "loss": 0.3823, "step": 1632}, {"epoch": 2.32, "learning_rate": 2.5801958827916904e-05, "loss": 0.3512, "step": 1633}, {"epoch": 2.32, "learning_rate": 2.569920478648783e-05, "loss": 0.3834, "step": 1634}, {"epoch": 2.32, "learning_rate": 2.5596625582007095e-05, "loss": 0.3356, "step": 1635}, {"epoch": 2.32, "learning_rate": 2.5494221455853408e-05, "loss": 0.4955, "step": 1636}, {"epoch": 2.33, "learning_rate": 2.5391992648993613e-05, "loss": 0.4108, "step": 1637}, {"epoch": 2.33, "learning_rate": 2.5289939401982e-05, "loss": 0.3584, "step": 1638}, {"epoch": 2.33, "learning_rate": 2.518806195495964e-05, "loss": 0.3885, "step": 1639}, {"epoch": 2.33, "learning_rate": 2.5086360547654087e-05, "loss": 0.4296, "step": 1640}, {"epoch": 2.33, "learning_rate": 2.4984835419378505e-05, "loss": 0.339, "step": 1641}, {"epoch": 2.33, "learning_rate": 2.4883486809031354e-05, "loss": 0.3679, "step": 1642}, {"epoch": 2.33, "learning_rate": 2.4782314955095754e-05, "loss": 0.389, "step": 1643}, {"epoch": 2.34, "learning_rate": 2.468132009563876e-05, "loss": 0.3992, "step": 1644}, {"epoch": 2.34, "learning_rate": 2.4580502468311084e-05, "loss": 0.3617, "step": 1645}, {"epoch": 2.34, "learning_rate": 2.447986231034636e-05, "loss": 0.4039, "step": 1646}, {"epoch": 2.34, "learning_rate": 2.4379399858560557e-05, "loss": 0.3759, "step": 1647}, {"epoch": 2.34, "learning_rate": 2.4279115349351543e-05, "loss": 0.3422, "step": 1648}, {"epoch": 2.34, "learning_rate": 2.4179009018698484e-05, "loss": 0.3394, "step": 1649}, {"epoch": 2.34, "learning_rate": 2.4079081102161195e-05, "loss": 0.4242, "step": 1650}, {"epoch": 2.35, "learning_rate": 2.397933183487978e-05, "loss": 0.362, "step": 1651}, {"epoch": 2.35, "learning_rate": 2.3879761451573835e-05, "loss": 0.4077, "step": 1652}, {"epoch": 2.35, "learning_rate": 2.378037018654211e-05, "loss": 0.416, "step": 1653}, {"epoch": 2.35, "learning_rate": 2.3681158273661896e-05, "loss": 0.3351, "step": 1654}, {"epoch": 2.35, "learning_rate": 2.3582125946388334e-05, "loss": 0.4254, "step": 1655}, {"epoch": 2.35, "learning_rate": 2.3483273437754107e-05, "loss": 0.364, "step": 1656}, {"epoch": 2.35, "learning_rate": 2.3384600980368733e-05, "loss": 0.385, "step": 1657}, {"epoch": 2.36, "learning_rate": 2.328610880641797e-05, "loss": 0.3998, "step": 1658}, {"epoch": 2.36, "learning_rate": 2.318779714766346e-05, "loss": 0.3478, "step": 1659}, {"epoch": 2.36, "learning_rate": 2.3089666235442054e-05, "loss": 0.3372, "step": 1660}, {"epoch": 2.36, "learning_rate": 2.2991716300665213e-05, "loss": 0.3252, "step": 1661}, {"epoch": 2.36, "learning_rate": 2.289394757381864e-05, "loss": 0.3716, "step": 1662}, {"epoch": 2.36, "learning_rate": 2.279636028496156e-05, "loss": 0.3697, "step": 1663}, {"epoch": 2.36, "learning_rate": 2.26989546637263e-05, "loss": 0.3833, "step": 1664}, {"epoch": 2.37, "learning_rate": 2.2601730939317732e-05, "loss": 0.3823, "step": 1665}, {"epoch": 2.37, "learning_rate": 2.250468934051262e-05, "loss": 0.3636, "step": 1666}, {"epoch": 2.37, "learning_rate": 2.2407830095659233e-05, "loss": 0.3467, "step": 1667}, {"epoch": 2.37, "learning_rate": 2.2311153432676768e-05, "loss": 0.3508, "step": 1668}, {"epoch": 2.37, "learning_rate": 2.2214659579054698e-05, "loss": 0.3733, "step": 1669}, {"epoch": 2.37, "learning_rate": 2.2118348761852403e-05, "loss": 0.3963, "step": 1670}, {"epoch": 2.37, "learning_rate": 2.202222120769857e-05, "loss": 0.3417, "step": 1671}, {"epoch": 2.38, "learning_rate": 2.1926277142790552e-05, "loss": 0.3397, "step": 1672}, {"epoch": 2.38, "learning_rate": 2.1830516792894085e-05, "loss": 0.3894, "step": 1673}, {"epoch": 2.38, "learning_rate": 2.173494038334244e-05, "loss": 0.3693, "step": 1674}, {"epoch": 2.38, "learning_rate": 2.1639548139036182e-05, "loss": 0.3571, "step": 1675}, {"epoch": 2.38, "learning_rate": 2.15443402844425e-05, "loss": 0.3465, "step": 1676}, {"epoch": 2.38, "learning_rate": 2.144931704359461e-05, "loss": 0.3568, "step": 1677}, {"epoch": 2.38, "learning_rate": 2.1354478640091424e-05, "loss": 0.3859, "step": 1678}, {"epoch": 2.38, "learning_rate": 2.1259825297096882e-05, "loss": 0.3683, "step": 1679}, {"epoch": 2.39, "learning_rate": 2.116535723733938e-05, "loss": 0.3912, "step": 1680}, {"epoch": 2.39, "learning_rate": 2.1071074683111426e-05, "loss": 0.3604, "step": 1681}, {"epoch": 2.39, "learning_rate": 2.0976977856269008e-05, "loss": 0.3716, "step": 1682}, {"epoch": 2.39, "learning_rate": 2.0883066978230982e-05, "loss": 0.3455, "step": 1683}, {"epoch": 2.39, "learning_rate": 2.0789342269978785e-05, "loss": 0.3572, "step": 1684}, {"epoch": 2.39, "learning_rate": 2.069580395205565e-05, "loss": 0.3825, "step": 1685}, {"epoch": 2.39, "learning_rate": 2.0602452244566283e-05, "loss": 0.3781, "step": 1686}, {"epoch": 2.4, "learning_rate": 2.0509287367176323e-05, "loss": 0.3588, "step": 1687}, {"epoch": 2.4, "learning_rate": 2.0416309539111654e-05, "loss": 0.3751, "step": 1688}, {"epoch": 2.4, "learning_rate": 2.032351897915812e-05, "loss": 0.3572, "step": 1689}, {"epoch": 2.4, "learning_rate": 2.0230915905660906e-05, "loss": 0.3604, "step": 1690}, {"epoch": 2.4, "learning_rate": 2.013850053652392e-05, "loss": 0.3545, "step": 1691}, {"epoch": 2.4, "learning_rate": 2.0046273089209487e-05, "loss": 0.3663, "step": 1692}, {"epoch": 2.4, "learning_rate": 1.995423378073773e-05, "loss": 0.3656, "step": 1693}, {"epoch": 2.41, "learning_rate": 1.986238282768598e-05, "loss": 0.3806, "step": 1694}, {"epoch": 2.41, "learning_rate": 1.977072044618845e-05, "loss": 0.3548, "step": 1695}, {"epoch": 2.41, "learning_rate": 1.967924685193552e-05, "loss": 0.3721, "step": 1696}, {"epoch": 2.41, "learning_rate": 1.9587962260173433e-05, "loss": 0.3939, "step": 1697}, {"epoch": 2.41, "learning_rate": 1.9496866885703657e-05, "loss": 0.3668, "step": 1698}, {"epoch": 2.41, "learning_rate": 1.9405960942882373e-05, "loss": 0.3694, "step": 1699}, {"epoch": 2.41, "learning_rate": 1.9315244645620067e-05, "loss": 0.523, "step": 1700}, {"epoch": 2.42, "learning_rate": 1.9224718207380976e-05, "loss": 0.3301, "step": 1701}, {"epoch": 2.42, "learning_rate": 1.9134381841182503e-05, "loss": 0.4055, "step": 1702}, {"epoch": 2.42, "learning_rate": 1.9044235759594863e-05, "loss": 0.4393, "step": 1703}, {"epoch": 2.42, "learning_rate": 1.8954280174740537e-05, "loss": 0.3598, "step": 1704}, {"epoch": 2.42, "learning_rate": 1.8864515298293626e-05, "loss": 0.3677, "step": 1705}, {"epoch": 2.42, "learning_rate": 1.8774941341479623e-05, "loss": 0.4122, "step": 1706}, {"epoch": 2.42, "learning_rate": 1.868555851507465e-05, "loss": 0.355, "step": 1707}, {"epoch": 2.43, "learning_rate": 1.859636702940516e-05, "loss": 0.3554, "step": 1708}, {"epoch": 2.43, "learning_rate": 1.8507367094347363e-05, "loss": 0.3736, "step": 1709}, {"epoch": 2.43, "learning_rate": 1.8418558919326635e-05, "loss": 0.3948, "step": 1710}, {"epoch": 2.43, "learning_rate": 1.8329942713317216e-05, "loss": 0.3818, "step": 1711}, {"epoch": 2.43, "learning_rate": 1.824151868484164e-05, "loss": 0.348, "step": 1712}, {"epoch": 2.43, "learning_rate": 1.8153287041970136e-05, "loss": 0.3538, "step": 1713}, {"epoch": 2.43, "learning_rate": 1.80652479923203e-05, "loss": 0.3332, "step": 1714}, {"epoch": 2.44, "learning_rate": 1.7977401743056544e-05, "loss": 0.3692, "step": 1715}, {"epoch": 2.44, "learning_rate": 1.7889748500889536e-05, "loss": 0.3744, "step": 1716}, {"epoch": 2.44, "learning_rate": 1.7802288472075867e-05, "loss": 0.3586, "step": 1717}, {"epoch": 2.44, "learning_rate": 1.771502186241738e-05, "loss": 0.3877, "step": 1718}, {"epoch": 2.44, "learning_rate": 1.762794887726086e-05, "loss": 0.3711, "step": 1719}, {"epoch": 2.44, "learning_rate": 1.7541069721497493e-05, "loss": 0.371, "step": 1720}, {"epoch": 2.44, "learning_rate": 1.7454384599562256e-05, "loss": 0.39, "step": 1721}, {"epoch": 2.45, "learning_rate": 1.7367893715433647e-05, "loss": 0.386, "step": 1722}, {"epoch": 2.45, "learning_rate": 1.7281597272633098e-05, "loss": 0.3938, "step": 1723}, {"epoch": 2.45, "learning_rate": 1.719549547422443e-05, "loss": 0.3417, "step": 1724}, {"epoch": 2.45, "learning_rate": 1.7109588522813502e-05, "loss": 0.3619, "step": 1725}, {"epoch": 2.45, "learning_rate": 1.7023876620547707e-05, "loss": 0.3834, "step": 1726}, {"epoch": 2.45, "learning_rate": 1.693835996911537e-05, "loss": 0.3976, "step": 1727}, {"epoch": 2.45, "learning_rate": 1.6853038769745467e-05, "loss": 0.3542, "step": 1728}, {"epoch": 2.46, "learning_rate": 1.6767913223207044e-05, "loss": 0.3407, "step": 1729}, {"epoch": 2.46, "learning_rate": 1.6682983529808683e-05, "loss": 0.348, "step": 1730}, {"epoch": 2.46, "learning_rate": 1.65982498893982e-05, "loss": 0.3911, "step": 1731}, {"epoch": 2.46, "learning_rate": 1.6513712501362e-05, "loss": 0.3784, "step": 1732}, {"epoch": 2.46, "learning_rate": 1.642937156462474e-05, "loss": 0.3383, "step": 1733}, {"epoch": 2.46, "learning_rate": 1.6345227277648812e-05, "loss": 0.3888, "step": 1734}, {"epoch": 2.46, "learning_rate": 1.6261279838433817e-05, "loss": 0.3574, "step": 1735}, {"epoch": 2.47, "learning_rate": 1.6177529444516194e-05, "loss": 0.3468, "step": 1736}, {"epoch": 2.47, "learning_rate": 1.6093976292968737e-05, "loss": 0.3573, "step": 1737}, {"epoch": 2.47, "learning_rate": 1.6010620580400047e-05, "loss": 0.3259, "step": 1738}, {"epoch": 2.47, "learning_rate": 1.5927462502954194e-05, "loss": 0.3422, "step": 1739}, {"epoch": 2.47, "learning_rate": 1.5844502256310167e-05, "loss": 0.3762, "step": 1740}, {"epoch": 2.47, "learning_rate": 1.5761740035681417e-05, "loss": 0.3777, "step": 1741}, {"epoch": 2.47, "learning_rate": 1.567917603581547e-05, "loss": 0.3828, "step": 1742}, {"epoch": 2.48, "learning_rate": 1.559681045099336e-05, "loss": 0.3779, "step": 1743}, {"epoch": 2.48, "learning_rate": 1.551464347502929e-05, "loss": 0.3732, "step": 1744}, {"epoch": 2.48, "learning_rate": 1.5432675301270104e-05, "loss": 0.3976, "step": 1745}, {"epoch": 2.48, "learning_rate": 1.53509061225948e-05, "loss": 0.349, "step": 1746}, {"epoch": 2.48, "learning_rate": 1.5269336131414158e-05, "loss": 0.3642, "step": 1747}, {"epoch": 2.48, "learning_rate": 1.5187965519670289e-05, "loss": 0.3587, "step": 1748}, {"epoch": 2.48, "learning_rate": 1.5106794478836039e-05, "loss": 0.3616, "step": 1749}, {"epoch": 2.49, "learning_rate": 1.5025823199914747e-05, "loss": 0.3827, "step": 1750}, {"epoch": 2.49, "learning_rate": 1.4945051873439663e-05, "loss": 0.3472, "step": 1751}, {"epoch": 2.49, "learning_rate": 1.486448068947348e-05, "loss": 0.4445, "step": 1752}, {"epoch": 2.49, "learning_rate": 1.478410983760804e-05, "loss": 0.354, "step": 1753}, {"epoch": 2.49, "learning_rate": 1.4703939506963637e-05, "loss": 0.4078, "step": 1754}, {"epoch": 2.49, "learning_rate": 1.4623969886188859e-05, "loss": 0.3772, "step": 1755}, {"epoch": 2.49, "learning_rate": 1.4544201163459959e-05, "loss": 0.3439, "step": 1756}, {"epoch": 2.5, "learning_rate": 1.4464633526480409e-05, "loss": 0.3736, "step": 1757}, {"epoch": 2.5, "learning_rate": 1.4385267162480554e-05, "loss": 0.3736, "step": 1758}, {"epoch": 2.5, "learning_rate": 1.4306102258217136e-05, "loss": 0.3355, "step": 1759}, {"epoch": 2.5, "learning_rate": 1.42271389999728e-05, "loss": 0.3651, "step": 1760}, {"epoch": 2.5, "learning_rate": 1.4148377573555716e-05, "loss": 0.3937, "step": 1761}, {"epoch": 2.5, "learning_rate": 1.4069818164299165e-05, "loss": 0.357, "step": 1762}, {"epoch": 2.5, "learning_rate": 1.3991460957060986e-05, "loss": 0.3427, "step": 1763}, {"epoch": 2.51, "learning_rate": 1.3913306136223292e-05, "loss": 0.3313, "step": 1764}, {"epoch": 2.51, "learning_rate": 1.3835353885691871e-05, "loss": 0.3694, "step": 1765}, {"epoch": 2.51, "learning_rate": 1.3757604388895951e-05, "loss": 0.3929, "step": 1766}, {"epoch": 2.51, "learning_rate": 1.3680057828787595e-05, "loss": 0.3922, "step": 1767}, {"epoch": 2.51, "learning_rate": 1.360271438784133e-05, "loss": 0.3783, "step": 1768}, {"epoch": 2.51, "learning_rate": 1.3525574248053763e-05, "loss": 0.3947, "step": 1769}, {"epoch": 2.51, "learning_rate": 1.3448637590943103e-05, "loss": 0.3553, "step": 1770}, {"epoch": 2.52, "learning_rate": 1.33719045975487e-05, "loss": 0.409, "step": 1771}, {"epoch": 2.52, "learning_rate": 1.3295375448430724e-05, "loss": 0.3684, "step": 1772}, {"epoch": 2.52, "learning_rate": 1.321905032366968e-05, "loss": 0.3553, "step": 1773}, {"epoch": 2.52, "learning_rate": 1.314292940286591e-05, "loss": 0.3873, "step": 1774}, {"epoch": 2.52, "learning_rate": 1.3067012865139339e-05, "loss": 0.3498, "step": 1775}, {"epoch": 2.52, "learning_rate": 1.2991300889128866e-05, "loss": 0.3642, "step": 1776}, {"epoch": 2.52, "learning_rate": 1.2915793652992104e-05, "loss": 0.3528, "step": 1777}, {"epoch": 2.53, "learning_rate": 1.2840491334404913e-05, "loss": 0.3965, "step": 1778}, {"epoch": 2.53, "learning_rate": 1.2765394110560858e-05, "loss": 0.3565, "step": 1779}, {"epoch": 2.53, "learning_rate": 1.269050215817099e-05, "loss": 0.347, "step": 1780}, {"epoch": 2.53, "learning_rate": 1.2615815653463325e-05, "loss": 0.4032, "step": 1781}, {"epoch": 2.53, "learning_rate": 1.254133477218239e-05, "loss": 0.3768, "step": 1782}, {"epoch": 2.53, "learning_rate": 1.2467059689588912e-05, "loss": 0.3973, "step": 1783}, {"epoch": 2.53, "learning_rate": 1.2392990580459352e-05, "loss": 0.3585, "step": 1784}, {"epoch": 2.54, "learning_rate": 1.2319127619085435e-05, "loss": 0.3968, "step": 1785}, {"epoch": 2.54, "learning_rate": 1.2245470979273887e-05, "loss": 0.3439, "step": 1786}, {"epoch": 2.54, "learning_rate": 1.2172020834345853e-05, "loss": 0.4507, "step": 1787}, {"epoch": 2.54, "learning_rate": 1.2098777357136648e-05, "loss": 0.3799, "step": 1788}, {"epoch": 2.54, "learning_rate": 1.2025740719995271e-05, "loss": 0.359, "step": 1789}, {"epoch": 2.54, "learning_rate": 1.1952911094783926e-05, "loss": 0.3637, "step": 1790}, {"epoch": 2.54, "learning_rate": 1.188028865287779e-05, "loss": 0.3319, "step": 1791}, {"epoch": 2.55, "learning_rate": 1.1807873565164506e-05, "loss": 0.4141, "step": 1792}, {"epoch": 2.55, "learning_rate": 1.1735666002043722e-05, "loss": 0.4041, "step": 1793}, {"epoch": 2.55, "learning_rate": 1.1663666133426832e-05, "loss": 0.3641, "step": 1794}, {"epoch": 2.55, "learning_rate": 1.1591874128736513e-05, "loss": 0.3644, "step": 1795}, {"epoch": 2.55, "learning_rate": 1.1520290156906221e-05, "loss": 0.3872, "step": 1796}, {"epoch": 2.55, "learning_rate": 1.1448914386380016e-05, "loss": 0.364, "step": 1797}, {"epoch": 2.55, "learning_rate": 1.1377746985111947e-05, "loss": 0.3834, "step": 1798}, {"epoch": 2.56, "learning_rate": 1.130678812056578e-05, "loss": 0.373, "step": 1799}, {"epoch": 2.56, "learning_rate": 1.1236037959714618e-05, "loss": 0.3855, "step": 1800}, {"epoch": 2.56, "learning_rate": 1.1165496669040377e-05, "loss": 0.4163, "step": 1801}, {"epoch": 2.56, "learning_rate": 1.1095164414533543e-05, "loss": 0.3233, "step": 1802}, {"epoch": 2.56, "learning_rate": 1.1025041361692734e-05, "loss": 0.4281, "step": 1803}, {"epoch": 2.56, "learning_rate": 1.0955127675524214e-05, "loss": 0.3551, "step": 1804}, {"epoch": 2.56, "learning_rate": 1.088542352054167e-05, "loss": 0.4001, "step": 1805}, {"epoch": 2.57, "learning_rate": 1.0815929060765739e-05, "loss": 0.3819, "step": 1806}, {"epoch": 2.57, "learning_rate": 1.0746644459723542e-05, "loss": 0.3733, "step": 1807}, {"epoch": 2.57, "learning_rate": 1.067756988044848e-05, "loss": 0.368, "step": 1808}, {"epoch": 2.57, "learning_rate": 1.0608705485479686e-05, "loss": 0.3954, "step": 1809}, {"epoch": 2.57, "learning_rate": 1.054005143686173e-05, "loss": 0.3679, "step": 1810}, {"epoch": 2.57, "learning_rate": 1.0471607896144264e-05, "loss": 0.3426, "step": 1811}, {"epoch": 2.57, "learning_rate": 1.040337502438149e-05, "loss": 0.3633, "step": 1812}, {"epoch": 2.58, "learning_rate": 1.0335352982131975e-05, "loss": 0.3295, "step": 1813}, {"epoch": 2.58, "learning_rate": 1.0267541929458179e-05, "loss": 0.3587, "step": 1814}, {"epoch": 2.58, "learning_rate": 1.0199942025926024e-05, "loss": 0.357, "step": 1815}, {"epoch": 2.58, "learning_rate": 1.0132553430604608e-05, "loss": 0.3494, "step": 1816}, {"epoch": 2.58, "learning_rate": 1.0065376302065853e-05, "loss": 0.3785, "step": 1817}, {"epoch": 2.58, "learning_rate": 9.998410798383984e-06, "loss": 0.34, "step": 1818}, {"epoch": 2.58, "learning_rate": 9.931657077135326e-06, "loss": 0.3598, "step": 1819}, {"epoch": 2.59, "learning_rate": 9.865115295397808e-06, "loss": 0.3688, "step": 1820}, {"epoch": 2.59, "learning_rate": 9.798785609750683e-06, "loss": 0.3892, "step": 1821}, {"epoch": 2.59, "learning_rate": 9.73266817627413e-06, "loss": 0.3877, "step": 1822}, {"epoch": 2.59, "learning_rate": 9.666763150548818e-06, "loss": 0.3962, "step": 1823}, {"epoch": 2.59, "learning_rate": 9.601070687655667e-06, "loss": 0.4438, "step": 1824}, {"epoch": 2.59, "learning_rate": 9.535590942175388e-06, "loss": 0.3714, "step": 1825}, {"epoch": 2.59, "learning_rate": 9.47032406818813e-06, "loss": 0.3631, "step": 1826}, {"epoch": 2.6, "learning_rate": 9.405270219273155e-06, "loss": 0.3689, "step": 1827}, {"epoch": 2.6, "learning_rate": 9.340429548508468e-06, "loss": 0.3557, "step": 1828}, {"epoch": 2.6, "learning_rate": 9.275802208470418e-06, "loss": 0.3842, "step": 1829}, {"epoch": 2.6, "learning_rate": 9.211388351233396e-06, "loss": 0.3684, "step": 1830}, {"epoch": 2.6, "learning_rate": 9.147188128369388e-06, "loss": 0.3824, "step": 1831}, {"epoch": 2.6, "learning_rate": 9.083201690947763e-06, "loss": 0.3886, "step": 1832}, {"epoch": 2.6, "learning_rate": 9.019429189534789e-06, "loss": 0.3706, "step": 1833}, {"epoch": 2.61, "learning_rate": 8.955870774193287e-06, "loss": 0.3305, "step": 1834}, {"epoch": 2.61, "learning_rate": 8.892526594482364e-06, "loss": 0.3449, "step": 1835}, {"epoch": 2.61, "learning_rate": 8.829396799457024e-06, "loss": 0.3714, "step": 1836}, {"epoch": 2.61, "learning_rate": 8.766481537667725e-06, "loss": 0.3849, "step": 1837}, {"epoch": 2.61, "learning_rate": 8.70378095716018e-06, "loss": 0.3041, "step": 1838}, {"epoch": 2.61, "learning_rate": 8.64129520547492e-06, "loss": 0.3849, "step": 1839}, {"epoch": 2.61, "learning_rate": 8.579024429646932e-06, "loss": 0.3856, "step": 1840}, {"epoch": 2.62, "learning_rate": 8.516968776205403e-06, "loss": 0.3887, "step": 1841}, {"epoch": 2.62, "learning_rate": 8.455128391173228e-06, "loss": 0.3625, "step": 1842}, {"epoch": 2.62, "learning_rate": 8.393503420066829e-06, "loss": 0.3766, "step": 1843}, {"epoch": 2.62, "learning_rate": 8.332094007895741e-06, "loss": 0.3786, "step": 1844}, {"epoch": 2.62, "learning_rate": 8.27090029916221e-06, "loss": 0.3903, "step": 1845}, {"epoch": 2.62, "learning_rate": 8.20992243786095e-06, "loss": 0.3521, "step": 1846}, {"epoch": 2.62, "learning_rate": 8.149160567478787e-06, "loss": 0.3925, "step": 1847}, {"epoch": 2.62, "learning_rate": 8.088614830994223e-06, "loss": 0.3564, "step": 1848}, {"epoch": 2.63, "learning_rate": 8.028285370877263e-06, "loss": 0.3628, "step": 1849}, {"epoch": 2.63, "learning_rate": 7.968172329088952e-06, "loss": 0.3572, "step": 1850}, {"epoch": 2.63, "learning_rate": 7.908275847081059e-06, "loss": 0.3768, "step": 1851}, {"epoch": 2.63, "learning_rate": 7.84859606579582e-06, "loss": 0.3365, "step": 1852}, {"epoch": 2.63, "learning_rate": 7.789133125665493e-06, "loss": 0.3592, "step": 1853}, {"epoch": 2.63, "learning_rate": 7.729887166612138e-06, "loss": 0.3382, "step": 1854}, {"epoch": 2.63, "learning_rate": 7.670858328047248e-06, "loss": 0.4158, "step": 1855}, {"epoch": 2.64, "learning_rate": 7.612046748871327e-06, "loss": 0.3859, "step": 1856}, {"epoch": 2.64, "learning_rate": 7.55345256747374e-06, "loss": 0.4163, "step": 1857}, {"epoch": 2.64, "learning_rate": 7.495075921732253e-06, "loss": 0.3261, "step": 1858}, {"epoch": 2.64, "learning_rate": 7.436916949012729e-06, "loss": 0.354, "step": 1859}, {"epoch": 2.64, "learning_rate": 7.378975786168863e-06, "loss": 0.4653, "step": 1860}, {"epoch": 2.64, "learning_rate": 7.321252569541826e-06, "loss": 0.3828, "step": 1861}, {"epoch": 2.64, "learning_rate": 7.2637474349598886e-06, "loss": 0.4068, "step": 1862}, {"epoch": 2.65, "learning_rate": 7.2064605177382224e-06, "loss": 0.3688, "step": 1863}, {"epoch": 2.65, "learning_rate": 7.149391952678452e-06, "loss": 0.435, "step": 1864}, {"epoch": 2.65, "learning_rate": 7.092541874068425e-06, "loss": 0.3555, "step": 1865}, {"epoch": 2.65, "learning_rate": 7.035910415681879e-06, "loss": 0.378, "step": 1866}, {"epoch": 2.65, "learning_rate": 6.979497710778093e-06, "loss": 0.3716, "step": 1867}, {"epoch": 2.65, "learning_rate": 6.923303892101629e-06, "loss": 0.3919, "step": 1868}, {"epoch": 2.65, "learning_rate": 6.867329091881969e-06, "loss": 0.3433, "step": 1869}, {"epoch": 2.66, "learning_rate": 6.8115734418331965e-06, "loss": 0.3682, "step": 1870}, {"epoch": 2.66, "learning_rate": 6.756037073153754e-06, "loss": 0.3553, "step": 1871}, {"epoch": 2.66, "learning_rate": 6.700720116526116e-06, "loss": 0.4057, "step": 1872}, {"epoch": 2.66, "learning_rate": 6.645622702116383e-06, "loss": 0.3491, "step": 1873}, {"epoch": 2.66, "learning_rate": 6.5907449595741244e-06, "loss": 0.3653, "step": 1874}, {"epoch": 2.66, "learning_rate": 6.536087018031933e-06, "loss": 0.3354, "step": 1875}, {"epoch": 2.66, "learning_rate": 6.48164900610524e-06, "loss": 0.3632, "step": 1876}, {"epoch": 2.67, "learning_rate": 6.4274310518919746e-06, "loss": 0.3705, "step": 1877}, {"epoch": 2.67, "learning_rate": 6.3734332829721745e-06, "loss": 0.3261, "step": 1878}, {"epoch": 2.67, "learning_rate": 6.319655826407833e-06, "loss": 0.3353, "step": 1879}, {"epoch": 2.67, "learning_rate": 6.266098808742516e-06, "loss": 0.3479, "step": 1880}, {"epoch": 2.67, "learning_rate": 6.212762356001023e-06, "loss": 0.394, "step": 1881}, {"epoch": 2.67, "learning_rate": 6.1596465936891835e-06, "loss": 0.3541, "step": 1882}, {"epoch": 2.67, "learning_rate": 6.106751646793551e-06, "loss": 0.4012, "step": 1883}, {"epoch": 2.68, "learning_rate": 6.0540776397810085e-06, "loss": 0.3295, "step": 1884}, {"epoch": 2.68, "learning_rate": 6.001624696598618e-06, "loss": 0.3492, "step": 1885}, {"epoch": 2.68, "learning_rate": 5.9493929406731705e-06, "loss": 0.4034, "step": 1886}, {"epoch": 2.68, "learning_rate": 5.8973824949110744e-06, "loss": 0.3718, "step": 1887}, {"epoch": 2.68, "learning_rate": 5.8455934816979305e-06, "loss": 0.4913, "step": 1888}, {"epoch": 2.68, "learning_rate": 5.794026022898269e-06, "loss": 0.3726, "step": 1889}, {"epoch": 2.68, "learning_rate": 5.742680239855314e-06, "loss": 0.3457, "step": 1890}, {"epoch": 2.69, "learning_rate": 5.691556253390662e-06, "loss": 0.384, "step": 1891}, {"epoch": 2.69, "learning_rate": 5.640654183803962e-06, "loss": 0.3344, "step": 1892}, {"epoch": 2.69, "learning_rate": 5.589974150872735e-06, "loss": 0.3115, "step": 1893}, {"epoch": 2.69, "learning_rate": 5.539516273851986e-06, "loss": 0.3873, "step": 1894}, {"epoch": 2.69, "learning_rate": 5.4892806714739395e-06, "loss": 0.4037, "step": 1895}, {"epoch": 2.69, "learning_rate": 5.439267461947883e-06, "loss": 0.3945, "step": 1896}, {"epoch": 2.69, "learning_rate": 5.389476762959666e-06, "loss": 0.3914, "step": 1897}, {"epoch": 2.7, "learning_rate": 5.339908691671647e-06, "loss": 0.3488, "step": 1898}, {"epoch": 2.7, "learning_rate": 5.29056336472229e-06, "loss": 0.388, "step": 1899}, {"epoch": 2.7, "learning_rate": 5.241440898225891e-06, "loss": 0.388, "step": 1900}, {"epoch": 2.7, "learning_rate": 5.192541407772378e-06, "loss": 0.3909, "step": 1901}, {"epoch": 2.7, "learning_rate": 5.143865008426973e-06, "loss": 0.4216, "step": 1902}, {"epoch": 2.7, "learning_rate": 5.0954118147299444e-06, "loss": 0.3592, "step": 1903}, {"epoch": 2.7, "learning_rate": 5.047181940696333e-06, "loss": 0.3883, "step": 1904}, {"epoch": 2.71, "learning_rate": 4.9991754998157e-06, "loss": 0.4158, "step": 1905}, {"epoch": 2.71, "learning_rate": 4.95139260505183e-06, "loss": 0.3351, "step": 1906}, {"epoch": 2.71, "learning_rate": 4.903833368842503e-06, "loss": 0.4308, "step": 1907}, {"epoch": 2.71, "learning_rate": 4.856497903099166e-06, "loss": 0.3568, "step": 1908}, {"epoch": 2.71, "learning_rate": 4.809386319206766e-06, "loss": 0.377, "step": 1909}, {"epoch": 2.71, "learning_rate": 4.762498728023423e-06, "loss": 0.3383, "step": 1910}, {"epoch": 2.71, "learning_rate": 4.7158352398801395e-06, "loss": 0.3874, "step": 1911}, {"epoch": 2.72, "learning_rate": 4.669395964580614e-06, "loss": 0.3538, "step": 1912}, {"epoch": 2.72, "learning_rate": 4.6231810114009785e-06, "loss": 0.3509, "step": 1913}, {"epoch": 2.72, "learning_rate": 4.577190489089445e-06, "loss": 0.3413, "step": 1914}, {"epoch": 2.72, "learning_rate": 4.531424505866166e-06, "loss": 0.3249, "step": 1915}, {"epoch": 2.72, "learning_rate": 4.485883169422933e-06, "loss": 0.3848, "step": 1916}, {"epoch": 2.72, "learning_rate": 4.44056658692289e-06, "loss": 0.3754, "step": 1917}, {"epoch": 2.72, "learning_rate": 4.3954748650003704e-06, "loss": 0.3476, "step": 1918}, {"epoch": 2.73, "learning_rate": 4.350608109760501e-06, "loss": 0.3551, "step": 1919}, {"epoch": 2.73, "learning_rate": 4.305966426779118e-06, "loss": 0.3615, "step": 1920}, {"epoch": 2.73, "learning_rate": 4.261549921102415e-06, "loss": 0.3789, "step": 1921}, {"epoch": 2.73, "learning_rate": 4.217358697246709e-06, "loss": 0.4895, "step": 1922}, {"epoch": 2.73, "learning_rate": 4.173392859198233e-06, "loss": 0.3764, "step": 1923}, {"epoch": 2.73, "learning_rate": 4.129652510412851e-06, "loss": 0.355, "step": 1924}, {"epoch": 2.73, "learning_rate": 4.086137753815811e-06, "loss": 0.4053, "step": 1925}, {"epoch": 2.74, "learning_rate": 4.042848691801548e-06, "loss": 0.3411, "step": 1926}, {"epoch": 2.74, "learning_rate": 3.999785426233416e-06, "loss": 0.383, "step": 1927}, {"epoch": 2.74, "learning_rate": 3.9569480584434216e-06, "loss": 0.3592, "step": 1928}, {"epoch": 2.74, "learning_rate": 3.914336689232045e-06, "loss": 0.3526, "step": 1929}, {"epoch": 2.74, "learning_rate": 3.871951418867936e-06, "loss": 0.4074, "step": 1930}, {"epoch": 2.74, "learning_rate": 3.829792347087746e-06, "loss": 0.3604, "step": 1931}, {"epoch": 2.74, "learning_rate": 3.787859573095853e-06, "loss": 0.3927, "step": 1932}, {"epoch": 2.75, "learning_rate": 3.7461531955640837e-06, "loss": 0.3594, "step": 1933}, {"epoch": 2.75, "learning_rate": 3.7046733126316126e-06, "loss": 0.3447, "step": 1934}, {"epoch": 2.75, "learning_rate": 3.6634200219046067e-06, "loss": 0.3633, "step": 1935}, {"epoch": 2.75, "learning_rate": 3.622393420456016e-06, "loss": 0.38, "step": 1936}, {"epoch": 2.75, "learning_rate": 3.5815936048254173e-06, "loss": 0.3501, "step": 1937}, {"epoch": 2.75, "learning_rate": 3.5410206710187353e-06, "loss": 0.3789, "step": 1938}, {"epoch": 2.75, "learning_rate": 3.5006747145079675e-06, "loss": 0.3736, "step": 1939}, {"epoch": 2.76, "learning_rate": 3.4605558302310715e-06, "loss": 0.3886, "step": 1940}, {"epoch": 2.76, "learning_rate": 3.4206641125916427e-06, "loss": 0.3963, "step": 1941}, {"epoch": 2.76, "learning_rate": 3.3809996554587497e-06, "loss": 0.3226, "step": 1942}, {"epoch": 2.76, "learning_rate": 3.3415625521666995e-06, "loss": 0.3769, "step": 1943}, {"epoch": 2.76, "learning_rate": 3.302352895514793e-06, "loss": 0.3434, "step": 1944}, {"epoch": 2.76, "learning_rate": 3.2633707777671494e-06, "loss": 0.3302, "step": 1945}, {"epoch": 2.76, "learning_rate": 3.22461629065246e-06, "loss": 0.3695, "step": 1946}, {"epoch": 2.77, "learning_rate": 3.1860895253637667e-06, "loss": 0.4102, "step": 1947}, {"epoch": 2.77, "learning_rate": 3.1477905725582623e-06, "loss": 0.3692, "step": 1948}, {"epoch": 2.77, "learning_rate": 3.109719522357113e-06, "loss": 0.3984, "step": 1949}, {"epoch": 2.77, "learning_rate": 3.0718764643451582e-06, "loss": 0.365, "step": 1950}, {"epoch": 2.77, "learning_rate": 3.0342614875707664e-06, "loss": 0.3408, "step": 1951}, {"epoch": 2.77, "learning_rate": 2.996874680545603e-06, "loss": 0.3977, "step": 1952}, {"epoch": 2.77, "learning_rate": 2.95971613124445e-06, "loss": 0.3579, "step": 1953}, {"epoch": 2.78, "learning_rate": 2.9227859271049763e-06, "loss": 0.3797, "step": 1954}, {"epoch": 2.78, "learning_rate": 2.886084155027491e-06, "loss": 0.3741, "step": 1955}, {"epoch": 2.78, "learning_rate": 2.8496109013748217e-06, "loss": 0.38, "step": 1956}, {"epoch": 2.78, "learning_rate": 2.8133662519720716e-06, "loss": 0.3866, "step": 1957}, {"epoch": 2.78, "learning_rate": 2.7773502921063733e-06, "loss": 0.3703, "step": 1958}, {"epoch": 2.78, "learning_rate": 2.741563106526779e-06, "loss": 0.3597, "step": 1959}, {"epoch": 2.78, "learning_rate": 2.7060047794439936e-06, "loss": 0.3775, "step": 1960}, {"epoch": 2.79, "learning_rate": 2.6706753945301753e-06, "loss": 0.4479, "step": 1961}, {"epoch": 2.79, "learning_rate": 2.6355750349188136e-06, "loss": 0.3817, "step": 1962}, {"epoch": 2.79, "learning_rate": 2.600703783204417e-06, "loss": 0.3581, "step": 1963}, {"epoch": 2.79, "learning_rate": 2.5660617214424145e-06, "loss": 0.3987, "step": 1964}, {"epoch": 2.79, "learning_rate": 2.531648931148933e-06, "loss": 0.4075, "step": 1965}, {"epoch": 2.79, "learning_rate": 2.497465493300588e-06, "loss": 0.4034, "step": 1966}, {"epoch": 2.79, "learning_rate": 2.4635114883343023e-06, "loss": 0.3766, "step": 1967}, {"epoch": 2.8, "learning_rate": 2.429786996147154e-06, "loss": 0.3439, "step": 1968}, {"epoch": 2.8, "learning_rate": 2.3962920960960975e-06, "loss": 0.3544, "step": 1969}, {"epoch": 2.8, "learning_rate": 2.363026866997886e-06, "loss": 0.3498, "step": 1970}, {"epoch": 2.8, "learning_rate": 2.329991387128827e-06, "loss": 0.3508, "step": 1971}, {"epoch": 2.8, "learning_rate": 2.2971857342245606e-06, "loss": 0.376, "step": 1972}, {"epoch": 2.8, "learning_rate": 2.264609985480004e-06, "loss": 0.3605, "step": 1973}, {"epoch": 2.8, "learning_rate": 2.2322642175490073e-06, "loss": 0.3845, "step": 1974}, {"epoch": 2.81, "learning_rate": 2.2001485065442863e-06, "loss": 0.3774, "step": 1975}, {"epoch": 2.81, "learning_rate": 2.1682629280372456e-06, "loss": 0.3791, "step": 1976}, {"epoch": 2.81, "learning_rate": 2.1366075570576904e-06, "loss": 0.3886, "step": 1977}, {"epoch": 2.81, "learning_rate": 2.1051824680937802e-06, "loss": 0.396, "step": 1978}, {"epoch": 2.81, "learning_rate": 2.073987735091798e-06, "loss": 0.3916, "step": 1979}, {"epoch": 2.81, "learning_rate": 2.0430234314559482e-06, "loss": 0.3562, "step": 1980}, {"epoch": 2.81, "learning_rate": 2.0122896300482365e-06, "loss": 0.405, "step": 1981}, {"epoch": 2.82, "learning_rate": 1.981786403188268e-06, "loss": 0.3885, "step": 1982}, {"epoch": 2.82, "learning_rate": 1.951513822653062e-06, "loss": 0.3764, "step": 1983}, {"epoch": 2.82, "learning_rate": 1.921471959676957e-06, "loss": 0.3719, "step": 1984}, {"epoch": 2.82, "learning_rate": 1.8916608849513406e-06, "loss": 0.3563, "step": 1985}, {"epoch": 2.82, "learning_rate": 1.862080668624544e-06, "loss": 0.3422, "step": 1986}, {"epoch": 2.82, "learning_rate": 1.8327313803016887e-06, "loss": 0.3916, "step": 1987}, {"epoch": 2.82, "learning_rate": 1.8036130890444757e-06, "loss": 0.3583, "step": 1988}, {"epoch": 2.83, "learning_rate": 1.774725863371063e-06, "loss": 0.3191, "step": 1989}, {"epoch": 2.83, "learning_rate": 1.746069771255876e-06, "loss": 0.3762, "step": 1990}, {"epoch": 2.83, "learning_rate": 1.7176448801294764e-06, "loss": 0.3323, "step": 1991}, {"epoch": 2.83, "learning_rate": 1.6894512568783716e-06, "loss": 0.3752, "step": 1992}, {"epoch": 2.83, "learning_rate": 1.661488967844882e-06, "loss": 0.3547, "step": 1993}, {"epoch": 2.83, "learning_rate": 1.6337580788269747e-06, "loss": 0.383, "step": 1994}, {"epoch": 2.83, "learning_rate": 1.606258655078108e-06, "loss": 0.382, "step": 1995}, {"epoch": 2.84, "learning_rate": 1.5789907613070976e-06, "loss": 0.3829, "step": 1996}, {"epoch": 2.84, "learning_rate": 1.551954461677907e-06, "loss": 0.3801, "step": 1997}, {"epoch": 2.84, "learning_rate": 1.5251498198095793e-06, "loss": 0.3715, "step": 1998}, {"epoch": 2.84, "learning_rate": 1.4985768987760162e-06, "loss": 0.4136, "step": 1999}, {"epoch": 2.84, "learning_rate": 1.472235761105878e-06, "loss": 0.3685, "step": 2000}], "logging_steps": 1.0, "max_steps": 2112, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 1000, "total_flos": 2.9189386271425823e+18, "train_batch_size": 16, "trial_name": null, "trial_params": null}