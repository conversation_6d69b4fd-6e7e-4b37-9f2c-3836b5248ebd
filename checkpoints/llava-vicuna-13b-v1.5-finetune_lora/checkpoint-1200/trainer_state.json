{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.8846153846153846, "eval_steps": 500, "global_step": 1200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0, "learning_rate": 5.263157894736842e-06, "loss": 2.3601, "step": 1}, {"epoch": 0.0, "learning_rate": 1.0526315789473684e-05, "loss": 2.3794, "step": 2}, {"epoch": 0.01, "learning_rate": 1.5789473684210526e-05, "loss": 2.3731, "step": 3}, {"epoch": 0.01, "learning_rate": 2.105263157894737e-05, "loss": 2.4972, "step": 4}, {"epoch": 0.01, "learning_rate": 2.6315789473684212e-05, "loss": 2.351, "step": 5}, {"epoch": 0.01, "learning_rate": 3.157894736842105e-05, "loss": 2.4104, "step": 6}, {"epoch": 0.02, "learning_rate": 3.6842105263157895e-05, "loss": 2.4539, "step": 7}, {"epoch": 0.02, "learning_rate": 4.210526315789474e-05, "loss": 2.3098, "step": 8}, {"epoch": 0.02, "learning_rate": 4.736842105263158e-05, "loss": 2.2293, "step": 9}, {"epoch": 0.02, "learning_rate": 5.2631578947368424e-05, "loss": 2.1323, "step": 10}, {"epoch": 0.03, "learning_rate": 5.789473684210527e-05, "loss": 2.0639, "step": 11}, {"epoch": 0.03, "learning_rate": 6.31578947368421e-05, "loss": 2.0178, "step": 12}, {"epoch": 0.03, "learning_rate": 6.842105263157895e-05, "loss": 1.8966, "step": 13}, {"epoch": 0.03, "learning_rate": 7.368421052631579e-05, "loss": 1.7366, "step": 14}, {"epoch": 0.04, "learning_rate": 7.894736842105263e-05, "loss": 1.6628, "step": 15}, {"epoch": 0.04, "learning_rate": 8.421052631578948e-05, "loss": 1.5299, "step": 16}, {"epoch": 0.04, "learning_rate": 8.947368421052632e-05, "loss": 1.4947, "step": 17}, {"epoch": 0.04, "learning_rate": 9.473684210526316e-05, "loss": 1.4903, "step": 18}, {"epoch": 0.05, "learning_rate": 0.0001, "loss": 1.4535, "step": 19}, {"epoch": 0.05, "learning_rate": 0.00010526315789473685, "loss": 1.393, "step": 20}, {"epoch": 0.05, "learning_rate": 0.0001105263157894737, "loss": 1.3825, "step": 21}, {"epoch": 0.05, "learning_rate": 0.00011578947368421053, "loss": 1.3603, "step": 22}, {"epoch": 0.06, "learning_rate": 0.00012105263157894738, "loss": 1.3011, "step": 23}, {"epoch": 0.06, "learning_rate": 0.0001263157894736842, "loss": 1.3168, "step": 24}, {"epoch": 0.06, "learning_rate": 0.00013157894736842108, "loss": 1.3272, "step": 25}, {"epoch": 0.06, "learning_rate": 0.0001368421052631579, "loss": 1.3059, "step": 26}, {"epoch": 0.06, "learning_rate": 0.00014210526315789474, "loss": 1.221, "step": 27}, {"epoch": 0.07, "learning_rate": 0.00014736842105263158, "loss": 1.2329, "step": 28}, {"epoch": 0.07, "learning_rate": 0.00015263157894736845, "loss": 1.1462, "step": 29}, {"epoch": 0.07, "learning_rate": 0.00015789473684210527, "loss": 1.1311, "step": 30}, {"epoch": 0.07, "learning_rate": 0.0001631578947368421, "loss": 1.1482, "step": 31}, {"epoch": 0.08, "learning_rate": 0.00016842105263157895, "loss": 1.0966, "step": 32}, {"epoch": 0.08, "learning_rate": 0.0001736842105263158, "loss": 1.1087, "step": 33}, {"epoch": 0.08, "learning_rate": 0.00017894736842105264, "loss": 1.074, "step": 34}, {"epoch": 0.08, "learning_rate": 0.00018421052631578948, "loss": 1.0868, "step": 35}, {"epoch": 0.09, "learning_rate": 0.00018947368421052632, "loss": 0.9677, "step": 36}, {"epoch": 0.09, "learning_rate": 0.00019473684210526317, "loss": 0.9858, "step": 37}, {"epoch": 0.09, "learning_rate": 0.0002, "loss": 1.0689, "step": 38}, {"epoch": 0.09, "learning_rate": 0.0001999996629465591, "loss": 0.9834, "step": 39}, {"epoch": 0.1, "learning_rate": 0.00019999865178850845, "loss": 0.9666, "step": 40}, {"epoch": 0.1, "learning_rate": 0.00019999696653266437, "loss": 0.9427, "step": 41}, {"epoch": 0.1, "learning_rate": 0.00019999460719038732, "loss": 0.9224, "step": 42}, {"epoch": 0.1, "learning_rate": 0.0001999915737775817, "loss": 0.9571, "step": 43}, {"epoch": 0.11, "learning_rate": 0.00019998786631469603, "loss": 0.9875, "step": 44}, {"epoch": 0.11, "learning_rate": 0.0001999834848267225, "loss": 0.8777, "step": 45}, {"epoch": 0.11, "learning_rate": 0.0001999784293431971, "loss": 0.8734, "step": 46}, {"epoch": 0.11, "learning_rate": 0.00019997269989819916, "loss": 0.8826, "step": 47}, {"epoch": 0.12, "learning_rate": 0.00019996629653035126, "loss": 0.9106, "step": 48}, {"epoch": 0.12, "learning_rate": 0.00019995921928281894, "loss": 0.8671, "step": 49}, {"epoch": 0.12, "learning_rate": 0.0001999514682033104, "loss": 0.8983, "step": 50}, {"epoch": 0.12, "learning_rate": 0.00019994304334407622, "loss": 0.8398, "step": 51}, {"epoch": 0.12, "learning_rate": 0.000199933944761909, "loss": 0.8633, "step": 52}, {"epoch": 0.13, "learning_rate": 0.00019992417251814282, "loss": 0.841, "step": 53}, {"epoch": 0.13, "learning_rate": 0.0001999137266786531, "loss": 0.8118, "step": 54}, {"epoch": 0.13, "learning_rate": 0.00019990260731385595, "loss": 0.8508, "step": 55}, {"epoch": 0.13, "learning_rate": 0.00019989081449870778, "loss": 0.8603, "step": 56}, {"epoch": 0.14, "learning_rate": 0.00019987834831270476, "loss": 0.7928, "step": 57}, {"epoch": 0.14, "learning_rate": 0.00019986520883988232, "loss": 0.7712, "step": 58}, {"epoch": 0.14, "learning_rate": 0.00019985139616881452, "loss": 0.829, "step": 59}, {"epoch": 0.14, "learning_rate": 0.00019983691039261357, "loss": 0.85, "step": 60}, {"epoch": 0.15, "learning_rate": 0.00019982175160892908, "loss": 0.8154, "step": 61}, {"epoch": 0.15, "learning_rate": 0.0001998059199199474, "loss": 0.7955, "step": 62}, {"epoch": 0.15, "learning_rate": 0.0001997894154323911, "loss": 0.7638, "step": 63}, {"epoch": 0.15, "learning_rate": 0.00019977223825751801, "loss": 0.8221, "step": 64}, {"epoch": 0.16, "learning_rate": 0.0001997543885111207, "loss": 0.7743, "step": 65}, {"epoch": 0.16, "learning_rate": 0.0001997358663135255, "loss": 0.7946, "step": 66}, {"epoch": 0.16, "learning_rate": 0.00019971667178959185, "loss": 0.7576, "step": 67}, {"epoch": 0.16, "learning_rate": 0.00019969680506871137, "loss": 0.7442, "step": 68}, {"epoch": 0.17, "learning_rate": 0.0001996762662848069, "loss": 0.7467, "step": 69}, {"epoch": 0.17, "learning_rate": 0.0001996550555763319, "loss": 0.8406, "step": 70}, {"epoch": 0.17, "learning_rate": 0.00019963317308626914, "loss": 0.7749, "step": 71}, {"epoch": 0.17, "learning_rate": 0.00019961061896213008, "loss": 0.7, "step": 72}, {"epoch": 0.18, "learning_rate": 0.0001995873933559535, "loss": 0.7381, "step": 73}, {"epoch": 0.18, "learning_rate": 0.00019956349642430494, "loss": 0.7584, "step": 74}, {"epoch": 0.18, "learning_rate": 0.00019953892832827517, "loss": 0.7473, "step": 75}, {"epoch": 0.18, "learning_rate": 0.00019951368923347944, "loss": 0.801, "step": 76}, {"epoch": 0.19, "learning_rate": 0.00019948777931005625, "loss": 0.73, "step": 77}, {"epoch": 0.19, "learning_rate": 0.00019946119873266613, "loss": 0.7128, "step": 78}, {"epoch": 0.19, "learning_rate": 0.00019943394768049064, "loss": 0.7207, "step": 79}, {"epoch": 0.19, "learning_rate": 0.00019940602633723096, "loss": 0.7371, "step": 80}, {"epoch": 0.19, "learning_rate": 0.00019937743489110678, "loss": 0.7301, "step": 81}, {"epoch": 0.2, "learning_rate": 0.00019934817353485501, "loss": 0.7081, "step": 82}, {"epoch": 0.2, "learning_rate": 0.0001993182424657285, "loss": 0.7354, "step": 83}, {"epoch": 0.2, "learning_rate": 0.00019928764188549463, "loss": 0.7922, "step": 84}, {"epoch": 0.2, "learning_rate": 0.000199256372000434, "loss": 0.701, "step": 85}, {"epoch": 0.21, "learning_rate": 0.00019922443302133904, "loss": 0.7067, "step": 86}, {"epoch": 0.21, "learning_rate": 0.00019919182516351267, "loss": 0.736, "step": 87}, {"epoch": 0.21, "learning_rate": 0.00019915854864676664, "loss": 0.7161, "step": 88}, {"epoch": 0.21, "learning_rate": 0.00019912460369542027, "loss": 0.7383, "step": 89}, {"epoch": 0.22, "learning_rate": 0.00019908999053829882, "loss": 0.6926, "step": 90}, {"epoch": 0.22, "learning_rate": 0.00019905470940873195, "loss": 0.7282, "step": 91}, {"epoch": 0.22, "learning_rate": 0.00019901876054455217, "loss": 0.6861, "step": 92}, {"epoch": 0.22, "learning_rate": 0.0001989821441880933, "loss": 0.6626, "step": 93}, {"epoch": 0.23, "learning_rate": 0.00019894486058618865, "loss": 0.7302, "step": 94}, {"epoch": 0.23, "learning_rate": 0.00019890690999016956, "loss": 0.765, "step": 95}, {"epoch": 0.23, "learning_rate": 0.00019886829265586368, "loss": 0.729, "step": 96}, {"epoch": 0.23, "learning_rate": 0.00019882900884359304, "loss": 0.7716, "step": 97}, {"epoch": 0.24, "learning_rate": 0.00019878905881817252, "loss": 0.7063, "step": 98}, {"epoch": 0.24, "learning_rate": 0.00019874844284890805, "loss": 0.7282, "step": 99}, {"epoch": 0.24, "learning_rate": 0.00019870716120959462, "loss": 0.7746, "step": 100}, {"epoch": 0.24, "learning_rate": 0.00019866521417851463, "loss": 0.722, "step": 101}, {"epoch": 0.25, "learning_rate": 0.0001986226020384359, "loss": 0.7508, "step": 102}, {"epoch": 0.25, "learning_rate": 0.0001985793250766098, "loss": 0.7282, "step": 103}, {"epoch": 0.25, "learning_rate": 0.00019853538358476932, "loss": 0.754, "step": 104}, {"epoch": 0.25, "learning_rate": 0.00019849077785912705, "loss": 0.6912, "step": 105}, {"epoch": 0.25, "learning_rate": 0.00019844550820037325, "loss": 0.7598, "step": 106}, {"epoch": 0.26, "learning_rate": 0.00019839957491367383, "loss": 0.7161, "step": 107}, {"epoch": 0.26, "learning_rate": 0.00019835297830866826, "loss": 0.7569, "step": 108}, {"epoch": 0.26, "learning_rate": 0.00019830571869946742, "loss": 0.7001, "step": 109}, {"epoch": 0.26, "learning_rate": 0.00019825779640465156, "loss": 0.6902, "step": 110}, {"epoch": 0.27, "learning_rate": 0.00019820921174726826, "loss": 0.6979, "step": 111}, {"epoch": 0.27, "learning_rate": 0.00019815996505482996, "loss": 0.6428, "step": 112}, {"epoch": 0.27, "learning_rate": 0.00019811005665931205, "loss": 0.6981, "step": 113}, {"epoch": 0.27, "learning_rate": 0.00019805948689715041, "loss": 0.6969, "step": 114}, {"epoch": 0.28, "learning_rate": 0.00019800825610923934, "loss": 0.7052, "step": 115}, {"epoch": 0.28, "learning_rate": 0.00019795636464092908, "loss": 0.6844, "step": 116}, {"epoch": 0.28, "learning_rate": 0.0001979038128420236, "loss": 0.7008, "step": 117}, {"epoch": 0.28, "learning_rate": 0.00019785060106677818, "loss": 0.6586, "step": 118}, {"epoch": 0.29, "learning_rate": 0.00019779672967389704, "loss": 0.6986, "step": 119}, {"epoch": 0.29, "learning_rate": 0.000197742199026531, "loss": 0.6514, "step": 120}, {"epoch": 0.29, "learning_rate": 0.00019768700949227482, "loss": 0.672, "step": 121}, {"epoch": 0.29, "learning_rate": 0.00019763116144316505, "loss": 0.691, "step": 122}, {"epoch": 0.3, "learning_rate": 0.0001975746552556772, "loss": 0.6799, "step": 123}, {"epoch": 0.3, "learning_rate": 0.00019751749131072333, "loss": 0.6411, "step": 124}, {"epoch": 0.3, "learning_rate": 0.00019745966999364954, "loss": 0.7052, "step": 125}, {"epoch": 0.3, "learning_rate": 0.00019740119169423337, "loss": 0.7414, "step": 126}, {"epoch": 0.31, "learning_rate": 0.00019734205680668098, "loss": 0.7074, "step": 127}, {"epoch": 0.31, "learning_rate": 0.00019728226572962473, "loss": 0.7069, "step": 128}, {"epoch": 0.31, "learning_rate": 0.00019722181886612042, "loss": 0.6821, "step": 129}, {"epoch": 0.31, "learning_rate": 0.00019716071662364453, "loss": 0.7043, "step": 130}, {"epoch": 0.31, "learning_rate": 0.0001970989594140914, "loss": 0.7513, "step": 131}, {"epoch": 0.32, "learning_rate": 0.0001970365476537707, "loss": 0.6533, "step": 132}, {"epoch": 0.32, "learning_rate": 0.0001969734817634044, "loss": 0.7055, "step": 133}, {"epoch": 0.32, "learning_rate": 0.00019690976216812396, "loss": 0.6912, "step": 134}, {"epoch": 0.32, "learning_rate": 0.0001968453892974676, "loss": 0.637, "step": 135}, {"epoch": 0.33, "learning_rate": 0.00019678036358537724, "loss": 0.6661, "step": 136}, {"epoch": 0.33, "learning_rate": 0.00019671468547019573, "loss": 0.7009, "step": 137}, {"epoch": 0.33, "learning_rate": 0.0001966483553946637, "loss": 0.6482, "step": 138}, {"epoch": 0.33, "learning_rate": 0.00019658137380591678, "loss": 0.6663, "step": 139}, {"epoch": 0.34, "learning_rate": 0.00019651374115548252, "loss": 0.7226, "step": 140}, {"epoch": 0.34, "learning_rate": 0.0001964454578992772, "loss": 0.8458, "step": 141}, {"epoch": 0.34, "learning_rate": 0.000196376524497603, "loss": 0.6698, "step": 142}, {"epoch": 0.34, "learning_rate": 0.00019630694141514464, "loss": 0.6519, "step": 143}, {"epoch": 0.35, "learning_rate": 0.00019623670912096656, "loss": 0.6564, "step": 144}, {"epoch": 0.35, "learning_rate": 0.00019616582808850946, "loss": 0.6432, "step": 145}, {"epoch": 0.35, "learning_rate": 0.00019609429879558724, "loss": 0.6811, "step": 146}, {"epoch": 0.35, "learning_rate": 0.0001960221217243838, "loss": 0.6578, "step": 147}, {"epoch": 0.36, "learning_rate": 0.00019594929736144976, "loss": 0.7398, "step": 148}, {"epoch": 0.36, "learning_rate": 0.00019587582619769913, "loss": 0.7004, "step": 149}, {"epoch": 0.36, "learning_rate": 0.00019580170872840607, "loss": 0.6923, "step": 150}, {"epoch": 0.36, "learning_rate": 0.00019572694545320164, "loss": 0.6435, "step": 151}, {"epoch": 0.37, "learning_rate": 0.00019565153687607008, "loss": 0.6297, "step": 152}, {"epoch": 0.37, "learning_rate": 0.0001955754835053459, "loss": 0.7253, "step": 153}, {"epoch": 0.37, "learning_rate": 0.00019549878585371007, "loss": 0.6857, "step": 154}, {"epoch": 0.37, "learning_rate": 0.00019542144443818673, "loss": 0.6384, "step": 155}, {"epoch": 0.38, "learning_rate": 0.0001953434597801397, "loss": 0.6735, "step": 156}, {"epoch": 0.38, "learning_rate": 0.00019526483240526893, "loss": 0.6528, "step": 157}, {"epoch": 0.38, "learning_rate": 0.00019518556284360696, "loss": 0.6516, "step": 158}, {"epoch": 0.38, "learning_rate": 0.00019510565162951537, "loss": 0.6488, "step": 159}, {"epoch": 0.38, "learning_rate": 0.00019502509930168112, "loss": 0.6339, "step": 160}, {"epoch": 0.39, "learning_rate": 0.00019494390640311301, "loss": 0.6447, "step": 161}, {"epoch": 0.39, "learning_rate": 0.000194862073481138, "loss": 0.6976, "step": 162}, {"epoch": 0.39, "learning_rate": 0.0001947796010873974, "loss": 0.6384, "step": 163}, {"epoch": 0.39, "learning_rate": 0.0001946964897778433, "loss": 0.5805, "step": 164}, {"epoch": 0.4, "learning_rate": 0.00019461274011273476, "loss": 0.6157, "step": 165}, {"epoch": 0.4, "learning_rate": 0.00019452835265663403, "loss": 0.6702, "step": 166}, {"epoch": 0.4, "learning_rate": 0.0001944433279784028, "loss": 0.6747, "step": 167}, {"epoch": 0.4, "learning_rate": 0.0001943576666511982, "loss": 0.6381, "step": 168}, {"epoch": 0.41, "learning_rate": 0.00019427136925246922, "loss": 0.6793, "step": 169}, {"epoch": 0.41, "learning_rate": 0.00019418443636395248, "loss": 0.6779, "step": 170}, {"epoch": 0.41, "learning_rate": 0.00019409686857166863, "loss": 0.6705, "step": 171}, {"epoch": 0.41, "learning_rate": 0.00019400866646591814, "loss": 0.736, "step": 172}, {"epoch": 0.42, "learning_rate": 0.0001939198306412775, "loss": 0.6656, "step": 173}, {"epoch": 0.42, "learning_rate": 0.00019383036169659513, "loss": 0.6628, "step": 174}, {"epoch": 0.42, "learning_rate": 0.00019374026023498728, "loss": 0.6332, "step": 175}, {"epoch": 0.42, "learning_rate": 0.00019364952686383417, "loss": 0.6607, "step": 176}, {"epoch": 0.43, "learning_rate": 0.00019355816219477568, "loss": 0.616, "step": 177}, {"epoch": 0.43, "learning_rate": 0.0001934661668437073, "loss": 0.6634, "step": 178}, {"epoch": 0.43, "learning_rate": 0.0001933735414307761, "loss": 0.6237, "step": 179}, {"epoch": 0.43, "learning_rate": 0.00019328028658037626, "loss": 0.6267, "step": 180}, {"epoch": 0.44, "learning_rate": 0.00019318640292114524, "loss": 0.6642, "step": 181}, {"epoch": 0.44, "learning_rate": 0.0001930918910859592, "loss": 0.662, "step": 182}, {"epoch": 0.44, "learning_rate": 0.0001929967517119289, "loss": 0.6156, "step": 183}, {"epoch": 0.44, "learning_rate": 0.00019290098544039546, "loss": 0.6892, "step": 184}, {"epoch": 0.44, "learning_rate": 0.00019280459291692588, "loss": 0.6224, "step": 185}, {"epoch": 0.45, "learning_rate": 0.00019270757479130878, "loss": 0.6241, "step": 186}, {"epoch": 0.45, "learning_rate": 0.0001926099317175501, "loss": 0.604, "step": 187}, {"epoch": 0.45, "learning_rate": 0.0001925116643538684, "loss": 0.6243, "step": 188}, {"epoch": 0.45, "learning_rate": 0.00019241277336269082, "loss": 0.6448, "step": 189}, {"epoch": 0.46, "learning_rate": 0.00019231325941064832, "loss": 0.6651, "step": 190}, {"epoch": 0.46, "learning_rate": 0.0001922131231685713, "loss": 0.6158, "step": 191}, {"epoch": 0.46, "learning_rate": 0.000192112365311485, "loss": 0.6236, "step": 192}, {"epoch": 0.46, "learning_rate": 0.0001920109865186052, "loss": 0.6389, "step": 193}, {"epoch": 0.47, "learning_rate": 0.0001919089874733332, "loss": 0.6291, "step": 194}, {"epoch": 0.47, "learning_rate": 0.00019180636886325164, "loss": 0.6564, "step": 195}, {"epoch": 0.47, "learning_rate": 0.00019170313138011964, "loss": 0.6772, "step": 196}, {"epoch": 0.47, "learning_rate": 0.00019159927571986814, "loss": 0.6291, "step": 197}, {"epoch": 0.48, "learning_rate": 0.00019149480258259533, "loss": 0.8499, "step": 198}, {"epoch": 0.48, "learning_rate": 0.00019138971267256179, "loss": 0.6309, "step": 199}, {"epoch": 0.48, "learning_rate": 0.00019128400669818585, "loss": 0.5963, "step": 200}, {"epoch": 0.48, "learning_rate": 0.00019117768537203872, "loss": 0.6327, "step": 201}, {"epoch": 0.49, "learning_rate": 0.00019107074941083983, "loss": 0.6251, "step": 202}, {"epoch": 0.49, "learning_rate": 0.00019096319953545185, "loss": 0.6444, "step": 203}, {"epoch": 0.49, "learning_rate": 0.00019085503647087585, "loss": 0.625, "step": 204}, {"epoch": 0.49, "learning_rate": 0.00019074626094624654, "loss": 0.7266, "step": 205}, {"epoch": 0.5, "learning_rate": 0.0001906368736948272, "loss": 0.849, "step": 206}, {"epoch": 0.5, "learning_rate": 0.00019052687545400478, "loss": 0.6445, "step": 207}, {"epoch": 0.5, "learning_rate": 0.00019041626696528503, "loss": 0.6529, "step": 208}, {"epoch": 0.5, "learning_rate": 0.00019030504897428737, "loss": 0.5926, "step": 209}, {"epoch": 0.5, "learning_rate": 0.00019019322223073995, "loss": 0.6267, "step": 210}, {"epoch": 0.51, "learning_rate": 0.00019008078748847457, "loss": 0.6003, "step": 211}, {"epoch": 0.51, "learning_rate": 0.00018996774550542148, "loss": 0.6566, "step": 212}, {"epoch": 0.51, "learning_rate": 0.00018985409704360456, "loss": 0.6589, "step": 213}, {"epoch": 0.51, "learning_rate": 0.00018973984286913584, "loss": 0.6236, "step": 214}, {"epoch": 0.52, "learning_rate": 0.0001896249837522106, "loss": 0.5786, "step": 215}, {"epoch": 0.52, "learning_rate": 0.00018950952046710207, "loss": 0.6331, "step": 216}, {"epoch": 0.52, "learning_rate": 0.0001893934537921562, "loss": 0.6194, "step": 217}, {"epoch": 0.52, "learning_rate": 0.0001892767845097864, "loss": 0.6423, "step": 218}, {"epoch": 0.53, "learning_rate": 0.00018915951340646832, "loss": 0.6159, "step": 219}, {"epoch": 0.53, "learning_rate": 0.00018904164127273458, "loss": 0.6211, "step": 220}, {"epoch": 0.53, "learning_rate": 0.00018892316890316936, "loss": 0.6812, "step": 221}, {"epoch": 0.53, "learning_rate": 0.00018880409709640298, "loss": 0.6746, "step": 222}, {"epoch": 0.54, "learning_rate": 0.00018868442665510678, "loss": 0.6726, "step": 223}, {"epoch": 0.54, "learning_rate": 0.00018856415838598736, "loss": 0.6365, "step": 224}, {"epoch": 0.54, "learning_rate": 0.00018844329309978145, "loss": 0.6251, "step": 225}, {"epoch": 0.54, "learning_rate": 0.00018832183161125024, "loss": 0.6039, "step": 226}, {"epoch": 0.55, "learning_rate": 0.000188199774739174, "loss": 0.582, "step": 227}, {"epoch": 0.55, "learning_rate": 0.00018807712330634642, "loss": 0.6175, "step": 228}, {"epoch": 0.55, "learning_rate": 0.00018795387813956937, "loss": 0.6509, "step": 229}, {"epoch": 0.55, "learning_rate": 0.00018783004006964698, "loss": 0.6491, "step": 230}, {"epoch": 0.56, "learning_rate": 0.00018770560993138012, "loss": 0.6382, "step": 231}, {"epoch": 0.56, "learning_rate": 0.000187580588563561, "loss": 0.6335, "step": 232}, {"epoch": 0.56, "learning_rate": 0.00018745497680896722, "loss": 0.6194, "step": 233}, {"epoch": 0.56, "learning_rate": 0.00018732877551435627, "loss": 0.6474, "step": 234}, {"epoch": 0.56, "learning_rate": 0.00018720198553045977, "loss": 0.6281, "step": 235}, {"epoch": 0.57, "learning_rate": 0.00018707460771197774, "loss": 0.6282, "step": 236}, {"epoch": 0.57, "learning_rate": 0.00018694664291757276, "loss": 0.6057, "step": 237}, {"epoch": 0.57, "learning_rate": 0.0001868180920098644, "loss": 0.6164, "step": 238}, {"epoch": 0.57, "learning_rate": 0.0001866889558554231, "loss": 0.8259, "step": 239}, {"epoch": 0.58, "learning_rate": 0.00018655923532476463, "loss": 0.6116, "step": 240}, {"epoch": 0.58, "learning_rate": 0.00018642893129234395, "loss": 0.6605, "step": 241}, {"epoch": 0.58, "learning_rate": 0.00018629804463654955, "loss": 0.5657, "step": 242}, {"epoch": 0.58, "learning_rate": 0.0001861665762396974, "loss": 0.6318, "step": 243}, {"epoch": 0.59, "learning_rate": 0.00018603452698802498, "loss": 0.5924, "step": 244}, {"epoch": 0.59, "learning_rate": 0.00018590189777168537, "loss": 0.6241, "step": 245}, {"epoch": 0.59, "learning_rate": 0.00018576868948474127, "loss": 0.6331, "step": 246}, {"epoch": 0.59, "learning_rate": 0.0001856349030251589, "loss": 0.6414, "step": 247}, {"epoch": 0.6, "learning_rate": 0.00018550053929480202, "loss": 0.5914, "step": 248}, {"epoch": 0.6, "learning_rate": 0.00018536559919942573, "loss": 0.5968, "step": 249}, {"epoch": 0.6, "learning_rate": 0.00018523008364867055, "loss": 0.6518, "step": 250}, {"epoch": 0.6, "learning_rate": 0.00018509399355605606, "loss": 0.5975, "step": 251}, {"epoch": 0.61, "learning_rate": 0.00018495732983897503, "loss": 0.6, "step": 252}, {"epoch": 0.61, "learning_rate": 0.00018482009341868697, "loss": 0.6532, "step": 253}, {"epoch": 0.61, "learning_rate": 0.00018468228522031195, "loss": 0.6371, "step": 254}, {"epoch": 0.61, "learning_rate": 0.0001845439061728246, "loss": 0.6154, "step": 255}, {"epoch": 0.62, "learning_rate": 0.00018440495720904756, "loss": 0.6278, "step": 256}, {"epoch": 0.62, "learning_rate": 0.0001842654392656454, "loss": 0.603, "step": 257}, {"epoch": 0.62, "learning_rate": 0.00018412535328311814, "loss": 0.586, "step": 258}, {"epoch": 0.62, "learning_rate": 0.000183984700205795, "loss": 0.608, "step": 259}, {"epoch": 0.62, "learning_rate": 0.00018384348098182815, "loss": 0.5748, "step": 260}, {"epoch": 0.63, "learning_rate": 0.00018370169656318602, "loss": 0.5885, "step": 261}, {"epoch": 0.63, "learning_rate": 0.00018355934790564718, "loss": 0.6253, "step": 262}, {"epoch": 0.63, "learning_rate": 0.00018341643596879367, "loss": 0.6006, "step": 263}, {"epoch": 0.63, "learning_rate": 0.00018327296171600471, "loss": 0.7325, "step": 264}, {"epoch": 0.64, "learning_rate": 0.00018312892611445017, "loss": 0.6077, "step": 265}, {"epoch": 0.64, "learning_rate": 0.00018298433013508384, "loss": 0.6185, "step": 266}, {"epoch": 0.64, "learning_rate": 0.0001828391747526373, "loss": 0.6024, "step": 267}, {"epoch": 0.64, "learning_rate": 0.0001826934609456129, "loss": 0.6211, "step": 268}, {"epoch": 0.65, "learning_rate": 0.0001825471896962774, "loss": 0.6719, "step": 269}, {"epoch": 0.65, "learning_rate": 0.00018240036199065546, "loss": 0.6054, "step": 270}, {"epoch": 0.65, "learning_rate": 0.00018225297881852264, "loss": 0.5946, "step": 271}, {"epoch": 0.65, "learning_rate": 0.00018210504117339914, "loss": 0.6277, "step": 272}, {"epoch": 0.66, "learning_rate": 0.00018195655005254273, "loss": 0.6139, "step": 273}, {"epoch": 0.66, "learning_rate": 0.00018180750645694236, "loss": 0.6688, "step": 274}, {"epoch": 0.66, "learning_rate": 0.00018165791139131108, "loss": 0.572, "step": 275}, {"epoch": 0.66, "learning_rate": 0.00018150776586407956, "loss": 0.5978, "step": 276}, {"epoch": 0.67, "learning_rate": 0.00018135707088738913, "loss": 0.5662, "step": 277}, {"epoch": 0.67, "learning_rate": 0.00018120582747708502, "loss": 0.5981, "step": 278}, {"epoch": 0.67, "learning_rate": 0.00018105403665270942, "loss": 0.622, "step": 279}, {"epoch": 0.67, "learning_rate": 0.00018090169943749476, "loss": 0.5896, "step": 280}, {"epoch": 0.68, "learning_rate": 0.00018074881685835667, "loss": 0.6195, "step": 281}, {"epoch": 0.68, "learning_rate": 0.00018059538994588716, "loss": 0.6592, "step": 282}, {"epoch": 0.68, "learning_rate": 0.00018044141973434758, "loss": 0.6254, "step": 283}, {"epoch": 0.68, "learning_rate": 0.00018028690726166173, "loss": 0.5942, "step": 284}, {"epoch": 0.69, "learning_rate": 0.00018013185356940885, "loss": 0.6749, "step": 285}, {"epoch": 0.69, "learning_rate": 0.0001799762597028165, "loss": 0.6086, "step": 286}, {"epoch": 0.69, "learning_rate": 0.00017982012671075367, "loss": 0.6122, "step": 287}, {"epoch": 0.69, "learning_rate": 0.0001796634556457236, "loss": 0.5842, "step": 288}, {"epoch": 0.69, "learning_rate": 0.00017950624756385674, "loss": 0.6024, "step": 289}, {"epoch": 0.7, "learning_rate": 0.00017934850352490357, "loss": 0.6145, "step": 290}, {"epoch": 0.7, "learning_rate": 0.00017919022459222752, "loss": 0.6224, "step": 291}, {"epoch": 0.7, "learning_rate": 0.00017903141183279778, "loss": 0.642, "step": 292}, {"epoch": 0.7, "learning_rate": 0.00017887206631718203, "loss": 0.6279, "step": 293}, {"epoch": 0.71, "learning_rate": 0.0001787121891195394, "loss": 0.5691, "step": 294}, {"epoch": 0.71, "learning_rate": 0.0001785517813176131, "loss": 0.6362, "step": 295}, {"epoch": 0.71, "learning_rate": 0.00017839084399272315, "loss": 0.595, "step": 296}, {"epoch": 0.71, "learning_rate": 0.00017822937822975908, "loss": 0.5751, "step": 297}, {"epoch": 0.72, "learning_rate": 0.0001780673851171728, "loss": 0.5987, "step": 298}, {"epoch": 0.72, "learning_rate": 0.000177904865746971, "loss": 0.648, "step": 299}, {"epoch": 0.72, "learning_rate": 0.0001777418212147079, "loss": 0.6032, "step": 300}, {"epoch": 0.72, "learning_rate": 0.00017757825261947795, "loss": 0.6024, "step": 301}, {"epoch": 0.73, "learning_rate": 0.00017741416106390826, "loss": 0.6086, "step": 302}, {"epoch": 0.73, "learning_rate": 0.00017724954765415137, "loss": 0.5682, "step": 303}, {"epoch": 0.73, "learning_rate": 0.00017708441349987753, "loss": 0.5644, "step": 304}, {"epoch": 0.73, "learning_rate": 0.0001769187597142675, "loss": 0.6576, "step": 305}, {"epoch": 0.74, "learning_rate": 0.0001767525874140048, "loss": 0.6283, "step": 306}, {"epoch": 0.74, "learning_rate": 0.00017658589771926838, "loss": 0.6383, "step": 307}, {"epoch": 0.74, "learning_rate": 0.00017641869175372493, "loss": 0.7395, "step": 308}, {"epoch": 0.74, "learning_rate": 0.00017625097064452136, "loss": 0.6187, "step": 309}, {"epoch": 0.75, "learning_rate": 0.0001760827355222772, "loss": 0.6082, "step": 310}, {"epoch": 0.75, "learning_rate": 0.00017591398752107705, "loss": 0.6113, "step": 311}, {"epoch": 0.75, "learning_rate": 0.00017574472777846274, "loss": 0.593, "step": 312}, {"epoch": 0.75, "learning_rate": 0.00017557495743542585, "loss": 0.6006, "step": 313}, {"epoch": 0.75, "learning_rate": 0.00017540467763639994, "loss": 0.6639, "step": 314}, {"epoch": 0.76, "learning_rate": 0.0001752338895292529, "loss": 0.6242, "step": 315}, {"epoch": 0.76, "learning_rate": 0.00017506259426527902, "loss": 0.5859, "step": 316}, {"epoch": 0.76, "learning_rate": 0.00017489079299919157, "loss": 0.5968, "step": 317}, {"epoch": 0.76, "learning_rate": 0.00017471848688911464, "loss": 0.6095, "step": 318}, {"epoch": 0.77, "learning_rate": 0.0001745456770965756, "loss": 0.7362, "step": 319}, {"epoch": 0.77, "learning_rate": 0.00017437236478649716, "loss": 0.6354, "step": 320}, {"epoch": 0.77, "learning_rate": 0.00017419855112718951, "loss": 0.5866, "step": 321}, {"epoch": 0.77, "learning_rate": 0.0001740242372903425, "loss": 0.6317, "step": 322}, {"epoch": 0.78, "learning_rate": 0.00017384942445101772, "loss": 0.5817, "step": 323}, {"epoch": 0.78, "learning_rate": 0.0001736741137876405, "loss": 0.552, "step": 324}, {"epoch": 0.78, "learning_rate": 0.0001734983064819921, "loss": 0.5898, "step": 325}, {"epoch": 0.78, "learning_rate": 0.00017332200371920174, "loss": 0.606, "step": 326}, {"epoch": 0.79, "learning_rate": 0.00017314520668773836, "loss": 0.5737, "step": 327}, {"epoch": 0.79, "learning_rate": 0.000172967916579403, "loss": 0.5998, "step": 328}, {"epoch": 0.79, "learning_rate": 0.00017279013458932046, "loss": 0.6053, "step": 329}, {"epoch": 0.79, "learning_rate": 0.00017261186191593135, "loss": 0.6214, "step": 330}, {"epoch": 0.8, "learning_rate": 0.00017243309976098405, "loss": 0.6721, "step": 331}, {"epoch": 0.8, "learning_rate": 0.00017225384932952656, "loss": 0.5842, "step": 332}, {"epoch": 0.8, "learning_rate": 0.00017207411182989832, "loss": 0.5895, "step": 333}, {"epoch": 0.8, "learning_rate": 0.00017189388847372225, "loss": 0.6209, "step": 334}, {"epoch": 0.81, "learning_rate": 0.00017171318047589637, "loss": 0.587, "step": 335}, {"epoch": 0.81, "learning_rate": 0.00017153198905458573, "loss": 0.6052, "step": 336}, {"epoch": 0.81, "learning_rate": 0.00017135031543121413, "loss": 0.5814, "step": 337}, {"epoch": 0.81, "learning_rate": 0.00017116816083045602, "loss": 0.5686, "step": 338}, {"epoch": 0.81, "learning_rate": 0.0001709855264802281, "loss": 0.6086, "step": 339}, {"epoch": 0.82, "learning_rate": 0.00017080241361168107, "loss": 0.6128, "step": 340}, {"epoch": 0.82, "learning_rate": 0.0001706188234591914, "loss": 0.565, "step": 341}, {"epoch": 0.82, "learning_rate": 0.00017043475726035288, "loss": 0.7477, "step": 342}, {"epoch": 0.82, "learning_rate": 0.00017025021625596853, "loss": 0.5915, "step": 343}, {"epoch": 0.83, "learning_rate": 0.00017006520169004187, "loss": 0.6111, "step": 344}, {"epoch": 0.83, "learning_rate": 0.0001698797148097689, "loss": 0.6097, "step": 345}, {"epoch": 0.83, "learning_rate": 0.00016969375686552937, "loss": 0.6043, "step": 346}, {"epoch": 0.83, "learning_rate": 0.00016950732911087858, "loss": 0.5458, "step": 347}, {"epoch": 0.84, "learning_rate": 0.0001693204328025389, "loss": 0.6296, "step": 348}, {"epoch": 0.84, "learning_rate": 0.0001691330692003912, "loss": 0.5793, "step": 349}, {"epoch": 0.84, "learning_rate": 0.00016894523956746639, "loss": 0.5507, "step": 350}, {"epoch": 0.84, "learning_rate": 0.000168756945169937, "loss": 0.6016, "step": 351}, {"epoch": 0.85, "learning_rate": 0.00016856818727710847, "loss": 0.5631, "step": 352}, {"epoch": 0.85, "learning_rate": 0.0001683789671614107, "loss": 0.6037, "step": 353}, {"epoch": 0.85, "learning_rate": 0.00016818928609838967, "loss": 0.5988, "step": 354}, {"epoch": 0.85, "learning_rate": 0.0001679991453666983, "loss": 0.5907, "step": 355}, {"epoch": 0.86, "learning_rate": 0.0001678085462480885, "loss": 0.5726, "step": 356}, {"epoch": 0.86, "learning_rate": 0.00016761749002740193, "loss": 0.6361, "step": 357}, {"epoch": 0.86, "learning_rate": 0.00016742597799256182, "loss": 0.6128, "step": 358}, {"epoch": 0.86, "learning_rate": 0.0001672340114345639, "loss": 0.657, "step": 359}, {"epoch": 0.87, "learning_rate": 0.00016704159164746796, "loss": 0.6186, "step": 360}, {"epoch": 0.87, "learning_rate": 0.00016684871992838905, "loss": 0.575, "step": 361}, {"epoch": 0.87, "learning_rate": 0.00016665539757748868, "loss": 0.5753, "step": 362}, {"epoch": 0.87, "learning_rate": 0.00016646162589796615, "loss": 0.583, "step": 363}, {"epoch": 0.88, "learning_rate": 0.00016626740619604967, "loss": 0.6023, "step": 364}, {"epoch": 0.88, "learning_rate": 0.0001660727397809876, "loss": 0.6466, "step": 365}, {"epoch": 0.88, "learning_rate": 0.00016587762796503968, "loss": 0.5926, "step": 366}, {"epoch": 0.88, "learning_rate": 0.00016568207206346804, "loss": 0.5702, "step": 367}, {"epoch": 0.88, "learning_rate": 0.00016548607339452853, "loss": 0.6028, "step": 368}, {"epoch": 0.89, "learning_rate": 0.00016528963327946158, "loss": 0.7457, "step": 369}, {"epoch": 0.89, "learning_rate": 0.00016509275304248363, "loss": 0.5938, "step": 370}, {"epoch": 0.89, "learning_rate": 0.00016489543401077784, "loss": 0.6036, "step": 371}, {"epoch": 0.89, "learning_rate": 0.00016469767751448538, "loss": 0.5935, "step": 372}, {"epoch": 0.9, "learning_rate": 0.00016449948488669639, "loss": 0.5768, "step": 373}, {"epoch": 0.9, "learning_rate": 0.00016430085746344108, "loss": 0.5827, "step": 374}, {"epoch": 0.9, "learning_rate": 0.0001641017965836805, "loss": 0.5882, "step": 375}, {"epoch": 0.9, "learning_rate": 0.0001639023035892978, "loss": 0.5742, "step": 376}, {"epoch": 0.91, "learning_rate": 0.00016370237982508897, "loss": 0.5665, "step": 377}, {"epoch": 0.91, "learning_rate": 0.00016350202663875386, "loss": 0.5986, "step": 378}, {"epoch": 0.91, "learning_rate": 0.00016330124538088705, "loss": 0.5976, "step": 379}, {"epoch": 0.91, "learning_rate": 0.00016310003740496886, "loss": 0.624, "step": 380}, {"epoch": 0.92, "learning_rate": 0.0001628984040673561, "loss": 0.5604, "step": 381}, {"epoch": 0.92, "learning_rate": 0.00016269634672727294, "loss": 0.6067, "step": 382}, {"epoch": 0.92, "learning_rate": 0.00016249386674680184, "loss": 0.625, "step": 383}, {"epoch": 0.92, "learning_rate": 0.00016229096549087434, "loss": 0.5753, "step": 384}, {"epoch": 0.93, "learning_rate": 0.00016208764432726165, "loss": 0.585, "step": 385}, {"epoch": 0.93, "learning_rate": 0.0001618839046265658, "loss": 0.549, "step": 386}, {"epoch": 0.93, "learning_rate": 0.0001616797477622101, "loss": 0.5921, "step": 387}, {"epoch": 0.93, "learning_rate": 0.0001614751751104301, "loss": 0.5852, "step": 388}, {"epoch": 0.94, "learning_rate": 0.00016127018805026403, "loss": 0.584, "step": 389}, {"epoch": 0.94, "learning_rate": 0.00016106478796354382, "loss": 0.5914, "step": 390}, {"epoch": 0.94, "learning_rate": 0.00016085897623488557, "loss": 0.7402, "step": 391}, {"epoch": 0.94, "learning_rate": 0.00016065275425168032, "loss": 0.5806, "step": 392}, {"epoch": 0.94, "learning_rate": 0.00016044612340408466, "loss": 0.6149, "step": 393}, {"epoch": 0.95, "learning_rate": 0.00016023908508501128, "loss": 0.6675, "step": 394}, {"epoch": 0.95, "learning_rate": 0.00016003164069011984, "loss": 0.5708, "step": 395}, {"epoch": 0.95, "learning_rate": 0.00015982379161780724, "loss": 0.5286, "step": 396}, {"epoch": 0.95, "learning_rate": 0.00015961553926919836, "loss": 0.6597, "step": 397}, {"epoch": 0.96, "learning_rate": 0.00015940688504813662, "loss": 0.6326, "step": 398}, {"epoch": 0.96, "learning_rate": 0.00015919783036117451, "loss": 0.5579, "step": 399}, {"epoch": 0.96, "learning_rate": 0.00015898837661756406, "loss": 0.5498, "step": 400}, {"epoch": 0.96, "learning_rate": 0.00015877852522924732, "loss": 0.5653, "step": 401}, {"epoch": 0.97, "learning_rate": 0.00015856827761084698, "loss": 0.6716, "step": 402}, {"epoch": 0.97, "learning_rate": 0.00015835763517965673, "loss": 0.5683, "step": 403}, {"epoch": 0.97, "learning_rate": 0.00015814659935563163, "loss": 0.5865, "step": 404}, {"epoch": 0.97, "learning_rate": 0.00015793517156137876, "loss": 0.5702, "step": 405}, {"epoch": 0.98, "learning_rate": 0.00015772335322214738, "loss": 0.5685, "step": 406}, {"epoch": 0.98, "learning_rate": 0.00015751114576581952, "loss": 0.5978, "step": 407}, {"epoch": 0.98, "learning_rate": 0.00015729855062290022, "loss": 0.5814, "step": 408}, {"epoch": 0.98, "learning_rate": 0.000157085569226508, "loss": 0.5971, "step": 409}, {"epoch": 0.99, "learning_rate": 0.0001568722030123651, "loss": 0.5362, "step": 410}, {"epoch": 0.99, "learning_rate": 0.00015665845341878782, "loss": 0.5512, "step": 411}, {"epoch": 0.99, "learning_rate": 0.00015644432188667695, "loss": 0.5579, "step": 412}, {"epoch": 0.99, "learning_rate": 0.0001562298098595078, "loss": 0.6071, "step": 413}, {"epoch": 1.0, "learning_rate": 0.00015601491878332077, "loss": 0.5524, "step": 414}, {"epoch": 1.0, "learning_rate": 0.0001557996501067114, "loss": 0.5751, "step": 415}, {"epoch": 1.0, "learning_rate": 0.00015558400528082057, "loss": 0.5283, "step": 416}, {"epoch": 1.0, "learning_rate": 0.000155367985759325, "loss": 0.5527, "step": 417}, {"epoch": 1.0, "learning_rate": 0.00015515159299842707, "loss": 0.5643, "step": 418}, {"epoch": 1.01, "learning_rate": 0.0001549348284568453, "loss": 0.5512, "step": 419}, {"epoch": 1.01, "learning_rate": 0.0001547176935958044, "loss": 0.568, "step": 420}, {"epoch": 1.01, "learning_rate": 0.0001545001898790254, "loss": 0.6586, "step": 421}, {"epoch": 1.01, "learning_rate": 0.00015428231877271582, "loss": 0.5899, "step": 422}, {"epoch": 1.02, "learning_rate": 0.00015406408174555976, "loss": 0.5851, "step": 423}, {"epoch": 1.02, "learning_rate": 0.00015384548026870805, "loss": 0.5189, "step": 424}, {"epoch": 1.02, "learning_rate": 0.00015362651581576832, "loss": 0.5658, "step": 425}, {"epoch": 1.02, "learning_rate": 0.00015340718986279502, "loss": 0.572, "step": 426}, {"epoch": 1.03, "learning_rate": 0.00015318750388827943, "loss": 0.5627, "step": 427}, {"epoch": 1.03, "learning_rate": 0.00015296745937313987, "loss": 0.55, "step": 428}, {"epoch": 1.03, "learning_rate": 0.00015274705780071152, "loss": 0.5728, "step": 429}, {"epoch": 1.03, "learning_rate": 0.00015252630065673662, "loss": 0.5647, "step": 430}, {"epoch": 1.04, "learning_rate": 0.00015230518942935421, "loss": 0.5733, "step": 431}, {"epoch": 1.04, "learning_rate": 0.0001520837256090903, "loss": 0.5739, "step": 432}, {"epoch": 1.04, "learning_rate": 0.00015186191068884775, "loss": 0.6221, "step": 433}, {"epoch": 1.04, "learning_rate": 0.0001516397461638962, "loss": 0.5973, "step": 434}, {"epoch": 1.05, "learning_rate": 0.00015141723353186202, "loss": 0.5773, "step": 435}, {"epoch": 1.05, "learning_rate": 0.00015119437429271813, "loss": 0.5695, "step": 436}, {"epoch": 1.05, "learning_rate": 0.00015097116994877404, "loss": 0.5998, "step": 437}, {"epoch": 1.05, "learning_rate": 0.00015074762200466556, "loss": 0.5724, "step": 438}, {"epoch": 1.06, "learning_rate": 0.00015052373196734484, "loss": 0.5441, "step": 439}, {"epoch": 1.06, "learning_rate": 0.00015029950134606992, "loss": 0.52, "step": 440}, {"epoch": 1.06, "learning_rate": 0.00015007493165239492, "loss": 0.5605, "step": 441}, {"epoch": 1.06, "learning_rate": 0.00014985002440015958, "loss": 0.5392, "step": 442}, {"epoch": 1.06, "learning_rate": 0.00014962478110547918, "loss": 0.5445, "step": 443}, {"epoch": 1.07, "learning_rate": 0.00014939920328673422, "loss": 0.5459, "step": 444}, {"epoch": 1.07, "learning_rate": 0.0001491732924645604, "loss": 0.5699, "step": 445}, {"epoch": 1.07, "learning_rate": 0.00014894705016183803, "loss": 0.5858, "step": 446}, {"epoch": 1.07, "learning_rate": 0.00014872047790368204, "loss": 0.5595, "step": 447}, {"epoch": 1.08, "learning_rate": 0.00014849357721743168, "loss": 0.5867, "step": 448}, {"epoch": 1.08, "learning_rate": 0.00014826634963264004, "loss": 0.5823, "step": 449}, {"epoch": 1.08, "learning_rate": 0.00014803879668106394, "loss": 0.5578, "step": 450}, {"epoch": 1.08, "learning_rate": 0.00014781091989665343, "loss": 0.5899, "step": 451}, {"epoch": 1.09, "learning_rate": 0.00014758272081554167, "loss": 0.5535, "step": 452}, {"epoch": 1.09, "learning_rate": 0.0001473542009760343, "loss": 0.5844, "step": 453}, {"epoch": 1.09, "learning_rate": 0.00014712536191859932, "loss": 0.5513, "step": 454}, {"epoch": 1.09, "learning_rate": 0.00014689620518585657, "loss": 0.5793, "step": 455}, {"epoch": 1.1, "learning_rate": 0.00014666673232256738, "loss": 0.5838, "step": 456}, {"epoch": 1.1, "learning_rate": 0.00014643694487562404, "loss": 0.5777, "step": 457}, {"epoch": 1.1, "learning_rate": 0.00014620684439403962, "loss": 0.5552, "step": 458}, {"epoch": 1.1, "learning_rate": 0.00014597643242893725, "loss": 0.5407, "step": 459}, {"epoch": 1.11, "learning_rate": 0.00014574571053353988, "loss": 0.5665, "step": 460}, {"epoch": 1.11, "learning_rate": 0.00014551468026315962, "loss": 0.538, "step": 461}, {"epoch": 1.11, "learning_rate": 0.00014528334317518747, "loss": 0.5862, "step": 462}, {"epoch": 1.11, "learning_rate": 0.0001450517008290827, "loss": 0.5762, "step": 463}, {"epoch": 1.12, "learning_rate": 0.0001448197547863622, "loss": 0.5566, "step": 464}, {"epoch": 1.12, "learning_rate": 0.00014458750661059034, "loss": 0.5661, "step": 465}, {"epoch": 1.12, "learning_rate": 0.00014435495786736794, "loss": 0.6448, "step": 466}, {"epoch": 1.12, "learning_rate": 0.00014412211012432212, "loss": 0.5839, "step": 467}, {"epoch": 1.12, "learning_rate": 0.0001438889649510956, "loss": 0.5553, "step": 468}, {"epoch": 1.13, "learning_rate": 0.0001436555239193359, "loss": 0.5909, "step": 469}, {"epoch": 1.13, "learning_rate": 0.00014342178860268524, "loss": 0.5701, "step": 470}, {"epoch": 1.13, "learning_rate": 0.00014318776057676934, "loss": 0.5727, "step": 471}, {"epoch": 1.13, "learning_rate": 0.00014295344141918733, "loss": 0.6375, "step": 472}, {"epoch": 1.14, "learning_rate": 0.00014271883270950073, "loss": 0.554, "step": 473}, {"epoch": 1.14, "learning_rate": 0.000142483936029223, "loss": 0.5592, "step": 474}, {"epoch": 1.14, "learning_rate": 0.0001422487529618088, "loss": 0.5706, "step": 475}, {"epoch": 1.14, "learning_rate": 0.0001420132850926434, "loss": 0.542, "step": 476}, {"epoch": 1.15, "learning_rate": 0.00014177753400903195, "loss": 0.5689, "step": 477}, {"epoch": 1.15, "learning_rate": 0.00014154150130018866, "loss": 0.5552, "step": 478}, {"epoch": 1.15, "learning_rate": 0.0001413051885572263, "loss": 0.5629, "step": 479}, {"epoch": 1.15, "learning_rate": 0.0001410685973731453, "loss": 0.5245, "step": 480}, {"epoch": 1.16, "learning_rate": 0.00014083172934282318, "loss": 0.5395, "step": 481}, {"epoch": 1.16, "learning_rate": 0.00014059458606300356, "loss": 0.5828, "step": 482}, {"epoch": 1.16, "learning_rate": 0.00014035716913228568, "loss": 0.5313, "step": 483}, {"epoch": 1.16, "learning_rate": 0.00014011948015111333, "loss": 0.5626, "step": 484}, {"epoch": 1.17, "learning_rate": 0.00013988152072176436, "loss": 0.5704, "step": 485}, {"epoch": 1.17, "learning_rate": 0.0001396432924483396, "loss": 0.5364, "step": 486}, {"epoch": 1.17, "learning_rate": 0.00013940479693675228, "loss": 0.5619, "step": 487}, {"epoch": 1.17, "learning_rate": 0.00013916603579471705, "loss": 0.5896, "step": 488}, {"epoch": 1.18, "learning_rate": 0.00013892701063173918, "loss": 0.5306, "step": 489}, {"epoch": 1.18, "learning_rate": 0.00013868772305910377, "loss": 0.5204, "step": 490}, {"epoch": 1.18, "learning_rate": 0.00013844817468986476, "loss": 0.5811, "step": 491}, {"epoch": 1.18, "learning_rate": 0.00013820836713883422, "loss": 0.5498, "step": 492}, {"epoch": 1.19, "learning_rate": 0.0001379683020225714, "loss": 0.5666, "step": 493}, {"epoch": 1.19, "learning_rate": 0.0001377279809593717, "loss": 0.5549, "step": 494}, {"epoch": 1.19, "learning_rate": 0.00013748740556925607, "loss": 0.5501, "step": 495}, {"epoch": 1.19, "learning_rate": 0.00013724657747395957, "loss": 0.5524, "step": 496}, {"epoch": 1.19, "learning_rate": 0.00013700549829692116, "loss": 0.5661, "step": 497}, {"epoch": 1.2, "learning_rate": 0.000136764169663272, "loss": 0.553, "step": 498}, {"epoch": 1.2, "learning_rate": 0.00013652259319982518, "loss": 0.5201, "step": 499}, {"epoch": 1.2, "learning_rate": 0.0001362807705350641, "loss": 0.5978, "step": 500}, {"epoch": 1.2, "learning_rate": 0.00013603870329913212, "loss": 0.5702, "step": 501}, {"epoch": 1.21, "learning_rate": 0.00013579639312382105, "loss": 0.5578, "step": 502}, {"epoch": 1.21, "learning_rate": 0.00013555384164256048, "loss": 0.6488, "step": 503}, {"epoch": 1.21, "learning_rate": 0.00013531105049040666, "loss": 0.5617, "step": 504}, {"epoch": 1.21, "learning_rate": 0.00013506802130403144, "loss": 0.5553, "step": 505}, {"epoch": 1.22, "learning_rate": 0.0001348247557217113, "loss": 0.5314, "step": 506}, {"epoch": 1.22, "learning_rate": 0.00013458125538331626, "loss": 0.5416, "step": 507}, {"epoch": 1.22, "learning_rate": 0.00013433752193029886, "loss": 0.5344, "step": 508}, {"epoch": 1.22, "learning_rate": 0.00013409355700568306, "loss": 0.5263, "step": 509}, {"epoch": 1.23, "learning_rate": 0.00013384936225405326, "loss": 0.5763, "step": 510}, {"epoch": 1.23, "learning_rate": 0.00013360493932154302, "loss": 0.5353, "step": 511}, {"epoch": 1.23, "learning_rate": 0.0001333602898558242, "loss": 0.5442, "step": 512}, {"epoch": 1.23, "learning_rate": 0.00013311541550609565, "loss": 0.5787, "step": 513}, {"epoch": 1.24, "learning_rate": 0.00013287031792307225, "loss": 0.5471, "step": 514}, {"epoch": 1.24, "learning_rate": 0.00013262499875897366, "loss": 0.5441, "step": 515}, {"epoch": 1.24, "learning_rate": 0.0001323794596675132, "loss": 0.5521, "step": 516}, {"epoch": 1.24, "learning_rate": 0.00013213370230388683, "loss": 0.5135, "step": 517}, {"epoch": 1.25, "learning_rate": 0.00013188772832476188, "loss": 0.5896, "step": 518}, {"epoch": 1.25, "learning_rate": 0.00013164153938826582, "loss": 0.5512, "step": 519}, {"epoch": 1.25, "learning_rate": 0.00013139513715397521, "loss": 0.553, "step": 520}, {"epoch": 1.25, "learning_rate": 0.00013114852328290451, "loss": 0.5654, "step": 521}, {"epoch": 1.25, "learning_rate": 0.00013090169943749476, "loss": 0.542, "step": 522}, {"epoch": 1.26, "learning_rate": 0.00013065466728160252, "loss": 0.5402, "step": 523}, {"epoch": 1.26, "learning_rate": 0.0001304074284804885, "loss": 0.5816, "step": 524}, {"epoch": 1.26, "learning_rate": 0.00013015998470080654, "loss": 0.5484, "step": 525}, {"epoch": 1.26, "learning_rate": 0.00012991233761059214, "loss": 0.5357, "step": 526}, {"epoch": 1.27, "learning_rate": 0.00012966448887925138, "loss": 0.5819, "step": 527}, {"epoch": 1.27, "learning_rate": 0.00012941644017754964, "loss": 0.5947, "step": 528}, {"epoch": 1.27, "learning_rate": 0.00012916819317760028, "loss": 0.5477, "step": 529}, {"epoch": 1.27, "learning_rate": 0.0001289197495528534, "loss": 0.5811, "step": 530}, {"epoch": 1.28, "learning_rate": 0.00012867111097808457, "loss": 0.5636, "step": 531}, {"epoch": 1.28, "learning_rate": 0.00012842227912938359, "loss": 0.5382, "step": 532}, {"epoch": 1.28, "learning_rate": 0.00012817325568414297, "loss": 0.5271, "step": 533}, {"epoch": 1.28, "learning_rate": 0.00012792404232104697, "loss": 0.5341, "step": 534}, {"epoch": 1.29, "learning_rate": 0.00012767464072006, "loss": 0.5848, "step": 535}, {"epoch": 1.29, "learning_rate": 0.00012742505256241543, "loss": 0.552, "step": 536}, {"epoch": 1.29, "learning_rate": 0.00012717527953060416, "loss": 0.515, "step": 537}, {"epoch": 1.29, "learning_rate": 0.00012692532330836346, "loss": 0.4876, "step": 538}, {"epoch": 1.3, "learning_rate": 0.00012667518558066537, "loss": 0.555, "step": 539}, {"epoch": 1.3, "learning_rate": 0.00012642486803370552, "loss": 0.5637, "step": 540}, {"epoch": 1.3, "learning_rate": 0.00012617437235489177, "loss": 0.5986, "step": 541}, {"epoch": 1.3, "learning_rate": 0.0001259237002328327, "loss": 0.5467, "step": 542}, {"epoch": 1.31, "learning_rate": 0.00012567285335732633, "loss": 0.5322, "step": 543}, {"epoch": 1.31, "learning_rate": 0.00012542183341934872, "loss": 0.5225, "step": 544}, {"epoch": 1.31, "learning_rate": 0.00012517064211104253, "loss": 0.5503, "step": 545}, {"epoch": 1.31, "learning_rate": 0.00012491928112570567, "loss": 0.5568, "step": 546}, {"epoch": 1.31, "learning_rate": 0.00012466775215777987, "loss": 0.5122, "step": 547}, {"epoch": 1.32, "learning_rate": 0.00012441605690283915, "loss": 0.5571, "step": 548}, {"epoch": 1.32, "learning_rate": 0.00012416419705757857, "loss": 0.5471, "step": 549}, {"epoch": 1.32, "learning_rate": 0.00012391217431980274, "loss": 0.5488, "step": 550}, {"epoch": 1.32, "learning_rate": 0.00012365999038841419, "loss": 0.5242, "step": 551}, {"epoch": 1.33, "learning_rate": 0.0001234076469634022, "loss": 0.5364, "step": 552}, {"epoch": 1.33, "learning_rate": 0.00012315514574583113, "loss": 0.5348, "step": 553}, {"epoch": 1.33, "learning_rate": 0.00012290248843782915, "loss": 0.5793, "step": 554}, {"epoch": 1.33, "learning_rate": 0.00012264967674257646, "loss": 0.5986, "step": 555}, {"epoch": 1.34, "learning_rate": 0.00012239671236429414, "loss": 0.5801, "step": 556}, {"epoch": 1.34, "learning_rate": 0.00012214359700823247, "loss": 0.5842, "step": 557}, {"epoch": 1.34, "learning_rate": 0.0001218903323806595, "loss": 0.5615, "step": 558}, {"epoch": 1.34, "learning_rate": 0.00012163692018884947, "loss": 0.5227, "step": 559}, {"epoch": 1.35, "learning_rate": 0.00012138336214107147, "loss": 0.5398, "step": 560}, {"epoch": 1.35, "learning_rate": 0.00012112965994657768, "loss": 0.5567, "step": 561}, {"epoch": 1.35, "learning_rate": 0.00012087581531559207, "loss": 0.5385, "step": 562}, {"epoch": 1.35, "learning_rate": 0.00012062182995929882, "loss": 0.5147, "step": 563}, {"epoch": 1.36, "learning_rate": 0.00012036770558983066, "loss": 0.5458, "step": 564}, {"epoch": 1.36, "learning_rate": 0.00012011344392025741, "loss": 0.4938, "step": 565}, {"epoch": 1.36, "learning_rate": 0.00011985904666457455, "loss": 0.5161, "step": 566}, {"epoch": 1.36, "learning_rate": 0.00011960451553769145, "loss": 0.5089, "step": 567}, {"epoch": 1.37, "learning_rate": 0.00011934985225541998, "loss": 0.5347, "step": 568}, {"epoch": 1.37, "learning_rate": 0.00011909505853446281, "loss": 0.6665, "step": 569}, {"epoch": 1.37, "learning_rate": 0.00011884013609240199, "loss": 0.5709, "step": 570}, {"epoch": 1.37, "learning_rate": 0.0001185850866476872, "loss": 0.5883, "step": 571}, {"epoch": 1.38, "learning_rate": 0.00011832991191962435, "loss": 0.5915, "step": 572}, {"epoch": 1.38, "learning_rate": 0.0001180746136283638, "loss": 0.5294, "step": 573}, {"epoch": 1.38, "learning_rate": 0.00011781919349488895, "loss": 0.5104, "step": 574}, {"epoch": 1.38, "learning_rate": 0.00011756365324100445, "loss": 0.5632, "step": 575}, {"epoch": 1.38, "learning_rate": 0.00011730799458932474, "loss": 0.555, "step": 576}, {"epoch": 1.39, "learning_rate": 0.0001170522192632624, "loss": 0.5403, "step": 577}, {"epoch": 1.39, "learning_rate": 0.00011679632898701649, "loss": 0.5778, "step": 578}, {"epoch": 1.39, "learning_rate": 0.00011654032548556102, "loss": 0.5593, "step": 579}, {"epoch": 1.39, "learning_rate": 0.00011628421048463314, "loss": 0.5031, "step": 580}, {"epoch": 1.4, "learning_rate": 0.00011602798571072175, "loss": 0.538, "step": 581}, {"epoch": 1.4, "learning_rate": 0.00011577165289105565, "loss": 0.5518, "step": 582}, {"epoch": 1.4, "learning_rate": 0.00011551521375359206, "loss": 0.5423, "step": 583}, {"epoch": 1.4, "learning_rate": 0.00011525867002700484, "loss": 0.5722, "step": 584}, {"epoch": 1.41, "learning_rate": 0.00011500202344067286, "loss": 0.536, "step": 585}, {"epoch": 1.41, "learning_rate": 0.00011474527572466847, "loss": 0.5657, "step": 586}, {"epoch": 1.41, "learning_rate": 0.00011448842860974564, "loss": 0.5385, "step": 587}, {"epoch": 1.41, "learning_rate": 0.00011423148382732853, "loss": 0.6045, "step": 588}, {"epoch": 1.42, "learning_rate": 0.00011397444310949954, "loss": 0.5394, "step": 589}, {"epoch": 1.42, "learning_rate": 0.00011371730818898785, "loss": 0.5824, "step": 590}, {"epoch": 1.42, "learning_rate": 0.00011346008079915764, "loss": 0.5885, "step": 591}, {"epoch": 1.42, "learning_rate": 0.0001132027626739965, "loss": 0.5701, "step": 592}, {"epoch": 1.43, "learning_rate": 0.00011294535554810354, "loss": 0.5018, "step": 593}, {"epoch": 1.43, "learning_rate": 0.00011268786115667798, "loss": 0.5265, "step": 594}, {"epoch": 1.43, "learning_rate": 0.0001124302812355072, "loss": 0.5213, "step": 595}, {"epoch": 1.43, "learning_rate": 0.00011217261752095518, "loss": 0.5354, "step": 596}, {"epoch": 1.44, "learning_rate": 0.00011191487174995079, "loss": 0.5632, "step": 597}, {"epoch": 1.44, "learning_rate": 0.00011165704565997593, "loss": 0.5421, "step": 598}, {"epoch": 1.44, "learning_rate": 0.00011139914098905406, "loss": 0.6741, "step": 599}, {"epoch": 1.44, "learning_rate": 0.00011114115947573833, "loss": 0.5066, "step": 600}, {"epoch": 1.44, "learning_rate": 0.00011088310285909986, "loss": 0.5716, "step": 601}, {"epoch": 1.45, "learning_rate": 0.00011062497287871605, "loss": 0.5342, "step": 602}, {"epoch": 1.45, "learning_rate": 0.00011036677127465889, "loss": 0.5593, "step": 603}, {"epoch": 1.45, "learning_rate": 0.00011010849978748314, "loss": 0.5777, "step": 604}, {"epoch": 1.45, "learning_rate": 0.00010985016015821465, "loss": 0.5331, "step": 605}, {"epoch": 1.46, "learning_rate": 0.00010959175412833869, "loss": 0.5734, "step": 606}, {"epoch": 1.46, "learning_rate": 0.00010933328343978804, "loss": 0.5309, "step": 607}, {"epoch": 1.46, "learning_rate": 0.00010907474983493144, "loss": 0.5464, "step": 608}, {"epoch": 1.46, "learning_rate": 0.00010881615505656169, "loss": 0.5359, "step": 609}, {"epoch": 1.47, "learning_rate": 0.00010855750084788398, "loss": 0.5527, "step": 610}, {"epoch": 1.47, "learning_rate": 0.00010829878895250416, "loss": 0.5464, "step": 611}, {"epoch": 1.47, "learning_rate": 0.00010804002111441689, "loss": 0.5475, "step": 612}, {"epoch": 1.47, "learning_rate": 0.00010778119907799398, "loss": 0.5444, "step": 613}, {"epoch": 1.48, "learning_rate": 0.00010752232458797262, "loss": 0.5852, "step": 614}, {"epoch": 1.48, "learning_rate": 0.00010726339938944355, "loss": 0.567, "step": 615}, {"epoch": 1.48, "learning_rate": 0.00010700442522783932, "loss": 0.6864, "step": 616}, {"epoch": 1.48, "learning_rate": 0.0001067454038489226, "loss": 0.5188, "step": 617}, {"epoch": 1.49, "learning_rate": 0.0001064863369987743, "loss": 0.5058, "step": 618}, {"epoch": 1.49, "learning_rate": 0.00010622722642378196, "loss": 0.566, "step": 619}, {"epoch": 1.49, "learning_rate": 0.0001059680738706277, "loss": 0.5482, "step": 620}, {"epoch": 1.49, "learning_rate": 0.00010570888108627681, "loss": 0.548, "step": 621}, {"epoch": 1.5, "learning_rate": 0.00010544964981796563, "loss": 0.5629, "step": 622}, {"epoch": 1.5, "learning_rate": 0.00010519038181318999, "loss": 0.5262, "step": 623}, {"epoch": 1.5, "learning_rate": 0.00010493107881969336, "loss": 0.591, "step": 624}, {"epoch": 1.5, "learning_rate": 0.00010467174258545504, "loss": 0.5621, "step": 625}, {"epoch": 1.5, "learning_rate": 0.00010441237485867846, "loss": 0.5167, "step": 626}, {"epoch": 1.51, "learning_rate": 0.00010415297738777931, "loss": 0.5441, "step": 627}, {"epoch": 1.51, "learning_rate": 0.00010389355192137377, "loss": 0.5274, "step": 628}, {"epoch": 1.51, "learning_rate": 0.0001036341002082668, "loss": 0.5496, "step": 629}, {"epoch": 1.51, "learning_rate": 0.00010337462399744024, "loss": 0.5572, "step": 630}, {"epoch": 1.52, "learning_rate": 0.00010311512503804106, "loss": 0.5629, "step": 631}, {"epoch": 1.52, "learning_rate": 0.00010285560507936961, "loss": 0.5432, "step": 632}, {"epoch": 1.52, "learning_rate": 0.00010259606587086783, "loss": 0.5642, "step": 633}, {"epoch": 1.52, "learning_rate": 0.00010233650916210735, "loss": 0.5358, "step": 634}, {"epoch": 1.53, "learning_rate": 0.00010207693670277782, "loss": 0.5383, "step": 635}, {"epoch": 1.53, "learning_rate": 0.00010181735024267505, "loss": 0.5779, "step": 636}, {"epoch": 1.53, "learning_rate": 0.0001015577515316892, "loss": 0.5124, "step": 637}, {"epoch": 1.53, "learning_rate": 0.0001012981423197931, "loss": 0.5526, "step": 638}, {"epoch": 1.54, "learning_rate": 0.00010103852435703027, "loss": 0.5509, "step": 639}, {"epoch": 1.54, "learning_rate": 0.0001007788993935033, "loss": 0.5303, "step": 640}, {"epoch": 1.54, "learning_rate": 0.0001005192691793619, "loss": 0.573, "step": 641}, {"epoch": 1.54, "learning_rate": 0.0001002596354647912, "loss": 0.4889, "step": 642}, {"epoch": 1.55, "learning_rate": 0.0001, "loss": 0.5216, "step": 643}, {"epoch": 1.55, "learning_rate": 9.974036453520881e-05, "loss": 0.5481, "step": 644}, {"epoch": 1.55, "learning_rate": 9.948073082063814e-05, "loss": 0.5227, "step": 645}, {"epoch": 1.55, "learning_rate": 9.922110060649672e-05, "loss": 0.5152, "step": 646}, {"epoch": 1.56, "learning_rate": 9.896147564296974e-05, "loss": 0.5511, "step": 647}, {"epoch": 1.56, "learning_rate": 9.870185768020693e-05, "loss": 0.5603, "step": 648}, {"epoch": 1.56, "learning_rate": 9.844224846831083e-05, "loss": 0.5474, "step": 649}, {"epoch": 1.56, "learning_rate": 9.818264975732496e-05, "loss": 0.5047, "step": 650}, {"epoch": 1.56, "learning_rate": 9.792306329722219e-05, "loss": 0.547, "step": 651}, {"epoch": 1.57, "learning_rate": 9.766349083789266e-05, "loss": 0.5165, "step": 652}, {"epoch": 1.57, "learning_rate": 9.740393412913219e-05, "loss": 0.6636, "step": 653}, {"epoch": 1.57, "learning_rate": 9.71443949206304e-05, "loss": 0.5357, "step": 654}, {"epoch": 1.57, "learning_rate": 9.688487496195895e-05, "loss": 0.5651, "step": 655}, {"epoch": 1.58, "learning_rate": 9.662537600255978e-05, "loss": 0.5291, "step": 656}, {"epoch": 1.58, "learning_rate": 9.636589979173323e-05, "loss": 0.5371, "step": 657}, {"epoch": 1.58, "learning_rate": 9.610644807862625e-05, "loss": 0.533, "step": 658}, {"epoch": 1.58, "learning_rate": 9.584702261222071e-05, "loss": 0.5996, "step": 659}, {"epoch": 1.59, "learning_rate": 9.558762514132157e-05, "loss": 0.4974, "step": 660}, {"epoch": 1.59, "learning_rate": 9.532825741454498e-05, "loss": 0.5603, "step": 661}, {"epoch": 1.59, "learning_rate": 9.506892118030668e-05, "loss": 0.493, "step": 662}, {"epoch": 1.59, "learning_rate": 9.480961818681004e-05, "loss": 0.5313, "step": 663}, {"epoch": 1.6, "learning_rate": 9.455035018203438e-05, "loss": 0.5348, "step": 664}, {"epoch": 1.6, "learning_rate": 9.42911189137232e-05, "loss": 0.5715, "step": 665}, {"epoch": 1.6, "learning_rate": 9.403192612937231e-05, "loss": 0.5114, "step": 666}, {"epoch": 1.6, "learning_rate": 9.37727735762181e-05, "loss": 0.562, "step": 667}, {"epoch": 1.61, "learning_rate": 9.35136630012257e-05, "loss": 0.5579, "step": 668}, {"epoch": 1.61, "learning_rate": 9.325459615107742e-05, "loss": 0.5559, "step": 669}, {"epoch": 1.61, "learning_rate": 9.299557477216072e-05, "loss": 0.5199, "step": 670}, {"epoch": 1.61, "learning_rate": 9.27366006105565e-05, "loss": 0.519, "step": 671}, {"epoch": 1.62, "learning_rate": 9.247767541202738e-05, "loss": 0.5237, "step": 672}, {"epoch": 1.62, "learning_rate": 9.221880092200601e-05, "loss": 0.5525, "step": 673}, {"epoch": 1.62, "learning_rate": 9.195997888558312e-05, "loss": 0.5481, "step": 674}, {"epoch": 1.62, "learning_rate": 9.170121104749587e-05, "loss": 0.5321, "step": 675}, {"epoch": 1.62, "learning_rate": 9.144249915211605e-05, "loss": 0.553, "step": 676}, {"epoch": 1.63, "learning_rate": 9.118384494343832e-05, "loss": 0.5262, "step": 677}, {"epoch": 1.63, "learning_rate": 9.092525016506858e-05, "loss": 0.5193, "step": 678}, {"epoch": 1.63, "learning_rate": 9.066671656021198e-05, "loss": 0.5065, "step": 679}, {"epoch": 1.63, "learning_rate": 9.040824587166136e-05, "loss": 0.5319, "step": 680}, {"epoch": 1.64, "learning_rate": 9.014983984178536e-05, "loss": 0.5091, "step": 681}, {"epoch": 1.64, "learning_rate": 8.989150021251689e-05, "loss": 0.5528, "step": 682}, {"epoch": 1.64, "learning_rate": 8.963322872534114e-05, "loss": 0.5289, "step": 683}, {"epoch": 1.64, "learning_rate": 8.937502712128398e-05, "loss": 0.5626, "step": 684}, {"epoch": 1.65, "learning_rate": 8.911689714090018e-05, "loss": 0.5014, "step": 685}, {"epoch": 1.65, "learning_rate": 8.885884052426168e-05, "loss": 0.5642, "step": 686}, {"epoch": 1.65, "learning_rate": 8.860085901094595e-05, "loss": 0.5157, "step": 687}, {"epoch": 1.65, "learning_rate": 8.83429543400241e-05, "loss": 0.5501, "step": 688}, {"epoch": 1.66, "learning_rate": 8.808512825004925e-05, "loss": 0.5215, "step": 689}, {"epoch": 1.66, "learning_rate": 8.782738247904481e-05, "loss": 0.5461, "step": 690}, {"epoch": 1.66, "learning_rate": 8.75697187644928e-05, "loss": 0.5618, "step": 691}, {"epoch": 1.66, "learning_rate": 8.731213884332205e-05, "loss": 0.5672, "step": 692}, {"epoch": 1.67, "learning_rate": 8.705464445189647e-05, "loss": 0.5325, "step": 693}, {"epoch": 1.67, "learning_rate": 8.679723732600354e-05, "loss": 0.5193, "step": 694}, {"epoch": 1.67, "learning_rate": 8.653991920084237e-05, "loss": 0.5062, "step": 695}, {"epoch": 1.67, "learning_rate": 8.628269181101216e-05, "loss": 0.5522, "step": 696}, {"epoch": 1.68, "learning_rate": 8.602555689050049e-05, "loss": 0.5266, "step": 697}, {"epoch": 1.68, "learning_rate": 8.57685161726715e-05, "loss": 0.5729, "step": 698}, {"epoch": 1.68, "learning_rate": 8.551157139025437e-05, "loss": 0.5057, "step": 699}, {"epoch": 1.68, "learning_rate": 8.525472427533156e-05, "loss": 0.5386, "step": 700}, {"epoch": 1.69, "learning_rate": 8.499797655932716e-05, "loss": 0.5719, "step": 701}, {"epoch": 1.69, "learning_rate": 8.474132997299521e-05, "loss": 0.5724, "step": 702}, {"epoch": 1.69, "learning_rate": 8.448478624640797e-05, "loss": 0.5662, "step": 703}, {"epoch": 1.69, "learning_rate": 8.422834710894434e-05, "loss": 0.5049, "step": 704}, {"epoch": 1.69, "learning_rate": 8.397201428927827e-05, "loss": 0.5413, "step": 705}, {"epoch": 1.7, "learning_rate": 8.371578951536688e-05, "loss": 0.5586, "step": 706}, {"epoch": 1.7, "learning_rate": 8.345967451443903e-05, "loss": 0.5168, "step": 707}, {"epoch": 1.7, "learning_rate": 8.320367101298351e-05, "loss": 0.543, "step": 708}, {"epoch": 1.7, "learning_rate": 8.294778073673762e-05, "loss": 0.5529, "step": 709}, {"epoch": 1.71, "learning_rate": 8.26920054106753e-05, "loss": 0.5328, "step": 710}, {"epoch": 1.71, "learning_rate": 8.24363467589956e-05, "loss": 0.5031, "step": 711}, {"epoch": 1.71, "learning_rate": 8.218080650511106e-05, "loss": 0.6333, "step": 712}, {"epoch": 1.71, "learning_rate": 8.192538637163621e-05, "loss": 0.5576, "step": 713}, {"epoch": 1.72, "learning_rate": 8.167008808037567e-05, "loss": 0.5456, "step": 714}, {"epoch": 1.72, "learning_rate": 8.141491335231281e-05, "loss": 0.5488, "step": 715}, {"epoch": 1.72, "learning_rate": 8.115986390759806e-05, "loss": 0.5076, "step": 716}, {"epoch": 1.72, "learning_rate": 8.09049414655372e-05, "loss": 0.5572, "step": 717}, {"epoch": 1.73, "learning_rate": 8.065014774458003e-05, "loss": 0.5326, "step": 718}, {"epoch": 1.73, "learning_rate": 8.039548446230857e-05, "loss": 0.5088, "step": 719}, {"epoch": 1.73, "learning_rate": 8.014095333542548e-05, "loss": 0.5004, "step": 720}, {"epoch": 1.73, "learning_rate": 7.988655607974258e-05, "loss": 0.4873, "step": 721}, {"epoch": 1.74, "learning_rate": 7.963229441016937e-05, "loss": 0.528, "step": 722}, {"epoch": 1.74, "learning_rate": 7.93781700407012e-05, "loss": 0.5222, "step": 723}, {"epoch": 1.74, "learning_rate": 7.912418468440794e-05, "loss": 0.5484, "step": 724}, {"epoch": 1.74, "learning_rate": 7.887034005342236e-05, "loss": 0.5131, "step": 725}, {"epoch": 1.75, "learning_rate": 7.861663785892857e-05, "loss": 0.5439, "step": 726}, {"epoch": 1.75, "learning_rate": 7.836307981115055e-05, "loss": 0.5357, "step": 727}, {"epoch": 1.75, "learning_rate": 7.810966761934053e-05, "loss": 0.5757, "step": 728}, {"epoch": 1.75, "learning_rate": 7.785640299176756e-05, "loss": 0.5494, "step": 729}, {"epoch": 1.75, "learning_rate": 7.760328763570588e-05, "loss": 0.5349, "step": 730}, {"epoch": 1.76, "learning_rate": 7.735032325742355e-05, "loss": 0.5191, "step": 731}, {"epoch": 1.76, "learning_rate": 7.709751156217089e-05, "loss": 0.4989, "step": 732}, {"epoch": 1.76, "learning_rate": 7.684485425416888e-05, "loss": 0.5106, "step": 733}, {"epoch": 1.76, "learning_rate": 7.659235303659784e-05, "loss": 0.4986, "step": 734}, {"epoch": 1.77, "learning_rate": 7.634000961158581e-05, "loss": 0.5473, "step": 735}, {"epoch": 1.77, "learning_rate": 7.608782568019729e-05, "loss": 0.5277, "step": 736}, {"epoch": 1.77, "learning_rate": 7.583580294242144e-05, "loss": 0.5492, "step": 737}, {"epoch": 1.77, "learning_rate": 7.558394309716088e-05, "loss": 0.5281, "step": 738}, {"epoch": 1.78, "learning_rate": 7.533224784222015e-05, "loss": 0.5226, "step": 739}, {"epoch": 1.78, "learning_rate": 7.508071887429433e-05, "loss": 0.5597, "step": 740}, {"epoch": 1.78, "learning_rate": 7.48293578889575e-05, "loss": 0.5192, "step": 741}, {"epoch": 1.78, "learning_rate": 7.457816658065134e-05, "loss": 0.5241, "step": 742}, {"epoch": 1.79, "learning_rate": 7.432714664267373e-05, "loss": 0.546, "step": 743}, {"epoch": 1.79, "learning_rate": 7.407629976716732e-05, "loss": 0.5401, "step": 744}, {"epoch": 1.79, "learning_rate": 7.382562764510826e-05, "loss": 0.5226, "step": 745}, {"epoch": 1.79, "learning_rate": 7.35751319662945e-05, "loss": 0.4968, "step": 746}, {"epoch": 1.8, "learning_rate": 7.332481441933467e-05, "loss": 0.5144, "step": 747}, {"epoch": 1.8, "learning_rate": 7.307467669163655e-05, "loss": 0.5528, "step": 748}, {"epoch": 1.8, "learning_rate": 7.282472046939583e-05, "loss": 0.5147, "step": 749}, {"epoch": 1.8, "learning_rate": 7.257494743758459e-05, "loss": 0.5643, "step": 750}, {"epoch": 1.81, "learning_rate": 7.232535927994002e-05, "loss": 0.5208, "step": 751}, {"epoch": 1.81, "learning_rate": 7.207595767895302e-05, "loss": 0.5458, "step": 752}, {"epoch": 1.81, "learning_rate": 7.182674431585704e-05, "loss": 0.5399, "step": 753}, {"epoch": 1.81, "learning_rate": 7.157772087061645e-05, "loss": 0.5607, "step": 754}, {"epoch": 1.81, "learning_rate": 7.132888902191543e-05, "loss": 0.5437, "step": 755}, {"epoch": 1.82, "learning_rate": 7.108025044714661e-05, "loss": 0.5717, "step": 756}, {"epoch": 1.82, "learning_rate": 7.083180682239973e-05, "loss": 0.5097, "step": 757}, {"epoch": 1.82, "learning_rate": 7.058355982245037e-05, "loss": 0.5635, "step": 758}, {"epoch": 1.82, "learning_rate": 7.033551112074865e-05, "loss": 0.6407, "step": 759}, {"epoch": 1.83, "learning_rate": 7.00876623894079e-05, "loss": 0.5192, "step": 760}, {"epoch": 1.83, "learning_rate": 6.984001529919348e-05, "loss": 0.5039, "step": 761}, {"epoch": 1.83, "learning_rate": 6.959257151951153e-05, "loss": 0.5272, "step": 762}, {"epoch": 1.83, "learning_rate": 6.934533271839752e-05, "loss": 0.5452, "step": 763}, {"epoch": 1.84, "learning_rate": 6.909830056250527e-05, "loss": 0.5558, "step": 764}, {"epoch": 1.84, "learning_rate": 6.885147671709554e-05, "loss": 0.5403, "step": 765}, {"epoch": 1.84, "learning_rate": 6.860486284602478e-05, "loss": 0.5335, "step": 766}, {"epoch": 1.84, "learning_rate": 6.83584606117342e-05, "loss": 0.5273, "step": 767}, {"epoch": 1.85, "learning_rate": 6.811227167523815e-05, "loss": 0.5508, "step": 768}, {"epoch": 1.85, "learning_rate": 6.78662976961132e-05, "loss": 0.5571, "step": 769}, {"epoch": 1.85, "learning_rate": 6.762054033248681e-05, "loss": 0.5677, "step": 770}, {"epoch": 1.85, "learning_rate": 6.737500124102638e-05, "loss": 0.5445, "step": 771}, {"epoch": 1.86, "learning_rate": 6.712968207692778e-05, "loss": 0.5711, "step": 772}, {"epoch": 1.86, "learning_rate": 6.688458449390437e-05, "loss": 0.5578, "step": 773}, {"epoch": 1.86, "learning_rate": 6.663971014417586e-05, "loss": 0.4867, "step": 774}, {"epoch": 1.86, "learning_rate": 6.639506067845697e-05, "loss": 0.5173, "step": 775}, {"epoch": 1.87, "learning_rate": 6.615063774594676e-05, "loss": 0.5125, "step": 776}, {"epoch": 1.87, "learning_rate": 6.590644299431696e-05, "loss": 0.5537, "step": 777}, {"epoch": 1.87, "learning_rate": 6.566247806970119e-05, "loss": 0.5271, "step": 778}, {"epoch": 1.87, "learning_rate": 6.541874461668373e-05, "loss": 0.5398, "step": 779}, {"epoch": 1.88, "learning_rate": 6.51752442782887e-05, "loss": 0.5575, "step": 780}, {"epoch": 1.88, "learning_rate": 6.493197869596857e-05, "loss": 0.5267, "step": 781}, {"epoch": 1.88, "learning_rate": 6.468894950959336e-05, "loss": 0.5017, "step": 782}, {"epoch": 1.88, "learning_rate": 6.444615835743955e-05, "loss": 0.5252, "step": 783}, {"epoch": 1.88, "learning_rate": 6.420360687617897e-05, "loss": 0.5538, "step": 784}, {"epoch": 1.89, "learning_rate": 6.396129670086791e-05, "loss": 0.5672, "step": 785}, {"epoch": 1.89, "learning_rate": 6.371922946493591e-05, "loss": 0.5507, "step": 786}, {"epoch": 1.89, "learning_rate": 6.347740680017489e-05, "loss": 0.5539, "step": 787}, {"epoch": 1.89, "learning_rate": 6.323583033672799e-05, "loss": 0.5061, "step": 788}, {"epoch": 1.9, "learning_rate": 6.299450170307888e-05, "loss": 0.5423, "step": 789}, {"epoch": 1.9, "learning_rate": 6.275342252604044e-05, "loss": 0.532, "step": 790}, {"epoch": 1.9, "learning_rate": 6.251259443074398e-05, "loss": 0.5159, "step": 791}, {"epoch": 1.9, "learning_rate": 6.227201904062829e-05, "loss": 0.5775, "step": 792}, {"epoch": 1.91, "learning_rate": 6.203169797742861e-05, "loss": 0.546, "step": 793}, {"epoch": 1.91, "learning_rate": 6.179163286116581e-05, "loss": 0.5534, "step": 794}, {"epoch": 1.91, "learning_rate": 6.155182531013528e-05, "loss": 0.4836, "step": 795}, {"epoch": 1.91, "learning_rate": 6.13122769408963e-05, "loss": 0.4762, "step": 796}, {"epoch": 1.92, "learning_rate": 6.107298936826086e-05, "loss": 0.5048, "step": 797}, {"epoch": 1.92, "learning_rate": 6.083396420528298e-05, "loss": 0.5543, "step": 798}, {"epoch": 1.92, "learning_rate": 6.059520306324774e-05, "loss": 0.5128, "step": 799}, {"epoch": 1.92, "learning_rate": 6.035670755166043e-05, "loss": 0.5628, "step": 800}, {"epoch": 1.93, "learning_rate": 6.011847927823567e-05, "loss": 0.4919, "step": 801}, {"epoch": 1.93, "learning_rate": 5.988051984888667e-05, "loss": 0.5571, "step": 802}, {"epoch": 1.93, "learning_rate": 5.964283086771435e-05, "loss": 0.5824, "step": 803}, {"epoch": 1.93, "learning_rate": 5.940541393699646e-05, "loss": 0.5059, "step": 804}, {"epoch": 1.94, "learning_rate": 5.916827065717686e-05, "loss": 0.6107, "step": 805}, {"epoch": 1.94, "learning_rate": 5.893140262685469e-05, "loss": 0.5222, "step": 806}, {"epoch": 1.94, "learning_rate": 5.869481144277372e-05, "loss": 0.536, "step": 807}, {"epoch": 1.94, "learning_rate": 5.845849869981137e-05, "loss": 0.5281, "step": 808}, {"epoch": 1.94, "learning_rate": 5.8222465990968075e-05, "loss": 0.4939, "step": 809}, {"epoch": 1.95, "learning_rate": 5.798671490735661e-05, "loss": 0.5198, "step": 810}, {"epoch": 1.95, "learning_rate": 5.775124703819124e-05, "loss": 0.5373, "step": 811}, {"epoch": 1.95, "learning_rate": 5.751606397077702e-05, "loss": 0.5254, "step": 812}, {"epoch": 1.95, "learning_rate": 5.728116729049928e-05, "loss": 0.5531, "step": 813}, {"epoch": 1.96, "learning_rate": 5.704655858081268e-05, "loss": 0.4936, "step": 814}, {"epoch": 1.96, "learning_rate": 5.681223942323066e-05, "loss": 0.5251, "step": 815}, {"epoch": 1.96, "learning_rate": 5.657821139731476e-05, "loss": 0.5315, "step": 816}, {"epoch": 1.96, "learning_rate": 5.634447608066409e-05, "loss": 0.5475, "step": 817}, {"epoch": 1.97, "learning_rate": 5.611103504890444e-05, "loss": 0.5086, "step": 818}, {"epoch": 1.97, "learning_rate": 5.5877889875677845e-05, "loss": 0.4843, "step": 819}, {"epoch": 1.97, "learning_rate": 5.564504213263205e-05, "loss": 0.4841, "step": 820}, {"epoch": 1.97, "learning_rate": 5.541249338940968e-05, "loss": 0.5269, "step": 821}, {"epoch": 1.98, "learning_rate": 5.518024521363778e-05, "loss": 0.5199, "step": 822}, {"epoch": 1.98, "learning_rate": 5.4948299170917325e-05, "loss": 0.5196, "step": 823}, {"epoch": 1.98, "learning_rate": 5.4716656824812505e-05, "loss": 0.5408, "step": 824}, {"epoch": 1.98, "learning_rate": 5.448531973684039e-05, "loss": 0.5438, "step": 825}, {"epoch": 1.99, "learning_rate": 5.425428946646016e-05, "loss": 0.4914, "step": 826}, {"epoch": 1.99, "learning_rate": 5.4023567571062774e-05, "loss": 0.5556, "step": 827}, {"epoch": 1.99, "learning_rate": 5.379315560596038e-05, "loss": 0.5119, "step": 828}, {"epoch": 1.99, "learning_rate": 5.356305512437594e-05, "loss": 0.5408, "step": 829}, {"epoch": 2.0, "learning_rate": 5.333326767743263e-05, "loss": 0.5287, "step": 830}, {"epoch": 2.0, "learning_rate": 5.3103794814143425e-05, "loss": 0.6645, "step": 831}, {"epoch": 2.0, "learning_rate": 5.2874638081400694e-05, "loss": 0.5139, "step": 832}, {"epoch": 2.0, "learning_rate": 5.26457990239657e-05, "loss": 0.4981, "step": 833}, {"epoch": 2.0, "learning_rate": 5.241727918445836e-05, "loss": 0.5396, "step": 834}, {"epoch": 2.01, "learning_rate": 5.21890801033466e-05, "loss": 0.5057, "step": 835}, {"epoch": 2.01, "learning_rate": 5.1961203318936116e-05, "loss": 0.5281, "step": 836}, {"epoch": 2.01, "learning_rate": 5.1733650367359964e-05, "loss": 0.5175, "step": 837}, {"epoch": 2.01, "learning_rate": 5.1506422782568345e-05, "loss": 0.515, "step": 838}, {"epoch": 2.02, "learning_rate": 5.127952209631797e-05, "loss": 0.5244, "step": 839}, {"epoch": 2.02, "learning_rate": 5.105294983816202e-05, "loss": 0.5194, "step": 840}, {"epoch": 2.02, "learning_rate": 5.082670753543961e-05, "loss": 0.526, "step": 841}, {"epoch": 2.02, "learning_rate": 5.0600796713265765e-05, "loss": 0.522, "step": 842}, {"epoch": 2.03, "learning_rate": 5.0375218894520834e-05, "loss": 0.5434, "step": 843}, {"epoch": 2.03, "learning_rate": 5.014997559984045e-05, "loss": 0.4654, "step": 844}, {"epoch": 2.03, "learning_rate": 4.9925068347605117e-05, "loss": 0.5147, "step": 845}, {"epoch": 2.03, "learning_rate": 4.970049865393008e-05, "loss": 0.5171, "step": 846}, {"epoch": 2.04, "learning_rate": 4.947626803265519e-05, "loss": 0.5975, "step": 847}, {"epoch": 2.04, "learning_rate": 4.9252377995334444e-05, "loss": 0.4986, "step": 848}, {"epoch": 2.04, "learning_rate": 4.9028830051225994e-05, "loss": 0.5162, "step": 849}, {"epoch": 2.04, "learning_rate": 4.8805625707281877e-05, "loss": 0.4652, "step": 850}, {"epoch": 2.05, "learning_rate": 4.8582766468138005e-05, "loss": 0.5521, "step": 851}, {"epoch": 2.05, "learning_rate": 4.836025383610382e-05, "loss": 0.4842, "step": 852}, {"epoch": 2.05, "learning_rate": 4.813808931115228e-05, "loss": 0.5294, "step": 853}, {"epoch": 2.05, "learning_rate": 4.791627439090975e-05, "loss": 0.4995, "step": 854}, {"epoch": 2.06, "learning_rate": 4.7694810570645795e-05, "loss": 0.4943, "step": 855}, {"epoch": 2.06, "learning_rate": 4.74736993432634e-05, "loss": 0.5486, "step": 856}, {"epoch": 2.06, "learning_rate": 4.7252942199288487e-05, "loss": 0.4907, "step": 857}, {"epoch": 2.06, "learning_rate": 4.703254062686017e-05, "loss": 0.4713, "step": 858}, {"epoch": 2.06, "learning_rate": 4.6812496111720585e-05, "loss": 0.5018, "step": 859}, {"epoch": 2.07, "learning_rate": 4.6592810137205e-05, "loss": 0.4636, "step": 860}, {"epoch": 2.07, "learning_rate": 4.637348418423169e-05, "loss": 0.4841, "step": 861}, {"epoch": 2.07, "learning_rate": 4.615451973129196e-05, "loss": 0.4869, "step": 862}, {"epoch": 2.07, "learning_rate": 4.593591825444028e-05, "loss": 0.4933, "step": 863}, {"epoch": 2.08, "learning_rate": 4.57176812272842e-05, "loss": 0.5078, "step": 864}, {"epoch": 2.08, "learning_rate": 4.5499810120974616e-05, "loss": 0.5275, "step": 865}, {"epoch": 2.08, "learning_rate": 4.528230640419562e-05, "loss": 0.5332, "step": 866}, {"epoch": 2.08, "learning_rate": 4.5065171543154725e-05, "loss": 0.5347, "step": 867}, {"epoch": 2.09, "learning_rate": 4.484840700157295e-05, "loss": 0.4948, "step": 868}, {"epoch": 2.09, "learning_rate": 4.4632014240675034e-05, "loss": 0.527, "step": 869}, {"epoch": 2.09, "learning_rate": 4.4415994719179456e-05, "loss": 0.5066, "step": 870}, {"epoch": 2.09, "learning_rate": 4.420034989328866e-05, "loss": 0.5595, "step": 871}, {"epoch": 2.1, "learning_rate": 4.398508121667925e-05, "loss": 0.4583, "step": 872}, {"epoch": 2.1, "learning_rate": 4.377019014049223e-05, "loss": 0.4733, "step": 873}, {"epoch": 2.1, "learning_rate": 4.355567811332311e-05, "loss": 0.526, "step": 874}, {"epoch": 2.1, "learning_rate": 4.334154658121222e-05, "loss": 0.4948, "step": 875}, {"epoch": 2.11, "learning_rate": 4.312779698763493e-05, "loss": 0.5079, "step": 876}, {"epoch": 2.11, "learning_rate": 4.2914430773492035e-05, "loss": 0.5342, "step": 877}, {"epoch": 2.11, "learning_rate": 4.270144937709981e-05, "loss": 0.4851, "step": 878}, {"epoch": 2.11, "learning_rate": 4.24888542341805e-05, "loss": 0.481, "step": 879}, {"epoch": 2.12, "learning_rate": 4.2276646777852636e-05, "loss": 0.4784, "step": 880}, {"epoch": 2.12, "learning_rate": 4.206482843862126e-05, "loss": 0.5263, "step": 881}, {"epoch": 2.12, "learning_rate": 4.185340064436839e-05, "loss": 0.5085, "step": 882}, {"epoch": 2.12, "learning_rate": 4.164236482034327e-05, "loss": 0.4933, "step": 883}, {"epoch": 2.12, "learning_rate": 4.1431722389153016e-05, "loss": 0.4742, "step": 884}, {"epoch": 2.13, "learning_rate": 4.12214747707527e-05, "loss": 0.5245, "step": 885}, {"epoch": 2.13, "learning_rate": 4.101162338243595e-05, "loss": 0.5098, "step": 886}, {"epoch": 2.13, "learning_rate": 4.080216963882548e-05, "loss": 0.497, "step": 887}, {"epoch": 2.13, "learning_rate": 4.059311495186338e-05, "loss": 0.4982, "step": 888}, {"epoch": 2.14, "learning_rate": 4.0384460730801667e-05, "loss": 0.5363, "step": 889}, {"epoch": 2.14, "learning_rate": 4.017620838219276e-05, "loss": 0.4962, "step": 890}, {"epoch": 2.14, "learning_rate": 3.9968359309880156e-05, "loss": 0.4941, "step": 891}, {"epoch": 2.14, "learning_rate": 3.976091491498871e-05, "loss": 0.5204, "step": 892}, {"epoch": 2.15, "learning_rate": 3.9553876595915375e-05, "loss": 0.4859, "step": 893}, {"epoch": 2.15, "learning_rate": 3.9347245748319705e-05, "loss": 0.5024, "step": 894}, {"epoch": 2.15, "learning_rate": 3.9141023765114426e-05, "loss": 0.5205, "step": 895}, {"epoch": 2.15, "learning_rate": 3.893521203645618e-05, "loss": 0.4734, "step": 896}, {"epoch": 2.16, "learning_rate": 3.872981194973597e-05, "loss": 0.5131, "step": 897}, {"epoch": 2.16, "learning_rate": 3.852482488956992e-05, "loss": 0.5162, "step": 898}, {"epoch": 2.16, "learning_rate": 3.8320252237789865e-05, "loss": 0.5365, "step": 899}, {"epoch": 2.16, "learning_rate": 3.81160953734342e-05, "loss": 0.4888, "step": 900}, {"epoch": 2.17, "learning_rate": 3.7912355672738364e-05, "loss": 0.5155, "step": 901}, {"epoch": 2.17, "learning_rate": 3.770903450912571e-05, "loss": 0.4999, "step": 902}, {"epoch": 2.17, "learning_rate": 3.750613325319817e-05, "loss": 0.5304, "step": 903}, {"epoch": 2.17, "learning_rate": 3.7303653272727057e-05, "loss": 0.5313, "step": 904}, {"epoch": 2.18, "learning_rate": 3.710159593264392e-05, "loss": 0.4928, "step": 905}, {"epoch": 2.18, "learning_rate": 3.689996259503116e-05, "loss": 0.4939, "step": 906}, {"epoch": 2.18, "learning_rate": 3.669875461911297e-05, "loss": 0.5545, "step": 907}, {"epoch": 2.18, "learning_rate": 3.649797336124615e-05, "loss": 0.5103, "step": 908}, {"epoch": 2.19, "learning_rate": 3.629762017491104e-05, "loss": 0.4847, "step": 909}, {"epoch": 2.19, "learning_rate": 3.609769641070221e-05, "loss": 0.4809, "step": 910}, {"epoch": 2.19, "learning_rate": 3.589820341631951e-05, "loss": 0.4572, "step": 911}, {"epoch": 2.19, "learning_rate": 3.569914253655896e-05, "loss": 0.5014, "step": 912}, {"epoch": 2.19, "learning_rate": 3.550051511330361e-05, "loss": 0.5029, "step": 913}, {"epoch": 2.2, "learning_rate": 3.530232248551466e-05, "loss": 0.4783, "step": 914}, {"epoch": 2.2, "learning_rate": 3.510456598922221e-05, "loss": 0.5933, "step": 915}, {"epoch": 2.2, "learning_rate": 3.490724695751642e-05, "loss": 0.4969, "step": 916}, {"epoch": 2.2, "learning_rate": 3.4710366720538415e-05, "loss": 0.5163, "step": 917}, {"epoch": 2.21, "learning_rate": 3.45139266054715e-05, "loss": 0.4814, "step": 918}, {"epoch": 2.21, "learning_rate": 3.431792793653198e-05, "loss": 0.6026, "step": 919}, {"epoch": 2.21, "learning_rate": 3.412237203496036e-05, "loss": 0.5093, "step": 920}, {"epoch": 2.21, "learning_rate": 3.392726021901244e-05, "loss": 0.5113, "step": 921}, {"epoch": 2.22, "learning_rate": 3.3732593803950355e-05, "loss": 0.513, "step": 922}, {"epoch": 2.22, "learning_rate": 3.3538374102033866e-05, "loss": 0.5165, "step": 923}, {"epoch": 2.22, "learning_rate": 3.334460242251134e-05, "loss": 0.5024, "step": 924}, {"epoch": 2.22, "learning_rate": 3.315128007161099e-05, "loss": 0.4992, "step": 925}, {"epoch": 2.23, "learning_rate": 3.295840835253206e-05, "loss": 0.4679, "step": 926}, {"epoch": 2.23, "learning_rate": 3.276598856543614e-05, "loss": 0.5223, "step": 927}, {"epoch": 2.23, "learning_rate": 3.257402200743821e-05, "loss": 0.5201, "step": 928}, {"epoch": 2.23, "learning_rate": 3.238250997259808e-05, "loss": 0.515, "step": 929}, {"epoch": 2.24, "learning_rate": 3.21914537519115e-05, "loss": 0.5379, "step": 930}, {"epoch": 2.24, "learning_rate": 3.2000854633301694e-05, "loss": 0.5239, "step": 931}, {"epoch": 2.24, "learning_rate": 3.181071390161037e-05, "loss": 0.5118, "step": 932}, {"epoch": 2.24, "learning_rate": 3.1621032838589305e-05, "loss": 0.4582, "step": 933}, {"epoch": 2.25, "learning_rate": 3.1431812722891594e-05, "loss": 0.516, "step": 934}, {"epoch": 2.25, "learning_rate": 3.1243054830063035e-05, "loss": 0.5142, "step": 935}, {"epoch": 2.25, "learning_rate": 3.1054760432533624e-05, "loss": 0.596, "step": 936}, {"epoch": 2.25, "learning_rate": 3.086693079960883e-05, "loss": 0.5092, "step": 937}, {"epoch": 2.25, "learning_rate": 3.0679567197461134e-05, "loss": 0.4874, "step": 938}, {"epoch": 2.26, "learning_rate": 3.0492670889121433e-05, "loss": 0.5213, "step": 939}, {"epoch": 2.26, "learning_rate": 3.030624313447067e-05, "loss": 0.5306, "step": 940}, {"epoch": 2.26, "learning_rate": 3.0120285190231144e-05, "loss": 0.5029, "step": 941}, {"epoch": 2.26, "learning_rate": 2.9934798309958146e-05, "loss": 0.5299, "step": 942}, {"epoch": 2.27, "learning_rate": 2.974978374403147e-05, "loss": 0.5384, "step": 943}, {"epoch": 2.27, "learning_rate": 2.9565242739647114e-05, "loss": 0.5083, "step": 944}, {"epoch": 2.27, "learning_rate": 2.938117654080863e-05, "loss": 0.5724, "step": 945}, {"epoch": 2.27, "learning_rate": 2.9197586388318932e-05, "loss": 0.4805, "step": 946}, {"epoch": 2.28, "learning_rate": 2.9014473519771913e-05, "loss": 0.5247, "step": 947}, {"epoch": 2.28, "learning_rate": 2.8831839169543996e-05, "loss": 0.5304, "step": 948}, {"epoch": 2.28, "learning_rate": 2.864968456878586e-05, "loss": 0.5059, "step": 949}, {"epoch": 2.28, "learning_rate": 2.8468010945414303e-05, "loss": 0.4839, "step": 950}, {"epoch": 2.29, "learning_rate": 2.828681952410366e-05, "loss": 0.4722, "step": 951}, {"epoch": 2.29, "learning_rate": 2.8106111526277767e-05, "loss": 0.5168, "step": 952}, {"epoch": 2.29, "learning_rate": 2.7925888170101665e-05, "loss": 0.5025, "step": 953}, {"epoch": 2.29, "learning_rate": 2.7746150670473458e-05, "loss": 0.4958, "step": 954}, {"epoch": 2.3, "learning_rate": 2.756690023901596e-05, "loss": 0.4777, "step": 955}, {"epoch": 2.3, "learning_rate": 2.738813808406866e-05, "loss": 0.5256, "step": 956}, {"epoch": 2.3, "learning_rate": 2.7209865410679536e-05, "loss": 0.4755, "step": 957}, {"epoch": 2.3, "learning_rate": 2.7032083420597e-05, "loss": 0.4982, "step": 958}, {"epoch": 2.31, "learning_rate": 2.685479331226164e-05, "loss": 0.4751, "step": 959}, {"epoch": 2.31, "learning_rate": 2.667799628079829e-05, "loss": 0.4993, "step": 960}, {"epoch": 2.31, "learning_rate": 2.6501693518007896e-05, "loss": 0.596, "step": 961}, {"epoch": 2.31, "learning_rate": 2.6325886212359498e-05, "loss": 0.4851, "step": 962}, {"epoch": 2.31, "learning_rate": 2.6150575548982292e-05, "loss": 0.477, "step": 963}, {"epoch": 2.32, "learning_rate": 2.5975762709657504e-05, "loss": 0.4548, "step": 964}, {"epoch": 2.32, "learning_rate": 2.580144887281051e-05, "loss": 0.5181, "step": 965}, {"epoch": 2.32, "learning_rate": 2.562763521350283e-05, "loss": 0.5043, "step": 966}, {"epoch": 2.32, "learning_rate": 2.5454322903424398e-05, "loss": 0.4955, "step": 967}, {"epoch": 2.33, "learning_rate": 2.528151311088537e-05, "loss": 0.4692, "step": 968}, {"epoch": 2.33, "learning_rate": 2.5109207000808455e-05, "loss": 0.4974, "step": 969}, {"epoch": 2.33, "learning_rate": 2.4937405734720966e-05, "loss": 0.5115, "step": 970}, {"epoch": 2.33, "learning_rate": 2.476611047074713e-05, "loss": 0.522, "step": 971}, {"epoch": 2.34, "learning_rate": 2.4595322363600072e-05, "loss": 0.4962, "step": 972}, {"epoch": 2.34, "learning_rate": 2.4425042564574184e-05, "loss": 0.4948, "step": 973}, {"epoch": 2.34, "learning_rate": 2.4255272221537295e-05, "loss": 0.505, "step": 974}, {"epoch": 2.34, "learning_rate": 2.4086012478922958e-05, "loss": 0.4491, "step": 975}, {"epoch": 2.35, "learning_rate": 2.3917264477722788e-05, "loss": 0.5062, "step": 976}, {"epoch": 2.35, "learning_rate": 2.374902935547866e-05, "loss": 0.5069, "step": 977}, {"epoch": 2.35, "learning_rate": 2.3581308246275103e-05, "loss": 0.5049, "step": 978}, {"epoch": 2.35, "learning_rate": 2.341410228073163e-05, "loss": 0.5099, "step": 979}, {"epoch": 2.36, "learning_rate": 2.324741258599521e-05, "loss": 0.4952, "step": 980}, {"epoch": 2.36, "learning_rate": 2.308124028573253e-05, "loss": 0.5383, "step": 981}, {"epoch": 2.36, "learning_rate": 2.29155865001225e-05, "loss": 0.5148, "step": 982}, {"epoch": 2.36, "learning_rate": 2.2750452345848682e-05, "loss": 0.4764, "step": 983}, {"epoch": 2.37, "learning_rate": 2.2585838936091754e-05, "loss": 0.4848, "step": 984}, {"epoch": 2.37, "learning_rate": 2.2421747380522095e-05, "loss": 0.5027, "step": 985}, {"epoch": 2.37, "learning_rate": 2.225817878529214e-05, "loss": 0.5082, "step": 986}, {"epoch": 2.37, "learning_rate": 2.2095134253029037e-05, "loss": 0.5086, "step": 987}, {"epoch": 2.38, "learning_rate": 2.1932614882827197e-05, "loss": 0.523, "step": 988}, {"epoch": 2.38, "learning_rate": 2.1770621770240905e-05, "loss": 0.5186, "step": 989}, {"epoch": 2.38, "learning_rate": 2.1609156007276876e-05, "loss": 0.5175, "step": 990}, {"epoch": 2.38, "learning_rate": 2.1448218682386922e-05, "loss": 0.5367, "step": 991}, {"epoch": 2.38, "learning_rate": 2.1287810880460635e-05, "loss": 0.4746, "step": 992}, {"epoch": 2.39, "learning_rate": 2.112793368281799e-05, "loss": 0.4885, "step": 993}, {"epoch": 2.39, "learning_rate": 2.0968588167202262e-05, "loss": 0.502, "step": 994}, {"epoch": 2.39, "learning_rate": 2.0809775407772503e-05, "loss": 0.4628, "step": 995}, {"epoch": 2.39, "learning_rate": 2.0651496475096453e-05, "loss": 0.4709, "step": 996}, {"epoch": 2.4, "learning_rate": 2.0493752436143264e-05, "loss": 0.4771, "step": 997}, {"epoch": 2.4, "learning_rate": 2.03365443542764e-05, "loss": 0.493, "step": 998}, {"epoch": 2.4, "learning_rate": 2.0179873289246355e-05, "loss": 0.5219, "step": 999}, {"epoch": 2.4, "learning_rate": 2.0023740297183534e-05, "loss": 0.507, "step": 1000}, {"epoch": 2.41, "learning_rate": 1.9868146430591194e-05, "loss": 0.4902, "step": 1001}, {"epoch": 2.41, "learning_rate": 1.971309273833828e-05, "loss": 0.5074, "step": 1002}, {"epoch": 2.41, "learning_rate": 1.9558580265652448e-05, "loss": 0.4746, "step": 1003}, {"epoch": 2.41, "learning_rate": 1.940461005411288e-05, "loss": 0.522, "step": 1004}, {"epoch": 2.42, "learning_rate": 1.925118314164337e-05, "loss": 0.4993, "step": 1005}, {"epoch": 2.42, "learning_rate": 1.9098300562505266e-05, "loss": 0.5047, "step": 1006}, {"epoch": 2.42, "learning_rate": 1.8945963347290607e-05, "loss": 0.5472, "step": 1007}, {"epoch": 2.42, "learning_rate": 1.879417252291502e-05, "loss": 0.5008, "step": 1008}, {"epoch": 2.43, "learning_rate": 1.8642929112610875e-05, "loss": 0.5022, "step": 1009}, {"epoch": 2.43, "learning_rate": 1.8492234135920462e-05, "loss": 0.4677, "step": 1010}, {"epoch": 2.43, "learning_rate": 1.8342088608688944e-05, "loss": 0.5649, "step": 1011}, {"epoch": 2.43, "learning_rate": 1.8192493543057674e-05, "loss": 0.4687, "step": 1012}, {"epoch": 2.44, "learning_rate": 1.804344994745727e-05, "loss": 0.5245, "step": 1013}, {"epoch": 2.44, "learning_rate": 1.7894958826600882e-05, "loss": 0.4817, "step": 1014}, {"epoch": 2.44, "learning_rate": 1.7747021181477374e-05, "loss": 0.4652, "step": 1015}, {"epoch": 2.44, "learning_rate": 1.7599638009344566e-05, "loss": 0.5258, "step": 1016}, {"epoch": 2.44, "learning_rate": 1.74528103037226e-05, "loss": 0.5618, "step": 1017}, {"epoch": 2.45, "learning_rate": 1.730653905438714e-05, "loss": 0.512, "step": 1018}, {"epoch": 2.45, "learning_rate": 1.7160825247362723e-05, "loss": 0.5165, "step": 1019}, {"epoch": 2.45, "learning_rate": 1.701566986491614e-05, "loss": 0.5052, "step": 1020}, {"epoch": 2.45, "learning_rate": 1.6871073885549847e-05, "loss": 0.5324, "step": 1021}, {"epoch": 2.46, "learning_rate": 1.672703828399529e-05, "loss": 0.4823, "step": 1022}, {"epoch": 2.46, "learning_rate": 1.6583564031206357e-05, "loss": 0.5252, "step": 1023}, {"epoch": 2.46, "learning_rate": 1.644065209435284e-05, "loss": 0.5177, "step": 1024}, {"epoch": 2.46, "learning_rate": 1.6298303436813977e-05, "loss": 0.5438, "step": 1025}, {"epoch": 2.47, "learning_rate": 1.6156519018171857e-05, "loss": 0.5111, "step": 1026}, {"epoch": 2.47, "learning_rate": 1.6015299794204996e-05, "loss": 0.4801, "step": 1027}, {"epoch": 2.47, "learning_rate": 1.587464671688187e-05, "loss": 0.5361, "step": 1028}, {"epoch": 2.47, "learning_rate": 1.5734560734354618e-05, "loss": 0.5052, "step": 1029}, {"epoch": 2.48, "learning_rate": 1.559504279095244e-05, "loss": 0.4966, "step": 1030}, {"epoch": 2.48, "learning_rate": 1.5456093827175422e-05, "loss": 0.4775, "step": 1031}, {"epoch": 2.48, "learning_rate": 1.5317714779688074e-05, "loss": 0.5051, "step": 1032}, {"epoch": 2.48, "learning_rate": 1.5179906581313064e-05, "loss": 0.5053, "step": 1033}, {"epoch": 2.49, "learning_rate": 1.5042670161024974e-05, "loss": 0.5024, "step": 1034}, {"epoch": 2.49, "learning_rate": 1.4906006443943943e-05, "loss": 0.5155, "step": 1035}, {"epoch": 2.49, "learning_rate": 1.4769916351329493e-05, "loss": 0.5087, "step": 1036}, {"epoch": 2.49, "learning_rate": 1.4634400800574278e-05, "loss": 0.4933, "step": 1037}, {"epoch": 2.5, "learning_rate": 1.4499460705197998e-05, "loss": 0.5178, "step": 1038}, {"epoch": 2.5, "learning_rate": 1.4365096974841108e-05, "loss": 0.5424, "step": 1039}, {"epoch": 2.5, "learning_rate": 1.4231310515258744e-05, "loss": 0.4899, "step": 1040}, {"epoch": 2.5, "learning_rate": 1.4098102228314658e-05, "loss": 0.4934, "step": 1041}, {"epoch": 2.5, "learning_rate": 1.3965473011975038e-05, "loss": 0.5035, "step": 1042}, {"epoch": 2.51, "learning_rate": 1.3833423760302611e-05, "loss": 0.4784, "step": 1043}, {"epoch": 2.51, "learning_rate": 1.3701955363450447e-05, "loss": 0.5001, "step": 1044}, {"epoch": 2.51, "learning_rate": 1.3571068707656065e-05, "loss": 0.4956, "step": 1045}, {"epoch": 2.51, "learning_rate": 1.3440764675235384e-05, "loss": 0.4566, "step": 1046}, {"epoch": 2.52, "learning_rate": 1.33110441445769e-05, "loss": 0.5641, "step": 1047}, {"epoch": 2.52, "learning_rate": 1.3181907990135622e-05, "loss": 0.5072, "step": 1048}, {"epoch": 2.52, "learning_rate": 1.3053357082427253e-05, "loss": 0.492, "step": 1049}, {"epoch": 2.52, "learning_rate": 1.2925392288022298e-05, "loss": 0.5022, "step": 1050}, {"epoch": 2.53, "learning_rate": 1.279801446954023e-05, "loss": 0.478, "step": 1051}, {"epoch": 2.53, "learning_rate": 1.267122448564374e-05, "loss": 0.4974, "step": 1052}, {"epoch": 2.53, "learning_rate": 1.2545023191032801e-05, "loss": 0.4973, "step": 1053}, {"epoch": 2.53, "learning_rate": 1.2419411436439022e-05, "loss": 0.481, "step": 1054}, {"epoch": 2.54, "learning_rate": 1.2294390068619877e-05, "loss": 0.6139, "step": 1055}, {"epoch": 2.54, "learning_rate": 1.2169959930353047e-05, "loss": 0.4896, "step": 1056}, {"epoch": 2.54, "learning_rate": 1.2046121860430637e-05, "loss": 0.4906, "step": 1057}, {"epoch": 2.54, "learning_rate": 1.1922876693653585e-05, "loss": 0.5379, "step": 1058}, {"epoch": 2.55, "learning_rate": 1.1800225260826037e-05, "loss": 0.4611, "step": 1059}, {"epoch": 2.55, "learning_rate": 1.1678168388749788e-05, "loss": 0.476, "step": 1060}, {"epoch": 2.55, "learning_rate": 1.1556706900218572e-05, "loss": 0.5371, "step": 1061}, {"epoch": 2.55, "learning_rate": 1.1435841614012666e-05, "loss": 0.5284, "step": 1062}, {"epoch": 2.56, "learning_rate": 1.131557334489326e-05, "loss": 0.4955, "step": 1063}, {"epoch": 2.56, "learning_rate": 1.1195902903597023e-05, "loss": 0.5041, "step": 1064}, {"epoch": 2.56, "learning_rate": 1.1076831096830676e-05, "loss": 0.4744, "step": 1065}, {"epoch": 2.56, "learning_rate": 1.0958358727265438e-05, "loss": 0.4895, "step": 1066}, {"epoch": 2.56, "learning_rate": 1.0840486593531706e-05, "loss": 0.5091, "step": 1067}, {"epoch": 2.57, "learning_rate": 1.0723215490213634e-05, "loss": 0.4817, "step": 1068}, {"epoch": 2.57, "learning_rate": 1.0606546207843837e-05, "loss": 0.476, "step": 1069}, {"epoch": 2.57, "learning_rate": 1.0490479532897946e-05, "loss": 0.5003, "step": 1070}, {"epoch": 2.57, "learning_rate": 1.0375016247789404e-05, "loss": 0.5438, "step": 1071}, {"epoch": 2.58, "learning_rate": 1.026015713086418e-05, "loss": 0.4734, "step": 1072}, {"epoch": 2.58, "learning_rate": 1.0145902956395447e-05, "loss": 0.4936, "step": 1073}, {"epoch": 2.58, "learning_rate": 1.003225449457852e-05, "loss": 0.4971, "step": 1074}, {"epoch": 2.58, "learning_rate": 9.919212511525456e-06, "loss": 0.4796, "step": 1075}, {"epoch": 2.59, "learning_rate": 9.806777769260033e-06, "loss": 0.5202, "step": 1076}, {"epoch": 2.59, "learning_rate": 9.69495102571263e-06, "loss": 0.4866, "step": 1077}, {"epoch": 2.59, "learning_rate": 9.583733034714981e-06, "loss": 0.4856, "step": 1078}, {"epoch": 2.59, "learning_rate": 9.473124545995249e-06, "loss": 0.5034, "step": 1079}, {"epoch": 2.6, "learning_rate": 9.363126305172831e-06, "loss": 0.4679, "step": 1080}, {"epoch": 2.6, "learning_rate": 9.253739053753474e-06, "loss": 0.5151, "step": 1081}, {"epoch": 2.6, "learning_rate": 9.144963529124162e-06, "loss": 0.4847, "step": 1082}, {"epoch": 2.6, "learning_rate": 9.036800464548157e-06, "loss": 0.5021, "step": 1083}, {"epoch": 2.61, "learning_rate": 8.929250589160166e-06, "loss": 0.5388, "step": 1084}, {"epoch": 2.61, "learning_rate": 8.822314627961293e-06, "loss": 0.4783, "step": 1085}, {"epoch": 2.61, "learning_rate": 8.715993301814173e-06, "loss": 0.498, "step": 1086}, {"epoch": 2.61, "learning_rate": 8.610287327438227e-06, "loss": 0.4922, "step": 1087}, {"epoch": 2.62, "learning_rate": 8.505197417404687e-06, "loss": 0.4737, "step": 1088}, {"epoch": 2.62, "learning_rate": 8.400724280131866e-06, "loss": 0.5591, "step": 1089}, {"epoch": 2.62, "learning_rate": 8.296868619880372e-06, "loss": 0.4679, "step": 1090}, {"epoch": 2.62, "learning_rate": 8.193631136748347e-06, "loss": 0.4975, "step": 1091}, {"epoch": 2.62, "learning_rate": 8.091012526666796e-06, "loss": 0.4809, "step": 1092}, {"epoch": 2.63, "learning_rate": 7.989013481394814e-06, "loss": 0.4877, "step": 1093}, {"epoch": 2.63, "learning_rate": 7.887634688515e-06, "loss": 0.4645, "step": 1094}, {"epoch": 2.63, "learning_rate": 7.786876831428736e-06, "loss": 0.5396, "step": 1095}, {"epoch": 2.63, "learning_rate": 7.686740589351704e-06, "loss": 0.4949, "step": 1096}, {"epoch": 2.64, "learning_rate": 7.5872266373092085e-06, "loss": 0.4633, "step": 1097}, {"epoch": 2.64, "learning_rate": 7.488335646131628e-06, "loss": 0.5377, "step": 1098}, {"epoch": 2.64, "learning_rate": 7.390068282449936e-06, "loss": 0.5019, "step": 1099}, {"epoch": 2.64, "learning_rate": 7.292425208691212e-06, "loss": 0.4966, "step": 1100}, {"epoch": 2.65, "learning_rate": 7.195407083074135e-06, "loss": 0.5109, "step": 1101}, {"epoch": 2.65, "learning_rate": 7.0990145596045555e-06, "loss": 0.5369, "step": 1102}, {"epoch": 2.65, "learning_rate": 7.003248288071118e-06, "loss": 0.5185, "step": 1103}, {"epoch": 2.65, "learning_rate": 6.908108914040823e-06, "loss": 0.5248, "step": 1104}, {"epoch": 2.66, "learning_rate": 6.813597078854772e-06, "loss": 0.524, "step": 1105}, {"epoch": 2.66, "learning_rate": 6.7197134196237295e-06, "loss": 0.4422, "step": 1106}, {"epoch": 2.66, "learning_rate": 6.626458569223926e-06, "loss": 0.4757, "step": 1107}, {"epoch": 2.66, "learning_rate": 6.533833156292679e-06, "loss": 0.5045, "step": 1108}, {"epoch": 2.67, "learning_rate": 6.441837805224327e-06, "loss": 0.5092, "step": 1109}, {"epoch": 2.67, "learning_rate": 6.350473136165835e-06, "loss": 0.4645, "step": 1110}, {"epoch": 2.67, "learning_rate": 6.25973976501274e-06, "loss": 0.5219, "step": 1111}, {"epoch": 2.67, "learning_rate": 6.169638303404912e-06, "loss": 0.5058, "step": 1112}, {"epoch": 2.68, "learning_rate": 6.08016935872251e-06, "loss": 0.5238, "step": 1113}, {"epoch": 2.68, "learning_rate": 5.991333534081878e-06, "loss": 0.5011, "step": 1114}, {"epoch": 2.68, "learning_rate": 5.903131428331399e-06, "loss": 0.5257, "step": 1115}, {"epoch": 2.68, "learning_rate": 5.8155636360475385e-06, "loss": 0.5376, "step": 1116}, {"epoch": 2.69, "learning_rate": 5.728630747530805e-06, "loss": 0.5046, "step": 1117}, {"epoch": 2.69, "learning_rate": 5.6423333488018095e-06, "loss": 0.481, "step": 1118}, {"epoch": 2.69, "learning_rate": 5.556672021597232e-06, "loss": 0.4668, "step": 1119}, {"epoch": 2.69, "learning_rate": 5.471647343365982e-06, "loss": 0.4931, "step": 1120}, {"epoch": 2.69, "learning_rate": 5.387259887265261e-06, "loss": 0.5704, "step": 1121}, {"epoch": 2.7, "learning_rate": 5.303510222156716e-06, "loss": 0.474, "step": 1122}, {"epoch": 2.7, "learning_rate": 5.22039891260262e-06, "loss": 0.5272, "step": 1123}, {"epoch": 2.7, "learning_rate": 5.137926518862013e-06, "loss": 0.5158, "step": 1124}, {"epoch": 2.7, "learning_rate": 5.056093596886991e-06, "loss": 0.5083, "step": 1125}, {"epoch": 2.71, "learning_rate": 4.974900698318885e-06, "loss": 0.5022, "step": 1126}, {"epoch": 2.71, "learning_rate": 4.8943483704846475e-06, "loss": 0.4642, "step": 1127}, {"epoch": 2.71, "learning_rate": 4.8144371563930476e-06, "loss": 0.4981, "step": 1128}, {"epoch": 2.71, "learning_rate": 4.735167594731083e-06, "loss": 0.476, "step": 1129}, {"epoch": 2.72, "learning_rate": 4.656540219860317e-06, "loss": 0.4895, "step": 1130}, {"epoch": 2.72, "learning_rate": 4.57855556181328e-06, "loss": 0.5247, "step": 1131}, {"epoch": 2.72, "learning_rate": 4.501214146289956e-06, "loss": 0.4451, "step": 1132}, {"epoch": 2.72, "learning_rate": 4.424516494654118e-06, "loss": 0.4542, "step": 1133}, {"epoch": 2.73, "learning_rate": 4.3484631239299356e-06, "loss": 0.518, "step": 1134}, {"epoch": 2.73, "learning_rate": 4.273054546798394e-06, "loss": 0.4875, "step": 1135}, {"epoch": 2.73, "learning_rate": 4.198291271593924e-06, "loss": 0.4778, "step": 1136}, {"epoch": 2.73, "learning_rate": 4.1241738023009016e-06, "loss": 0.4766, "step": 1137}, {"epoch": 2.74, "learning_rate": 4.050702638550275e-06, "loss": 0.4871, "step": 1138}, {"epoch": 2.74, "learning_rate": 3.97787827561622e-06, "loss": 0.524, "step": 1139}, {"epoch": 2.74, "learning_rate": 3.9057012044127815e-06, "loss": 0.4614, "step": 1140}, {"epoch": 2.74, "learning_rate": 3.834171911490569e-06, "loss": 0.497, "step": 1141}, {"epoch": 2.75, "learning_rate": 3.7632908790334655e-06, "loss": 0.474, "step": 1142}, {"epoch": 2.75, "learning_rate": 3.693058584855369e-06, "loss": 0.4832, "step": 1143}, {"epoch": 2.75, "learning_rate": 3.6234755023970446e-06, "loss": 0.5033, "step": 1144}, {"epoch": 2.75, "learning_rate": 3.5545421007228243e-06, "loss": 0.6024, "step": 1145}, {"epoch": 2.75, "learning_rate": 3.4862588445174984e-06, "loss": 0.655, "step": 1146}, {"epoch": 2.76, "learning_rate": 3.4186261940832076e-06, "loss": 0.4979, "step": 1147}, {"epoch": 2.76, "learning_rate": 3.3516446053363015e-06, "loss": 0.4942, "step": 1148}, {"epoch": 2.76, "learning_rate": 3.2853145298042953e-06, "loss": 0.4998, "step": 1149}, {"epoch": 2.76, "learning_rate": 3.2196364146227507e-06, "loss": 0.4919, "step": 1150}, {"epoch": 2.77, "learning_rate": 3.154610702532412e-06, "loss": 0.5304, "step": 1151}, {"epoch": 2.77, "learning_rate": 3.090237831876053e-06, "loss": 0.6093, "step": 1152}, {"epoch": 2.77, "learning_rate": 3.026518236595621e-06, "loss": 0.5022, "step": 1153}, {"epoch": 2.77, "learning_rate": 2.9634523462293005e-06, "loss": 0.5008, "step": 1154}, {"epoch": 2.78, "learning_rate": 2.9010405859086097e-06, "loss": 0.4821, "step": 1155}, {"epoch": 2.78, "learning_rate": 2.839283376355506e-06, "loss": 0.4893, "step": 1156}, {"epoch": 2.78, "learning_rate": 2.778181133879576e-06, "loss": 0.4899, "step": 1157}, {"epoch": 2.78, "learning_rate": 2.717734270375272e-06, "loss": 0.5189, "step": 1158}, {"epoch": 2.79, "learning_rate": 2.657943193319035e-06, "loss": 0.4717, "step": 1159}, {"epoch": 2.79, "learning_rate": 2.5988083057666533e-06, "loss": 0.4901, "step": 1160}, {"epoch": 2.79, "learning_rate": 2.5403300063504555e-06, "loss": 0.4924, "step": 1161}, {"epoch": 2.79, "learning_rate": 2.482508689276675e-06, "loss": 0.5018, "step": 1162}, {"epoch": 2.8, "learning_rate": 2.4253447443228106e-06, "loss": 0.4853, "step": 1163}, {"epoch": 2.8, "learning_rate": 2.3688385568349515e-06, "loss": 0.4936, "step": 1164}, {"epoch": 2.8, "learning_rate": 2.3129905077251768e-06, "loss": 0.4938, "step": 1165}, {"epoch": 2.8, "learning_rate": 2.2578009734690265e-06, "loss": 0.4883, "step": 1166}, {"epoch": 2.81, "learning_rate": 2.2032703261029686e-06, "loss": 0.4925, "step": 1167}, {"epoch": 2.81, "learning_rate": 2.1493989332218468e-06, "loss": 0.4825, "step": 1168}, {"epoch": 2.81, "learning_rate": 2.096187157976426e-06, "loss": 0.4968, "step": 1169}, {"epoch": 2.81, "learning_rate": 2.043635359070928e-06, "loss": 0.488, "step": 1170}, {"epoch": 2.81, "learning_rate": 1.9917438907606556e-06, "loss": 0.467, "step": 1171}, {"epoch": 2.82, "learning_rate": 1.9405131028495836e-06, "loss": 0.4479, "step": 1172}, {"epoch": 2.82, "learning_rate": 1.8899433406879608e-06, "loss": 0.5255, "step": 1173}, {"epoch": 2.82, "learning_rate": 1.8400349451700438e-06, "loss": 0.4865, "step": 1174}, {"epoch": 2.82, "learning_rate": 1.7907882527317454e-06, "loss": 0.4842, "step": 1175}, {"epoch": 2.83, "learning_rate": 1.742203595348435e-06, "loss": 0.491, "step": 1176}, {"epoch": 2.83, "learning_rate": 1.6942813005326075e-06, "loss": 0.5241, "step": 1177}, {"epoch": 2.83, "learning_rate": 1.6470216913317626e-06, "loss": 0.5181, "step": 1178}, {"epoch": 2.83, "learning_rate": 1.6004250863261739e-06, "loss": 0.5163, "step": 1179}, {"epoch": 2.84, "learning_rate": 1.554491799626756e-06, "loss": 0.4831, "step": 1180}, {"epoch": 2.84, "learning_rate": 1.509222140872979e-06, "loss": 0.4652, "step": 1181}, {"epoch": 2.84, "learning_rate": 1.4646164152307018e-06, "loss": 0.4944, "step": 1182}, {"epoch": 2.84, "learning_rate": 1.4206749233902084e-06, "loss": 0.501, "step": 1183}, {"epoch": 2.85, "learning_rate": 1.3773979615640975e-06, "loss": 0.4823, "step": 1184}, {"epoch": 2.85, "learning_rate": 1.3347858214853736e-06, "loss": 0.5145, "step": 1185}, {"epoch": 2.85, "learning_rate": 1.292838790405393e-06, "loss": 0.4883, "step": 1186}, {"epoch": 2.85, "learning_rate": 1.2515571510919754e-06, "loss": 0.5201, "step": 1187}, {"epoch": 2.86, "learning_rate": 1.2109411818274852e-06, "loss": 0.5294, "step": 1188}, {"epoch": 2.86, "learning_rate": 1.1709911564069976e-06, "loss": 0.5123, "step": 1189}, {"epoch": 2.86, "learning_rate": 1.1317073441363457e-06, "loss": 0.4945, "step": 1190}, {"epoch": 2.86, "learning_rate": 1.0930900098304443e-06, "loss": 0.4477, "step": 1191}, {"epoch": 2.87, "learning_rate": 1.055139413811379e-06, "loss": 0.4927, "step": 1192}, {"epoch": 2.87, "learning_rate": 1.0178558119067315e-06, "loss": 0.5659, "step": 1193}, {"epoch": 2.87, "learning_rate": 9.812394554478355e-07, "loss": 0.4963, "step": 1194}, {"epoch": 2.87, "learning_rate": 9.452905912680665e-07, "loss": 0.4735, "step": 1195}, {"epoch": 2.88, "learning_rate": 9.10009461701189e-07, "loss": 0.4797, "step": 1196}, {"epoch": 2.88, "learning_rate": 8.753963045797342e-07, "loss": 0.4976, "step": 1197}, {"epoch": 2.88, "learning_rate": 8.41451353233369e-07, "loss": 0.5003, "step": 1198}, {"epoch": 2.88, "learning_rate": 8.081748364873521e-07, "loss": 0.5025, "step": 1199}, {"epoch": 2.88, "learning_rate": 7.755669786609687e-07, "loss": 0.5096, "step": 1200}], "logging_steps": 1.0, "max_steps": 1248, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "total_flos": 8.120963972254925e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}