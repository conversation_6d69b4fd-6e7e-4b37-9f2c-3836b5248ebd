{"_name_or_path": "/data/yuanxiaoyan/ECGpro/Pertrain_M/llava-v1.6-vicuna-7b", "architectures": ["LlavaLlamaForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bos_token_id": 1, "eos_token_id": 2, "freeze_mm_mlp_adapter": false, "freeze_mm_vision_resampler": false, "hidden_act": "silu", "hidden_size": 4096, "image_aspect_ratio": "anyres", "image_crop_resolution": 224, "image_grid_pinpoints": [[336, 672], [672, 336], [672, 672], [1008, 336], [336, 1008]], "image_split_resolution": 224, "initializer_range": 0.02, "intermediate_size": 11008, "max_position_embeddings": 4096, "mm_ecg_tower": "/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt", "mm_hidden_size": 768, "mm_patch_merge_type": "flat", "mm_projector_lr": null, "mm_projector_type": "linear", "mm_resampler_type": null, "mm_use_ecg_patch_token": false, "mm_use_ecg_start_end": false, "mm_use_im_patch_token": false, "mm_use_im_start_end": false, "mm_vision_select_feature": "patch", "mm_vision_select_layer": -2, "mm_vision_tower": "openai/clip-vit-large-patch14-336", "mm_vision_tower_lr": 2e-06, "model_type": "llava_llama", "num_attention_heads": 32, "num_hidden_layers": 32, "num_key_value_heads": 32, "pad_token_id": 0, "pretraining_tp": 1, "rms_norm_eps": 1e-05, "rope_scaling": null, "rope_theta": 10000.0, "tie_word_embeddings": false, "tokenizer_model_max_length": 2048, "tokenizer_padding_side": "right", "torch_dtype": "bfloat16", "transformers_version": "4.37.2", "tune_mm_mlp_adapter": true, "tune_mm_vision_resampler": false, "unfreeze_mm_vision_tower": true, "use_cache": false, "use_mm_proj": true, "vocab_size": 32000}