#!/usr/bin/env python3
"""
Test script to verify the inference fix
"""

import os
import sys
import torch

# Add paths to sys.path for proper imports
script_dir = os.path.dirname(os.path.abspath(__file__))
llava_dir = os.path.join(script_dir, 'llava')
open_clip_dir = os.path.join(script_dir, 'open_clip')
sys.path.insert(0, script_dir)
sys.path.insert(0, llava_dir)
sys.path.insert(0, open_clip_dir)

from llava.llm_inference_lora import LLaVAECGInference

def test_inference():
    """Test the fixed inference code"""
    
    # Initialize inference class with default paths
    inference = LLaVAECGInference(
        model_path='/data/yuanxiaoyan/ECGpro/ECG-Chat/checkpoints/llava-vicuna-13b-v1.5-finetune-ecginstruct_lora',
        base_model_path='/data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5',
        ecg_tower_path='/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt',
        ecg_folder='/data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0',
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    print("Loading model...")
    try:
        inference.load_model()
        print("Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return False
    
    # Test with a sample ECG file (you may need to adjust the path)
    test_ecg_path = "files/p1700/p17002760/s47763168/47763168"  # Example from training data
    full_ecg_path = os.path.join(inference.ecg_folder, test_ecg_path)
    
    if not os.path.exists(full_ecg_path + '.hea'):
        print(f"Test ECG file not found: {full_ecg_path}.hea")
        print("Please provide a valid ECG file path for testing.")
        return False
    
    print(f"Testing with ECG: {test_ecg_path}")
    
    # Test questions
    test_questions = [
        "What does my ECG result indicate?",
        "Is there any abnormality in the electrocardiogram? Can you explain it in detail?",
        "What is the heart rhythm shown in this ECG?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- Test {i} ---")
        print(f"Question: {question}")
        
        try:
            response = inference.generate_response(
                ecg_path=full_ecg_path,
                question=question,
                max_new_tokens=256,
                temperature=0.7
            )
            
            print(f"Response: {response}")
            
            # Check if response contains ECG-specific content
            if response and len(response.strip()) > 10:
                print("✓ Generated meaningful response")
            else:
                print("✗ Response seems too short or empty")
                
        except Exception as e:
            print(f"✗ Error generating response: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("Testing LLaVA ECG Inference Fix")
    print("=" * 50)
    
    success = test_inference()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ Test completed successfully!")
        print("The inference fix appears to be working.")
    else:
        print("\n" + "=" * 50)
        print("✗ Test failed!")
        print("There may still be issues with the inference code.")
