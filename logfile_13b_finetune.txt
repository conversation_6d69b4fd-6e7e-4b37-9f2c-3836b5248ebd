/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/cuda/__init__.py:51: FutureWarning: The pynvml package is deprecated. Please install nvidia-ml-py instead. If you did not install pynvml directly, please report this to the maintainers of the package that installed pynvml for you.
  import pynvml  # type: ignore[import]
[2025-09-13 05:23:23,359] [INFO] [real_accelerator.py:161:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-09-13 05:23:24,970] [WARNING] [runner.py:202:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
[2025-09-13 05:23:24,970] [INFO] [runner.py:571:main] cmd = /data/yuanxiaoyan/ECGpro/llava/bin/python -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMSwgNSwgN119 --master_addr=127.0.0.1 --master_port=29500 --enable_each_rank_log=None /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llava/train/train_mem.py --deepspeed /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llava/scripts/zero2.json --lora_enable True --model_name_or_path /data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5 --version v1 --data_path /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/playground/data/diagnosis.json --ecg_folder /data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0 --ecg_tower /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt --open_clip_config coca_ViT-B-32 --pretrain_mm_mlp_adapter ./checkpoints/llava-vicuna-13b-v1.5-pretrain/mm_projector.bin --mm_projector_type linear --mm_use_ecg_start_end False --mm_use_ecg_patch_token False --group_by_modality_length True --bf16 True --output_dir ./checkpoints/llava-vicuna-13b-v1.5-finetune_lora --num_train_epochs 3 --per_device_train_batch_size 16 --per_device_eval_batch_size 4 --gradient_accumulation_steps 1 --evaluation_strategy no --save_strategy steps --save_steps 100 --save_total_limit 1 --learning_rate 2e-4 --weight_decay 0. --warmup_ratio 0.03 --lr_scheduler_type cosine --logging_steps 1 --tf32 True --model_max_length 2048 --gradient_checkpointing True --dataloader_num_workers 4 --lazy_preprocess True
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/cuda/__init__.py:51: FutureWarning: The pynvml package is deprecated. Please install nvidia-ml-py instead. If you did not install pynvml directly, please report this to the maintainers of the package that installed pynvml for you.
  import pynvml  # type: ignore[import]
[2025-09-13 05:23:26,780] [INFO] [real_accelerator.py:161:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-09-13 05:23:28,510] [INFO] [launch.py:145:main] WORLD INFO DICT: {'localhost': [1, 5, 7]}
[2025-09-13 05:23:28,510] [INFO] [launch.py:151:main] nnodes=1, num_local_procs=3, node_rank=0
[2025-09-13 05:23:28,510] [INFO] [launch.py:162:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2]})
[2025-09-13 05:23:28,510] [INFO] [launch.py:163:main] dist_world_size=3
[2025-09-13 05:23:28,510] [INFO] [launch.py:165:main] Setting CUDA_VISIBLE_DEVICES=1,5,7
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/cuda/__init__.py:51: FutureWarning: The pynvml package is deprecated. Please install nvidia-ml-py instead. If you did not install pynvml directly, please report this to the maintainers of the package that installed pynvml for you.
  import pynvml  # type: ignore[import]
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/cuda/__init__.py:51: FutureWarning: The pynvml package is deprecated. Please install nvidia-ml-py instead. If you did not install pynvml directly, please report this to the maintainers of the package that installed pynvml for you.
  import pynvml  # type: ignore[import]
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/cuda/__init__.py:51: FutureWarning: The pynvml package is deprecated. Please install nvidia-ml-py instead. If you did not install pynvml directly, please report this to the maintainers of the package that installed pynvml for you.
  import pynvml  # type: ignore[import]
[2025-09-13 05:23:30,735] [INFO] [real_accelerator.py:161:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-09-13 05:23:31,599] [INFO] [real_accelerator.py:161:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-09-13 05:23:31,762] [INFO] [real_accelerator.py:161:get_accelerator] Setting ds_accelerator to cuda (auto detect)
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
[2025-09-13 05:23:33,140] [INFO] [comm.py:637:init_distributed] cdb=None
[2025-09-13 05:23:33,140] [INFO] [comm.py:668:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
[2025-09-13 05:23:33,400] [INFO] [comm.py:637:init_distributed] cdb=None
[2025-09-13 05:23:33,501] [INFO] [comm.py:637:init_distributed] cdb=None
You are using a model of type llama to instantiate a model of type llava_llama. This is not supported for all configurations of models and can yield errors.
You are attempting to use Flash Attention 2.0 with a model not initialized on GPU. Make sure to move the model to GPU after initializing it on CPU with `model.to('cuda')`.

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()
  return self.fget.__get__(instance, owner)()
You are using a model of type llama to instantiate a model of type llava_llama. This is not supported for all configurations of models and can yield errors.
You are attempting to use Flash Attention 2.0 with a model not initialized on GPU. Make sure to move the model to GPU after initializing it on CPU with `model.to('cuda')`.
You are using a model of type llama to instantiate a model of type llava_llama. This is not supported for all configurations of models and can yield errors.
You are attempting to use Flash Attention 2.0 with a model not initialized on GPU. Make sure to move the model to GPU after initializing it on CPU with `model.to('cuda')`.

Loading checkpoint shards:  33%|███▎      | 1/3 [00:02<00:04,  2.01s/it]
Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()
  return self.fget.__get__(instance, owner)()

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()
  return self.fget.__get__(instance, owner)()

Loading checkpoint shards:  67%|██████▋   | 2/3 [00:03<00:01,  1.92s/it]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:01<00:03,  1.85s/it]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:01<00:03,  1.87s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:05<00:00,  1.58s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:05<00:00,  1.68s/it]
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:392: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:397: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(
Adding LoRA adapters...

Loading checkpoint shards:  67%|██████▋   | 2/3 [00:03<00:01,  1.68s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:03<00:01,  1.75s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:04<00:00,  1.27s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:04<00:00,  1.40s/it]
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:392: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:397: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(

Loading checkpoint shards: 100%|██████████| 3/3 [00:04<00:00,  1.27s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:04<00:00,  1.41s/it]
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:392: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.9` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/transformers/generation/configuration_utils.py:397: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0.6` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/huggingface_hub/file_download.py:945: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/huggingface_hub/file_download.py:945: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.
  warnings.warn(
Some weights of the model checkpoint at ncbi/MedCPT-Query-Encoder were not used when initializing BertModel: ['pooler.dense.bias', 'pooler.dense.weight']
- This IS expected if you are initializing BertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at ncbi/MedCPT-Query-Encoder were not used when initializing BertModel: ['pooler.dense.bias', 'pooler.dense.weight']
- This IS expected if you are initializing BertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/huggingface_hub/file_download.py:945: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.
  warnings.warn(
Some weights of the model checkpoint at ncbi/MedCPT-Query-Encoder were not used when initializing BertModel: ['pooler.dense.bias', 'pooler.dense.weight']
- This IS expected if you are initializing BertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Loaded /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt model
Loaded /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt model
Formatting inputs...Skip in lazy mode
Loaded /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt model
wandb: Tracking run with wandb version 0.21.3
wandb: W&B syncing is set to `offline` in this directory. Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
wandb: Run data is saved locally in /data/yuanxiaoyan/ECGpro/ECG-Chat/wandb/offline-run-20250913_052602-kmhs1dpa

  0%|          | 0/1248 [00:00<?, ?it/s]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py:1652: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)
  total_norm_cuda = get_accelerator().FloatTensor([float(total_norm)])
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py:1652: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)
  total_norm_cuda = get_accelerator().FloatTensor([float(total_norm)])
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py:1652: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)
  total_norm_cuda = get_accelerator().FloatTensor([float(total_norm)])

  0%|          | 1/1248 [00:07<2:38:55,  7.65s/it]
                                                  
{'loss': 2.3601, 'learning_rate': 5.263157894736842e-06, 'epoch': 0.0}

  0%|          | 1/1248 [00:07<2:38:55,  7.65s/it]
  0%|          | 2/1248 [00:13<2:16:10,  6.56s/it]
                                                  
{'loss': 2.3794, 'learning_rate': 1.0526315789473684e-05, 'epoch': 0.0}

  0%|          | 2/1248 [00:13<2:16:10,  6.56s/it]
  0%|          | 3/1248 [00:19<2:10:01,  6.27s/it]
                                                  
{'loss': 2.3731, 'learning_rate': 1.5789473684210526e-05, 'epoch': 0.01}

  0%|          | 3/1248 [00:19<2:10:01,  6.27s/it]
  0%|          | 4/1248 [00:25<2:05:58,  6.08s/it]
                                                  
{'loss': 2.4972, 'learning_rate': 2.105263157894737e-05, 'epoch': 0.01}

  0%|          | 4/1248 [00:25<2:05:58,  6.08s/it]
  0%|          | 5/1248 [00:31<2:04:26,  6.01s/it]
                                                  
{'loss': 2.351, 'learning_rate': 2.6315789473684212e-05, 'epoch': 0.01}

  0%|          | 5/1248 [00:31<2:04:26,  6.01s/it]
  0%|          | 6/1248 [00:37<2:04:14,  6.00s/it]
                                                  
{'loss': 2.4104, 'learning_rate': 3.157894736842105e-05, 'epoch': 0.01}

  0%|          | 6/1248 [00:37<2:04:14,  6.00s/it]
  1%|          | 7/1248 [00:42<2:01:25,  5.87s/it]
                                                  
{'loss': 2.4539, 'learning_rate': 3.6842105263157895e-05, 'epoch': 0.02}

  1%|          | 7/1248 [00:42<2:01:25,  5.87s/it]
  1%|          | 8/1248 [00:48<1:59:22,  5.78s/it]
                                                  
{'loss': 2.3098, 'learning_rate': 4.210526315789474e-05, 'epoch': 0.02}

  1%|          | 8/1248 [00:48<1:59:22,  5.78s/it]
  1%|          | 9/1248 [00:54<2:00:15,  5.82s/it]
                                                  
{'loss': 2.2293, 'learning_rate': 4.736842105263158e-05, 'epoch': 0.02}

  1%|          | 9/1248 [00:54<2:00:15,  5.82s/it]
  1%|          | 10/1248 [00:59<1:59:24,  5.79s/it]
                                                   
{'loss': 2.1323, 'learning_rate': 5.2631578947368424e-05, 'epoch': 0.02}

  1%|          | 10/1248 [00:59<1:59:24,  5.79s/it]
  1%|          | 11/1248 [01:06<2:03:24,  5.99s/it]
                                                   
{'loss': 2.0639, 'learning_rate': 5.789473684210527e-05, 'epoch': 0.03}

  1%|          | 11/1248 [01:06<2:03:24,  5.99s/it]
  1%|          | 12/1248 [01:12<2:03:01,  5.97s/it]
                                                   
{'loss': 2.0178, 'learning_rate': 6.31578947368421e-05, 'epoch': 0.03}

  1%|          | 12/1248 [01:12<2:03:01,  5.97s/it]
  1%|          | 13/1248 [01:17<2:01:12,  5.89s/it]
                                                   
{'loss': 1.8966, 'learning_rate': 6.842105263157895e-05, 'epoch': 0.03}

  1%|          | 13/1248 [01:17<2:01:12,  5.89s/it]
  1%|          | 14/1248 [01:23<2:02:07,  5.94s/it]
                                                   
{'loss': 1.7366, 'learning_rate': 7.368421052631579e-05, 'epoch': 0.03}

  1%|          | 14/1248 [01:23<2:02:07,  5.94s/it]
  1%|          | 15/1248 [01:29<2:01:08,  5.89s/it]
                                                   
{'loss': 1.6628, 'learning_rate': 7.894736842105263e-05, 'epoch': 0.04}

  1%|          | 15/1248 [01:29<2:01:08,  5.89s/it]
  1%|▏         | 16/1248 [01:35<2:00:11,  5.85s/it]
                                                   
{'loss': 1.5299, 'learning_rate': 8.421052631578948e-05, 'epoch': 0.04}

  1%|▏         | 16/1248 [01:35<2:00:11,  5.85s/it]
  1%|▏         | 17/1248 [01:41<1:58:45,  5.79s/it]
                                                   
{'loss': 1.4947, 'learning_rate': 8.947368421052632e-05, 'epoch': 0.04}

  1%|▏         | 17/1248 [01:41<1:58:45,  5.79s/it]
  1%|▏         | 18/1248 [01:47<1:59:08,  5.81s/it]
                                                   
{'loss': 1.4903, 'learning_rate': 9.473684210526316e-05, 'epoch': 0.04}

  1%|▏         | 18/1248 [01:47<1:59:08,  5.81s/it]
  2%|▏         | 19/1248 [01:52<1:58:25,  5.78s/it]
                                                   
{'loss': 1.4535, 'learning_rate': 0.0001, 'epoch': 0.05}

  2%|▏         | 19/1248 [01:52<1:58:25,  5.78s/it]
  2%|▏         | 20/1248 [01:58<2:00:47,  5.90s/it]
                                                   
{'loss': 1.393, 'learning_rate': 0.00010526315789473685, 'epoch': 0.05}

  2%|▏         | 20/1248 [01:58<2:00:47,  5.90s/it]
  2%|▏         | 21/1248 [02:04<1:58:29,  5.79s/it]
                                                   
{'loss': 1.3825, 'learning_rate': 0.0001105263157894737, 'epoch': 0.05}

  2%|▏         | 21/1248 [02:04<1:58:29,  5.79s/it]
  2%|▏         | 22/1248 [02:10<2:00:36,  5.90s/it]
                                                   
{'loss': 1.3603, 'learning_rate': 0.00011578947368421053, 'epoch': 0.05}

  2%|▏         | 22/1248 [02:10<2:00:36,  5.90s/it]
  2%|▏         | 23/1248 [02:16<1:58:02,  5.78s/it]
                                                   
{'loss': 1.3011, 'learning_rate': 0.00012105263157894738, 'epoch': 0.06}

  2%|▏         | 23/1248 [02:16<1:58:02,  5.78s/it]
  2%|▏         | 24/1248 [02:22<1:59:51,  5.88s/it]
                                                   
{'loss': 1.3168, 'learning_rate': 0.0001263157894736842, 'epoch': 0.06}

  2%|▏         | 24/1248 [02:22<1:59:51,  5.88s/it]
  2%|▏         | 25/1248 [02:27<1:58:06,  5.79s/it]
                                                   
{'loss': 1.3272, 'learning_rate': 0.00013157894736842108, 'epoch': 0.06}

  2%|▏         | 25/1248 [02:27<1:58:06,  5.79s/it]
  2%|▏         | 26/1248 [02:33<1:59:26,  5.86s/it]
                                                   
{'loss': 1.3059, 'learning_rate': 0.0001368421052631579, 'epoch': 0.06}

  2%|▏         | 26/1248 [02:33<1:59:26,  5.86s/it]
  2%|▏         | 27/1248 [02:40<2:01:21,  5.96s/it]
                                                   
{'loss': 1.221, 'learning_rate': 0.00014210526315789474, 'epoch': 0.06}

  2%|▏         | 27/1248 [02:40<2:01:21,  5.96s/it]
  2%|▏         | 28/1248 [02:45<2:00:49,  5.94s/it]
                                                   
{'loss': 1.2329, 'learning_rate': 0.00014736842105263158, 'epoch': 0.07}

  2%|▏         | 28/1248 [02:45<2:00:49,  5.94s/it]
  2%|▏         | 29/1248 [02:51<2:00:38,  5.94s/it]
                                                   
{'loss': 1.1462, 'learning_rate': 0.00015263157894736845, 'epoch': 0.07}

  2%|▏         | 29/1248 [02:51<2:00:38,  5.94s/it]
  2%|▏         | 30/1248 [02:57<1:58:43,  5.85s/it]
                                                   
{'loss': 1.1311, 'learning_rate': 0.00015789473684210527, 'epoch': 0.07}

  2%|▏         | 30/1248 [02:57<1:58:43,  5.85s/it]
  2%|▏         | 31/1248 [03:03<1:57:58,  5.82s/it]
                                                   
{'loss': 1.1482, 'learning_rate': 0.0001631578947368421, 'epoch': 0.07}

  2%|▏         | 31/1248 [03:03<1:57:58,  5.82s/it]
  3%|▎         | 32/1248 [03:09<2:01:32,  6.00s/it]
                                                   
{'loss': 1.0966, 'learning_rate': 0.00016842105263157895, 'epoch': 0.08}

  3%|▎         | 32/1248 [03:09<2:01:32,  6.00s/it]
  3%|▎         | 33/1248 [03:15<2:00:24,  5.95s/it]
                                                   
{'loss': 1.1087, 'learning_rate': 0.0001736842105263158, 'epoch': 0.08}

  3%|▎         | 33/1248 [03:15<2:00:24,  5.95s/it]
  3%|▎         | 34/1248 [03:21<2:00:14,  5.94s/it]
                                                   
{'loss': 1.074, 'learning_rate': 0.00017894736842105264, 'epoch': 0.08}

  3%|▎         | 34/1248 [03:21<2:00:14,  5.94s/it]
  3%|▎         | 35/1248 [03:27<2:01:44,  6.02s/it]
                                                   
{'loss': 1.0868, 'learning_rate': 0.00018421052631578948, 'epoch': 0.08}

  3%|▎         | 35/1248 [03:27<2:01:44,  6.02s/it]
  3%|▎         | 36/1248 [03:33<1:59:47,  5.93s/it]
                                                   
{'loss': 0.9677, 'learning_rate': 0.00018947368421052632, 'epoch': 0.09}

  3%|▎         | 36/1248 [03:33<1:59:47,  5.93s/it]
  3%|▎         | 37/1248 [03:39<1:59:06,  5.90s/it]
                                                   
{'loss': 0.9858, 'learning_rate': 0.00019473684210526317, 'epoch': 0.09}

  3%|▎         | 37/1248 [03:39<1:59:06,  5.90s/it]
  3%|▎         | 38/1248 [03:44<1:58:34,  5.88s/it]
                                                   
{'loss': 1.0689, 'learning_rate': 0.0002, 'epoch': 0.09}

  3%|▎         | 38/1248 [03:45<1:58:34,  5.88s/it]
  3%|▎         | 39/1248 [03:50<1:58:06,  5.86s/it]
                                                   
{'loss': 0.9834, 'learning_rate': 0.0001999996629465591, 'epoch': 0.09}

  3%|▎         | 39/1248 [03:50<1:58:06,  5.86s/it]
  3%|▎         | 40/1248 [03:56<1:59:20,  5.93s/it]
                                                   
{'loss': 0.9666, 'learning_rate': 0.00019999865178850845, 'epoch': 0.1}

  3%|▎         | 40/1248 [03:56<1:59:20,  5.93s/it]
  3%|▎         | 41/1248 [04:02<1:58:32,  5.89s/it]
                                                   
{'loss': 0.9427, 'learning_rate': 0.00019999696653266437, 'epoch': 0.1}

  3%|▎         | 41/1248 [04:02<1:58:32,  5.89s/it]
  3%|▎         | 42/1248 [04:08<1:58:24,  5.89s/it]
                                                   
{'loss': 0.9224, 'learning_rate': 0.00019999460719038732, 'epoch': 0.1}

  3%|▎         | 42/1248 [04:08<1:58:24,  5.89s/it]
  3%|▎         | 43/1248 [04:14<1:58:52,  5.92s/it]
                                                   
{'loss': 0.9571, 'learning_rate': 0.0001999915737775817, 'epoch': 0.1}

  3%|▎         | 43/1248 [04:14<1:58:52,  5.92s/it]
  4%|▎         | 44/1248 [04:20<1:56:41,  5.82s/it]
                                                   
{'loss': 0.9875, 'learning_rate': 0.00019998786631469603, 'epoch': 0.11}

  4%|▎         | 44/1248 [04:20<1:56:41,  5.82s/it]
  4%|▎         | 45/1248 [04:25<1:54:39,  5.72s/it]
                                                   
{'loss': 0.8777, 'learning_rate': 0.0001999834848267225, 'epoch': 0.11}

  4%|▎         | 45/1248 [04:25<1:54:39,  5.72s/it]
  4%|▎         | 46/1248 [04:31<1:53:35,  5.67s/it]
                                                   
{'loss': 0.8734, 'learning_rate': 0.0001999784293431971, 'epoch': 0.11}

  4%|▎         | 46/1248 [04:31<1:53:35,  5.67s/it]
  4%|▍         | 47/1248 [04:36<1:49:11,  5.45s/it]
                                                   
{'loss': 0.8826, 'learning_rate': 0.00019997269989819916, 'epoch': 0.11}

  4%|▍         | 47/1248 [04:36<1:49:11,  5.45s/it]
  4%|▍         | 48/1248 [04:41<1:49:55,  5.50s/it]
                                                   
{'loss': 0.9106, 'learning_rate': 0.00019996629653035126, 'epoch': 0.12}

  4%|▍         | 48/1248 [04:41<1:49:55,  5.50s/it]
  4%|▍         | 49/1248 [04:47<1:50:01,  5.51s/it]
                                                   
{'loss': 0.8671, 'learning_rate': 0.00019995921928281894, 'epoch': 0.12}

  4%|▍         | 49/1248 [04:47<1:50:01,  5.51s/it]
  4%|▍         | 50/1248 [04:52<1:49:00,  5.46s/it]
                                                   
{'loss': 0.8983, 'learning_rate': 0.0001999514682033104, 'epoch': 0.12}

  4%|▍         | 50/1248 [04:52<1:49:00,  5.46s/it]
  4%|▍         | 51/1248 [04:57<1:47:11,  5.37s/it]
                                                   
{'loss': 0.8398, 'learning_rate': 0.00019994304334407622, 'epoch': 0.12}

  4%|▍         | 51/1248 [04:57<1:47:11,  5.37s/it]
  4%|▍         | 52/1248 [05:03<1:46:14,  5.33s/it]
                                                   
{'loss': 0.8633, 'learning_rate': 0.000199933944761909, 'epoch': 0.12}

  4%|▍         | 52/1248 [05:03<1:46:14,  5.33s/it]
  4%|▍         | 53/1248 [05:08<1:45:45,  5.31s/it]
                                                   
{'loss': 0.841, 'learning_rate': 0.00019992417251814282, 'epoch': 0.13}

  4%|▍         | 53/1248 [05:08<1:45:45,  5.31s/it]
  4%|▍         | 54/1248 [05:13<1:46:36,  5.36s/it]
                                                   
{'loss': 0.8118, 'learning_rate': 0.0001999137266786531, 'epoch': 0.13}

  4%|▍         | 54/1248 [05:13<1:46:36,  5.36s/it]
  4%|▍         | 55/1248 [05:19<1:46:45,  5.37s/it]
                                                   
{'loss': 0.8508, 'learning_rate': 0.00019990260731385595, 'epoch': 0.13}

  4%|▍         | 55/1248 [05:19<1:46:45,  5.37s/it]
  4%|▍         | 56/1248 [05:24<1:48:19,  5.45s/it]
                                                   
{'loss': 0.8603, 'learning_rate': 0.00019989081449870778, 'epoch': 0.13}

  4%|▍         | 56/1248 [05:24<1:48:19,  5.45s/it]
  5%|▍         | 57/1248 [05:30<1:50:02,  5.54s/it]
                                                   
{'loss': 0.7928, 'learning_rate': 0.00019987834831270476, 'epoch': 0.14}

  5%|▍         | 57/1248 [05:30<1:50:02,  5.54s/it]
  5%|▍         | 58/1248 [05:36<1:51:57,  5.65s/it]
                                                   
{'loss': 0.7712, 'learning_rate': 0.00019986520883988232, 'epoch': 0.14}

  5%|▍         | 58/1248 [05:36<1:51:57,  5.65s/it]
  5%|▍         | 59/1248 [05:42<1:51:25,  5.62s/it]
                                                   
{'loss': 0.829, 'learning_rate': 0.00019985139616881452, 'epoch': 0.14}

  5%|▍         | 59/1248 [05:42<1:51:25,  5.62s/it]
  5%|▍         | 60/1248 [05:47<1:52:16,  5.67s/it]
                                                   
{'loss': 0.85, 'learning_rate': 0.00019983691039261357, 'epoch': 0.14}

  5%|▍         | 60/1248 [05:47<1:52:16,  5.67s/it]
  5%|▍         | 61/1248 [05:53<1:53:30,  5.74s/it]
                                                   
{'loss': 0.8154, 'learning_rate': 0.00019982175160892908, 'epoch': 0.15}

  5%|▍         | 61/1248 [05:53<1:53:30,  5.74s/it]
  5%|▍         | 62/1248 [05:59<1:54:11,  5.78s/it]
                                                   
{'loss': 0.7955, 'learning_rate': 0.0001998059199199474, 'epoch': 0.15}

  5%|▍         | 62/1248 [05:59<1:54:11,  5.78s/it]
  5%|▌         | 63/1248 [06:05<1:54:37,  5.80s/it]
                                                   
{'loss': 0.7638, 'learning_rate': 0.0001997894154323911, 'epoch': 0.15}

  5%|▌         | 63/1248 [06:05<1:54:37,  5.80s/it]
  5%|▌         | 64/1248 [06:10<1:51:36,  5.66s/it]
                                                   
{'loss': 0.8221, 'learning_rate': 0.00019977223825751801, 'epoch': 0.15}

  5%|▌         | 64/1248 [06:10<1:51:36,  5.66s/it]
  5%|▌         | 65/1248 [06:16<1:51:31,  5.66s/it]
                                                   
{'loss': 0.7743, 'learning_rate': 0.0001997543885111207, 'epoch': 0.16}

  5%|▌         | 65/1248 [06:16<1:51:31,  5.66s/it]
  5%|▌         | 66/1248 [06:21<1:50:59,  5.63s/it]
                                                   
{'loss': 0.7946, 'learning_rate': 0.0001997358663135255, 'epoch': 0.16}

  5%|▌         | 66/1248 [06:21<1:50:59,  5.63s/it]
  5%|▌         | 67/1248 [06:27<1:51:09,  5.65s/it]
                                                   
{'loss': 0.7576, 'learning_rate': 0.00019971667178959185, 'epoch': 0.16}

  5%|▌         | 67/1248 [06:27<1:51:09,  5.65s/it]
  5%|▌         | 68/1248 [06:33<1:50:33,  5.62s/it]
                                                   
{'loss': 0.7442, 'learning_rate': 0.00019969680506871137, 'epoch': 0.16}

  5%|▌         | 68/1248 [06:33<1:50:33,  5.62s/it]
  6%|▌         | 69/1248 [06:38<1:50:44,  5.64s/it]
                                                   
{'loss': 0.7467, 'learning_rate': 0.0001996762662848069, 'epoch': 0.17}

  6%|▌         | 69/1248 [06:38<1:50:44,  5.64s/it]
  6%|▌         | 70/1248 [06:44<1:49:54,  5.60s/it]
                                                   
{'loss': 0.8406, 'learning_rate': 0.0001996550555763319, 'epoch': 0.17}

  6%|▌         | 70/1248 [06:44<1:49:54,  5.60s/it]
  6%|▌         | 71/1248 [06:50<1:50:14,  5.62s/it]
                                                   
{'loss': 0.7749, 'learning_rate': 0.00019963317308626914, 'epoch': 0.17}

  6%|▌         | 71/1248 [06:50<1:50:14,  5.62s/it]
  6%|▌         | 72/1248 [06:55<1:50:51,  5.66s/it]
                                                   
{'loss': 0.7, 'learning_rate': 0.00019961061896213008, 'epoch': 0.17}

  6%|▌         | 72/1248 [06:55<1:50:51,  5.66s/it]
  6%|▌         | 73/1248 [07:01<1:51:05,  5.67s/it]
                                                   
{'loss': 0.7381, 'learning_rate': 0.0001995873933559535, 'epoch': 0.18}

  6%|▌         | 73/1248 [07:01<1:51:05,  5.67s/it]
  6%|▌         | 74/1248 [07:07<1:51:12,  5.68s/it]
                                                   
{'loss': 0.7584, 'learning_rate': 0.00019956349642430494, 'epoch': 0.18}

  6%|▌         | 74/1248 [07:07<1:51:12,  5.68s/it]
  6%|▌         | 75/1248 [07:13<1:54:18,  5.85s/it]
                                                   
{'loss': 0.7473, 'learning_rate': 0.00019953892832827517, 'epoch': 0.18}

  6%|▌         | 75/1248 [07:13<1:54:18,  5.85s/it]
  6%|▌         | 76/1248 [07:18<1:51:17,  5.70s/it]
                                                   
{'loss': 0.801, 'learning_rate': 0.00019951368923347944, 'epoch': 0.18}

  6%|▌         | 76/1248 [07:18<1:51:17,  5.70s/it]
  6%|▌         | 77/1248 [07:24<1:51:25,  5.71s/it]
                                                   
{'loss': 0.73, 'learning_rate': 0.00019948777931005625, 'epoch': 0.19}

  6%|▌         | 77/1248 [07:24<1:51:25,  5.71s/it]
  6%|▋         | 78/1248 [07:30<1:50:28,  5.67s/it]
                                                   
{'loss': 0.7128, 'learning_rate': 0.00019946119873266613, 'epoch': 0.19}

  6%|▋         | 78/1248 [07:30<1:50:28,  5.67s/it]
  6%|▋         | 79/1248 [07:35<1:46:32,  5.47s/it]
                                                   
{'loss': 0.7207, 'learning_rate': 0.00019943394768049064, 'epoch': 0.19}

  6%|▋         | 79/1248 [07:35<1:46:32,  5.47s/it]
  6%|▋         | 80/1248 [07:40<1:46:59,  5.50s/it]
                                                   
{'loss': 0.7371, 'learning_rate': 0.00019940602633723096, 'epoch': 0.19}

  6%|▋         | 80/1248 [07:40<1:46:59,  5.50s/it]
  6%|▋         | 81/1248 [07:46<1:47:21,  5.52s/it]
                                                   
{'loss': 0.7301, 'learning_rate': 0.00019937743489110678, 'epoch': 0.19}

  6%|▋         | 81/1248 [07:46<1:47:21,  5.52s/it]
  7%|▋         | 82/1248 [07:51<1:46:27,  5.48s/it]
                                                   
{'loss': 0.7081, 'learning_rate': 0.00019934817353485501, 'epoch': 0.2}

  7%|▋         | 82/1248 [07:51<1:46:27,  5.48s/it]
  7%|▋         | 83/1248 [07:56<1:44:25,  5.38s/it]
                                                   
{'loss': 0.7354, 'learning_rate': 0.0001993182424657285, 'epoch': 0.2}

  7%|▋         | 83/1248 [07:56<1:44:25,  5.38s/it]
  7%|▋         | 84/1248 [08:02<1:44:59,  5.41s/it]
                                                   
{'loss': 0.7922, 'learning_rate': 0.00019928764188549463, 'epoch': 0.2}

  7%|▋         | 84/1248 [08:02<1:44:59,  5.41s/it]
  7%|▋         | 85/1248 [08:07<1:43:22,  5.33s/it]
                                                   
{'loss': 0.701, 'learning_rate': 0.000199256372000434, 'epoch': 0.2}

  7%|▋         | 85/1248 [08:07<1:43:22,  5.33s/it]
  7%|▋         | 86/1248 [08:12<1:41:46,  5.25s/it]
                                                   
{'loss': 0.7067, 'learning_rate': 0.00019922443302133904, 'epoch': 0.21}

  7%|▋         | 86/1248 [08:12<1:41:46,  5.25s/it]
  7%|▋         | 87/1248 [08:18<1:45:24,  5.45s/it]
                                                   
{'loss': 0.736, 'learning_rate': 0.00019919182516351267, 'epoch': 0.21}

  7%|▋         | 87/1248 [08:18<1:45:24,  5.45s/it]
  7%|▋         | 88/1248 [08:23<1:43:38,  5.36s/it]
                                                   
{'loss': 0.7161, 'learning_rate': 0.00019915854864676664, 'epoch': 0.21}

  7%|▋         | 88/1248 [08:23<1:43:38,  5.36s/it]
  7%|▋         | 89/1248 [08:28<1:42:44,  5.32s/it]
                                                   
{'loss': 0.7383, 'learning_rate': 0.00019912460369542027, 'epoch': 0.21}

  7%|▋         | 89/1248 [08:28<1:42:44,  5.32s/it]
  7%|▋         | 90/1248 [08:33<1:42:09,  5.29s/it]
                                                   
{'loss': 0.6926, 'learning_rate': 0.00019908999053829882, 'epoch': 0.22}

  7%|▋         | 90/1248 [08:33<1:42:09,  5.29s/it]
  7%|▋         | 91/1248 [08:39<1:41:14,  5.25s/it]
                                                   
{'loss': 0.7282, 'learning_rate': 0.00019905470940873195, 'epoch': 0.22}

  7%|▋         | 91/1248 [08:39<1:41:14,  5.25s/it]
  7%|▋         | 92/1248 [08:44<1:41:09,  5.25s/it]
                                                   
{'loss': 0.6861, 'learning_rate': 0.00019901876054455217, 'epoch': 0.22}

  7%|▋         | 92/1248 [08:44<1:41:09,  5.25s/it]
  7%|▋         | 93/1248 [08:50<1:44:34,  5.43s/it]
                                                   
{'loss': 0.6626, 'learning_rate': 0.0001989821441880933, 'epoch': 0.22}

  7%|▋         | 93/1248 [08:50<1:44:34,  5.43s/it]
  8%|▊         | 94/1248 [08:56<1:47:06,  5.57s/it]
                                                   
{'loss': 0.7302, 'learning_rate': 0.00019894486058618865, 'epoch': 0.23}

  8%|▊         | 94/1248 [08:56<1:47:06,  5.57s/it]
  8%|▊         | 95/1248 [09:01<1:48:32,  5.65s/it]
                                                   
{'loss': 0.765, 'learning_rate': 0.00019890690999016956, 'epoch': 0.23}

  8%|▊         | 95/1248 [09:01<1:48:32,  5.65s/it]
  8%|▊         | 96/1248 [09:07<1:49:11,  5.69s/it]
                                                   
{'loss': 0.729, 'learning_rate': 0.00019886829265586368, 'epoch': 0.23}

  8%|▊         | 96/1248 [09:07<1:49:11,  5.69s/it]
  8%|▊         | 97/1248 [09:13<1:48:52,  5.68s/it]
                                                   
{'loss': 0.7716, 'learning_rate': 0.00019882900884359304, 'epoch': 0.23}

  8%|▊         | 97/1248 [09:13<1:48:52,  5.68s/it]
  8%|▊         | 98/1248 [09:18<1:47:50,  5.63s/it]
                                                   
{'loss': 0.7063, 'learning_rate': 0.00019878905881817252, 'epoch': 0.24}

  8%|▊         | 98/1248 [09:18<1:47:50,  5.63s/it]
  8%|▊         | 99/1248 [09:25<1:50:33,  5.77s/it]
                                                   
{'loss': 0.7282, 'learning_rate': 0.00019874844284890805, 'epoch': 0.24}

  8%|▊         | 99/1248 [09:25<1:50:33,  5.77s/it]
  8%|▊         | 100/1248 [09:30<1:48:18,  5.66s/it]
                                                    
{'loss': 0.7746, 'learning_rate': 0.00019870716120959462, 'epoch': 0.24}

  8%|▊         | 100/1248 [09:30<1:48:18,  5.66s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

  8%|▊         | 101/1248 [09:56<3:46:43, 11.86s/it]
                                                    
{'loss': 0.722, 'learning_rate': 0.00019866521417851463, 'epoch': 0.24}

  8%|▊         | 101/1248 [09:56<3:46:43, 11.86s/it]
  8%|▊         | 102/1248 [10:02<3:11:09, 10.01s/it]
                                                    
{'loss': 0.7508, 'learning_rate': 0.0001986226020384359, 'epoch': 0.25}

  8%|▊         | 102/1248 [10:02<3:11:09, 10.01s/it]
  8%|▊         | 103/1248 [10:08<2:48:01,  8.80s/it]
                                                    
{'loss': 0.7282, 'learning_rate': 0.0001985793250766098, 'epoch': 0.25}

  8%|▊         | 103/1248 [10:08<2:48:01,  8.80s/it]
  8%|▊         | 104/1248 [10:13<2:28:44,  7.80s/it]
                                                    
{'loss': 0.754, 'learning_rate': 0.00019853538358476932, 'epoch': 0.25}

  8%|▊         | 104/1248 [10:13<2:28:44,  7.80s/it]
  8%|▊         | 105/1248 [10:19<2:18:28,  7.27s/it]
                                                    
{'loss': 0.6912, 'learning_rate': 0.00019849077785912705, 'epoch': 0.25}

  8%|▊         | 105/1248 [10:19<2:18:28,  7.27s/it]
  8%|▊         | 106/1248 [10:25<2:11:15,  6.90s/it]
                                                    
{'loss': 0.7598, 'learning_rate': 0.00019844550820037325, 'epoch': 0.25}

  8%|▊         | 106/1248 [10:25<2:11:15,  6.90s/it]
  9%|▊         | 107/1248 [10:32<2:07:38,  6.71s/it]
                                                    
{'loss': 0.7161, 'learning_rate': 0.00019839957491367383, 'epoch': 0.26}

  9%|▊         | 107/1248 [10:32<2:07:38,  6.71s/it]
  9%|▊         | 108/1248 [10:38<2:03:12,  6.48s/it]
                                                    
{'loss': 0.7569, 'learning_rate': 0.00019835297830866826, 'epoch': 0.26}

  9%|▊         | 108/1248 [10:38<2:03:12,  6.48s/it]
  9%|▊         | 109/1248 [10:43<1:58:59,  6.27s/it]
                                                    
{'loss': 0.7001, 'learning_rate': 0.00019830571869946742, 'epoch': 0.26}

  9%|▊         | 109/1248 [10:43<1:58:59,  6.27s/it]
  9%|▉         | 110/1248 [10:50<2:00:29,  6.35s/it]
                                                    
{'loss': 0.6902, 'learning_rate': 0.00019825779640465156, 'epoch': 0.26}

  9%|▉         | 110/1248 [10:50<2:00:29,  6.35s/it]
  9%|▉         | 111/1248 [10:57<2:01:19,  6.40s/it]
                                                    
{'loss': 0.6979, 'learning_rate': 0.00019820921174726826, 'epoch': 0.27}

  9%|▉         | 111/1248 [10:57<2:01:19,  6.40s/it]
  9%|▉         | 112/1248 [11:02<1:58:36,  6.26s/it]
                                                    
{'loss': 0.6428, 'learning_rate': 0.00019815996505482996, 'epoch': 0.27}

  9%|▉         | 112/1248 [11:02<1:58:36,  6.26s/it]
  9%|▉         | 113/1248 [11:08<1:55:32,  6.11s/it]
                                                    
{'loss': 0.6981, 'learning_rate': 0.00019811005665931205, 'epoch': 0.27}

  9%|▉         | 113/1248 [11:08<1:55:32,  6.11s/it]
  9%|▉         | 114/1248 [11:14<1:53:29,  6.00s/it]
                                                    
{'loss': 0.6969, 'learning_rate': 0.00019805948689715041, 'epoch': 0.27}

  9%|▉         | 114/1248 [11:14<1:53:29,  6.00s/it]
  9%|▉         | 115/1248 [11:20<1:53:11,  5.99s/it]
                                                    
{'loss': 0.7052, 'learning_rate': 0.00019800825610923934, 'epoch': 0.28}

  9%|▉         | 115/1248 [11:20<1:53:11,  5.99s/it]
  9%|▉         | 116/1248 [11:26<1:51:43,  5.92s/it]
                                                    
{'loss': 0.6844, 'learning_rate': 0.00019795636464092908, 'epoch': 0.28}

  9%|▉         | 116/1248 [11:26<1:51:43,  5.92s/it]
  9%|▉         | 117/1248 [11:32<1:52:06,  5.95s/it]
                                                    
{'loss': 0.7008, 'learning_rate': 0.0001979038128420236, 'epoch': 0.28}

  9%|▉         | 117/1248 [11:32<1:52:06,  5.95s/it]
  9%|▉         | 118/1248 [11:38<1:51:57,  5.94s/it]
                                                    
{'loss': 0.6586, 'learning_rate': 0.00019785060106677818, 'epoch': 0.28}

  9%|▉         | 118/1248 [11:38<1:51:57,  5.94s/it]
 10%|▉         | 119/1248 [11:43<1:51:15,  5.91s/it]
                                                    
{'loss': 0.6986, 'learning_rate': 0.00019779672967389704, 'epoch': 0.29}

 10%|▉         | 119/1248 [11:43<1:51:15,  5.91s/it]
 10%|▉         | 120/1248 [11:49<1:51:17,  5.92s/it]
                                                    
{'loss': 0.6514, 'learning_rate': 0.000197742199026531, 'epoch': 0.29}

 10%|▉         | 120/1248 [11:49<1:51:17,  5.92s/it]
 10%|▉         | 121/1248 [11:56<1:52:37,  6.00s/it]
                                                    
{'loss': 0.672, 'learning_rate': 0.00019768700949227482, 'epoch': 0.29}

 10%|▉         | 121/1248 [11:56<1:52:37,  6.00s/it]
 10%|▉         | 122/1248 [12:02<1:55:06,  6.13s/it]
                                                    
{'loss': 0.691, 'learning_rate': 0.00019763116144316505, 'epoch': 0.29}

 10%|▉         | 122/1248 [12:02<1:55:06,  6.13s/it]
 10%|▉         | 123/1248 [12:08<1:53:26,  6.05s/it]
                                                    
{'loss': 0.6799, 'learning_rate': 0.0001975746552556772, 'epoch': 0.3}

 10%|▉         | 123/1248 [12:08<1:53:26,  6.05s/it]
 10%|▉         | 124/1248 [12:14<1:55:13,  6.15s/it]
                                                    
{'loss': 0.6411, 'learning_rate': 0.00019751749131072333, 'epoch': 0.3}

 10%|▉         | 124/1248 [12:14<1:55:13,  6.15s/it]
 10%|█         | 125/1248 [12:20<1:55:08,  6.15s/it]
                                                    
{'loss': 0.7052, 'learning_rate': 0.00019745966999364954, 'epoch': 0.3}

 10%|█         | 125/1248 [12:20<1:55:08,  6.15s/it]
 10%|█         | 126/1248 [12:26<1:54:02,  6.10s/it]
                                                    
{'loss': 0.7414, 'learning_rate': 0.00019740119169423337, 'epoch': 0.3}

 10%|█         | 126/1248 [12:26<1:54:02,  6.10s/it]
 10%|█         | 127/1248 [12:32<1:53:02,  6.05s/it]
                                                    
{'loss': 0.7074, 'learning_rate': 0.00019734205680668098, 'epoch': 0.31}

 10%|█         | 127/1248 [12:32<1:53:02,  6.05s/it]
 10%|█         | 128/1248 [12:38<1:51:25,  5.97s/it]
                                                    
{'loss': 0.7069, 'learning_rate': 0.00019728226572962473, 'epoch': 0.31}

 10%|█         | 128/1248 [12:38<1:51:25,  5.97s/it]
 10%|█         | 129/1248 [12:44<1:49:55,  5.89s/it]
                                                    
{'loss': 0.6821, 'learning_rate': 0.00019722181886612042, 'epoch': 0.31}

 10%|█         | 129/1248 [12:44<1:49:55,  5.89s/it]
 10%|█         | 130/1248 [12:50<1:51:33,  5.99s/it]
                                                    
{'loss': 0.7043, 'learning_rate': 0.00019716071662364453, 'epoch': 0.31}

 10%|█         | 130/1248 [12:50<1:51:33,  5.99s/it]
 10%|█         | 131/1248 [12:56<1:49:52,  5.90s/it]
                                                    
{'loss': 0.7513, 'learning_rate': 0.0001970989594140914, 'epoch': 0.31}

 10%|█         | 131/1248 [12:56<1:49:52,  5.90s/it]
 11%|█         | 132/1248 [13:01<1:46:54,  5.75s/it]
                                                    
{'loss': 0.6533, 'learning_rate': 0.0001970365476537707, 'epoch': 0.32}

 11%|█         | 132/1248 [13:01<1:46:54,  5.75s/it]
 11%|█         | 133/1248 [13:07<1:46:30,  5.73s/it]
                                                    
{'loss': 0.7055, 'learning_rate': 0.0001969734817634044, 'epoch': 0.32}

 11%|█         | 133/1248 [13:07<1:46:30,  5.73s/it]
 11%|█         | 134/1248 [13:12<1:45:03,  5.66s/it]
                                                    
{'loss': 0.6912, 'learning_rate': 0.00019690976216812396, 'epoch': 0.32}

 11%|█         | 134/1248 [13:12<1:45:03,  5.66s/it]
 11%|█         | 135/1248 [13:18<1:45:17,  5.68s/it]
                                                    
{'loss': 0.637, 'learning_rate': 0.0001968453892974676, 'epoch': 0.32}

 11%|█         | 135/1248 [13:18<1:45:17,  5.68s/it]
 11%|█         | 136/1248 [13:24<1:44:33,  5.64s/it]
                                                    
{'loss': 0.6661, 'learning_rate': 0.00019678036358537724, 'epoch': 0.33}

 11%|█         | 136/1248 [13:24<1:44:33,  5.64s/it]
 11%|█         | 137/1248 [13:29<1:45:07,  5.68s/it]
                                                    
{'loss': 0.7009, 'learning_rate': 0.00019671468547019573, 'epoch': 0.33}

 11%|█         | 137/1248 [13:29<1:45:07,  5.68s/it]
 11%|█         | 138/1248 [13:35<1:43:50,  5.61s/it]
                                                    
{'loss': 0.6482, 'learning_rate': 0.0001966483553946637, 'epoch': 0.33}

 11%|█         | 138/1248 [13:35<1:43:50,  5.61s/it]
 11%|█         | 139/1248 [13:40<1:41:13,  5.48s/it]
                                                    
{'loss': 0.6663, 'learning_rate': 0.00019658137380591678, 'epoch': 0.33}

 11%|█         | 139/1248 [13:40<1:41:13,  5.48s/it]
 11%|█         | 140/1248 [13:46<1:44:08,  5.64s/it]
                                                    
{'loss': 0.7226, 'learning_rate': 0.00019651374115548252, 'epoch': 0.34}

 11%|█         | 140/1248 [13:46<1:44:08,  5.64s/it]
 11%|█▏        | 141/1248 [13:57<2:11:56,  7.15s/it]
                                                    
{'loss': 0.8458, 'learning_rate': 0.0001964454578992772, 'epoch': 0.34}

 11%|█▏        | 141/1248 [13:57<2:11:56,  7.15s/it]
 11%|█▏        | 142/1248 [14:02<2:04:10,  6.74s/it]
                                                    
{'loss': 0.6698, 'learning_rate': 0.000196376524497603, 'epoch': 0.34}

 11%|█▏        | 142/1248 [14:02<2:04:10,  6.74s/it]
 11%|█▏        | 143/1248 [14:08<1:56:50,  6.34s/it]
                                                    
{'loss': 0.6519, 'learning_rate': 0.00019630694141514464, 'epoch': 0.34}

 11%|█▏        | 143/1248 [14:08<1:56:50,  6.34s/it]
 12%|█▏        | 144/1248 [14:14<1:53:03,  6.14s/it]
                                                    
{'loss': 0.6564, 'learning_rate': 0.00019623670912096656, 'epoch': 0.35}

 12%|█▏        | 144/1248 [14:14<1:53:03,  6.14s/it]
 12%|█▏        | 145/1248 [14:19<1:50:12,  5.99s/it]
                                                    
{'loss': 0.6432, 'learning_rate': 0.00019616582808850946, 'epoch': 0.35}

 12%|█▏        | 145/1248 [14:19<1:50:12,  5.99s/it]
 12%|█▏        | 146/1248 [14:25<1:49:28,  5.96s/it]
                                                    
{'loss': 0.6811, 'learning_rate': 0.00019609429879558724, 'epoch': 0.35}

 12%|█▏        | 146/1248 [14:25<1:49:28,  5.96s/it]
 12%|█▏        | 147/1248 [14:31<1:47:01,  5.83s/it]
                                                    
{'loss': 0.6578, 'learning_rate': 0.0001960221217243838, 'epoch': 0.35}

 12%|█▏        | 147/1248 [14:31<1:47:01,  5.83s/it]
 12%|█▏        | 148/1248 [14:37<1:49:41,  5.98s/it]
                                                    
{'loss': 0.7398, 'learning_rate': 0.00019594929736144976, 'epoch': 0.36}

 12%|█▏        | 148/1248 [14:37<1:49:41,  5.98s/it]
 12%|█▏        | 149/1248 [14:42<1:46:13,  5.80s/it]
                                                    
{'loss': 0.7004, 'learning_rate': 0.00019587582619769913, 'epoch': 0.36}

 12%|█▏        | 149/1248 [14:42<1:46:13,  5.80s/it]
 12%|█▏        | 150/1248 [14:48<1:46:22,  5.81s/it]
                                                    
{'loss': 0.6923, 'learning_rate': 0.00019580170872840607, 'epoch': 0.36}

 12%|█▏        | 150/1248 [14:48<1:46:22,  5.81s/it]
 12%|█▏        | 151/1248 [14:54<1:47:40,  5.89s/it]
                                                    
{'loss': 0.6435, 'learning_rate': 0.00019572694545320164, 'epoch': 0.36}

 12%|█▏        | 151/1248 [14:54<1:47:40,  5.89s/it]
 12%|█▏        | 152/1248 [15:00<1:47:44,  5.90s/it]
                                                    
{'loss': 0.6297, 'learning_rate': 0.00019565153687607008, 'epoch': 0.37}

 12%|█▏        | 152/1248 [15:00<1:47:44,  5.90s/it]
 12%|█▏        | 153/1248 [15:06<1:47:46,  5.91s/it]
                                                    
{'loss': 0.7253, 'learning_rate': 0.0001955754835053459, 'epoch': 0.37}

 12%|█▏        | 153/1248 [15:06<1:47:46,  5.91s/it]
 12%|█▏        | 154/1248 [15:12<1:47:44,  5.91s/it]
                                                    
{'loss': 0.6857, 'learning_rate': 0.00019549878585371007, 'epoch': 0.37}

 12%|█▏        | 154/1248 [15:12<1:47:44,  5.91s/it]
 12%|█▏        | 155/1248 [15:18<1:47:01,  5.87s/it]
                                                    
{'loss': 0.6384, 'learning_rate': 0.00019542144443818673, 'epoch': 0.37}

 12%|█▏        | 155/1248 [15:18<1:47:01,  5.87s/it]
 12%|█▎        | 156/1248 [15:24<1:47:16,  5.89s/it]
                                                    
{'loss': 0.6735, 'learning_rate': 0.0001953434597801397, 'epoch': 0.38}

 12%|█▎        | 156/1248 [15:24<1:47:16,  5.89s/it]
 13%|█▎        | 157/1248 [15:30<1:47:15,  5.90s/it]
                                                    
{'loss': 0.6528, 'learning_rate': 0.00019526483240526893, 'epoch': 0.38}

 13%|█▎        | 157/1248 [15:30<1:47:15,  5.90s/it]
 13%|█▎        | 158/1248 [15:35<1:46:32,  5.86s/it]
                                                    
{'loss': 0.6516, 'learning_rate': 0.00019518556284360696, 'epoch': 0.38}

 13%|█▎        | 158/1248 [15:35<1:46:32,  5.86s/it]
 13%|█▎        | 159/1248 [15:41<1:46:33,  5.87s/it]
                                                    
{'loss': 0.6488, 'learning_rate': 0.00019510565162951537, 'epoch': 0.38}

 13%|█▎        | 159/1248 [15:41<1:46:33,  5.87s/it]
 13%|█▎        | 160/1248 [15:47<1:46:17,  5.86s/it]
                                                    
{'loss': 0.6339, 'learning_rate': 0.00019502509930168112, 'epoch': 0.38}

 13%|█▎        | 160/1248 [15:47<1:46:17,  5.86s/it]
 13%|█▎        | 161/1248 [15:53<1:44:22,  5.76s/it]
                                                    
{'loss': 0.6447, 'learning_rate': 0.00019494390640311301, 'epoch': 0.39}

 13%|█▎        | 161/1248 [15:53<1:44:22,  5.76s/it]
 13%|█▎        | 162/1248 [15:58<1:44:10,  5.76s/it]
                                                    
{'loss': 0.6976, 'learning_rate': 0.000194862073481138, 'epoch': 0.39}

 13%|█▎        | 162/1248 [15:58<1:44:10,  5.76s/it]
 13%|█▎        | 163/1248 [16:04<1:42:00,  5.64s/it]
                                                    
{'loss': 0.6384, 'learning_rate': 0.0001947796010873974, 'epoch': 0.39}

 13%|█▎        | 163/1248 [16:04<1:42:00,  5.64s/it]
 13%|█▎        | 164/1248 [16:10<1:43:19,  5.72s/it]
                                                    
{'loss': 0.5805, 'learning_rate': 0.0001946964897778433, 'epoch': 0.39}

 13%|█▎        | 164/1248 [16:10<1:43:19,  5.72s/it]
 13%|█▎        | 165/1248 [16:15<1:42:51,  5.70s/it]
                                                    
{'loss': 0.6157, 'learning_rate': 0.00019461274011273476, 'epoch': 0.4}

 13%|█▎        | 165/1248 [16:15<1:42:51,  5.70s/it]
 13%|█▎        | 166/1248 [16:21<1:42:08,  5.66s/it]
                                                    
{'loss': 0.6702, 'learning_rate': 0.00019452835265663403, 'epoch': 0.4}

 13%|█▎        | 166/1248 [16:21<1:42:08,  5.66s/it]
 13%|█▎        | 167/1248 [16:27<1:42:18,  5.68s/it]
                                                    
{'loss': 0.6747, 'learning_rate': 0.0001944433279784028, 'epoch': 0.4}

 13%|█▎        | 167/1248 [16:27<1:42:18,  5.68s/it]
 13%|█▎        | 168/1248 [16:32<1:42:54,  5.72s/it]
                                                    
{'loss': 0.6381, 'learning_rate': 0.0001943576666511982, 'epoch': 0.4}

 13%|█▎        | 168/1248 [16:32<1:42:54,  5.72s/it]
 14%|█▎        | 169/1248 [16:38<1:42:11,  5.68s/it]
                                                    
{'loss': 0.6793, 'learning_rate': 0.00019427136925246922, 'epoch': 0.41}

 14%|█▎        | 169/1248 [16:38<1:42:11,  5.68s/it]
 14%|█▎        | 170/1248 [16:44<1:41:43,  5.66s/it]
                                                    
{'loss': 0.6779, 'learning_rate': 0.00019418443636395248, 'epoch': 0.41}

 14%|█▎        | 170/1248 [16:44<1:41:43,  5.66s/it]
 14%|█▎        | 171/1248 [16:49<1:42:31,  5.71s/it]
                                                    
{'loss': 0.6705, 'learning_rate': 0.00019409686857166863, 'epoch': 0.41}

 14%|█▎        | 171/1248 [16:49<1:42:31,  5.71s/it]
 14%|█▍        | 172/1248 [16:55<1:41:35,  5.66s/it]
                                                    
{'loss': 0.736, 'learning_rate': 0.00019400866646591814, 'epoch': 0.41}

 14%|█▍        | 172/1248 [16:55<1:41:35,  5.66s/it]
 14%|█▍        | 173/1248 [17:01<1:41:48,  5.68s/it]
                                                    
{'loss': 0.6656, 'learning_rate': 0.0001939198306412775, 'epoch': 0.42}

 14%|█▍        | 173/1248 [17:01<1:41:48,  5.68s/it]
 14%|█▍        | 174/1248 [17:06<1:41:01,  5.64s/it]
                                                    
{'loss': 0.6628, 'learning_rate': 0.00019383036169659513, 'epoch': 0.42}

 14%|█▍        | 174/1248 [17:06<1:41:01,  5.64s/it]
 14%|█▍        | 175/1248 [17:12<1:39:13,  5.55s/it]
                                                    
{'loss': 0.6332, 'learning_rate': 0.00019374026023498728, 'epoch': 0.42}

 14%|█▍        | 175/1248 [17:12<1:39:13,  5.55s/it]
 14%|█▍        | 176/1248 [17:17<1:38:31,  5.51s/it]
                                                    
{'loss': 0.6607, 'learning_rate': 0.00019364952686383417, 'epoch': 0.42}

 14%|█▍        | 176/1248 [17:17<1:38:31,  5.51s/it]
 14%|█▍        | 177/1248 [17:22<1:37:06,  5.44s/it]
                                                    
{'loss': 0.616, 'learning_rate': 0.00019355816219477568, 'epoch': 0.43}

 14%|█▍        | 177/1248 [17:22<1:37:06,  5.44s/it]
 14%|█▍        | 178/1248 [17:28<1:37:07,  5.45s/it]
                                                    
{'loss': 0.6634, 'learning_rate': 0.0001934661668437073, 'epoch': 0.43}

 14%|█▍        | 178/1248 [17:28<1:37:07,  5.45s/it]
 14%|█▍        | 179/1248 [17:33<1:36:34,  5.42s/it]
                                                    
{'loss': 0.6237, 'learning_rate': 0.0001933735414307761, 'epoch': 0.43}

 14%|█▍        | 179/1248 [17:33<1:36:34,  5.42s/it]
 14%|█▍        | 180/1248 [17:38<1:36:00,  5.39s/it]
                                                    
{'loss': 0.6267, 'learning_rate': 0.00019328028658037626, 'epoch': 0.43}

 14%|█▍        | 180/1248 [17:39<1:36:00,  5.39s/it]
 15%|█▍        | 181/1248 [17:44<1:36:10,  5.41s/it]
                                                    
{'loss': 0.6642, 'learning_rate': 0.00019318640292114524, 'epoch': 0.44}

 15%|█▍        | 181/1248 [17:44<1:36:10,  5.41s/it]
 15%|█▍        | 182/1248 [17:49<1:34:34,  5.32s/it]
                                                    
{'loss': 0.662, 'learning_rate': 0.0001930918910859592, 'epoch': 0.44}

 15%|█▍        | 182/1248 [17:49<1:34:34,  5.32s/it]
 15%|█▍        | 183/1248 [17:54<1:33:25,  5.26s/it]
                                                    
{'loss': 0.6156, 'learning_rate': 0.0001929967517119289, 'epoch': 0.44}

 15%|█▍        | 183/1248 [17:54<1:33:25,  5.26s/it]
 15%|█▍        | 184/1248 [18:00<1:36:57,  5.47s/it]
                                                    
{'loss': 0.6892, 'learning_rate': 0.00019290098544039546, 'epoch': 0.44}

 15%|█▍        | 184/1248 [18:00<1:36:57,  5.47s/it]
 15%|█▍        | 185/1248 [18:06<1:36:56,  5.47s/it]
                                                    
{'loss': 0.6224, 'learning_rate': 0.00019280459291692588, 'epoch': 0.44}

 15%|█▍        | 185/1248 [18:06<1:36:56,  5.47s/it]
 15%|█▍        | 186/1248 [18:11<1:37:15,  5.50s/it]
                                                    
{'loss': 0.6241, 'learning_rate': 0.00019270757479130878, 'epoch': 0.45}

 15%|█▍        | 186/1248 [18:11<1:37:15,  5.50s/it]
 15%|█▍        | 187/1248 [18:17<1:39:04,  5.60s/it]
                                                    
{'loss': 0.604, 'learning_rate': 0.0001926099317175501, 'epoch': 0.45}

 15%|█▍        | 187/1248 [18:17<1:39:04,  5.60s/it]
 15%|█▌        | 188/1248 [18:23<1:39:51,  5.65s/it]
                                                    
{'loss': 0.6243, 'learning_rate': 0.0001925116643538684, 'epoch': 0.45}

 15%|█▌        | 188/1248 [18:23<1:39:51,  5.65s/it]
 15%|█▌        | 189/1248 [18:28<1:39:50,  5.66s/it]
                                                    
{'loss': 0.6448, 'learning_rate': 0.00019241277336269082, 'epoch': 0.45}

 15%|█▌        | 189/1248 [18:28<1:39:50,  5.66s/it]
 15%|█▌        | 190/1248 [18:35<1:43:05,  5.85s/it]
                                                    
{'loss': 0.6651, 'learning_rate': 0.00019231325941064832, 'epoch': 0.46}

 15%|█▌        | 190/1248 [18:35<1:43:05,  5.85s/it]
 15%|█▌        | 191/1248 [18:41<1:44:10,  5.91s/it]
                                                    
{'loss': 0.6158, 'learning_rate': 0.0001922131231685713, 'epoch': 0.46}

 15%|█▌        | 191/1248 [18:41<1:44:10,  5.91s/it]
 15%|█▌        | 192/1248 [18:47<1:43:15,  5.87s/it]
                                                    
{'loss': 0.6236, 'learning_rate': 0.000192112365311485, 'epoch': 0.46}

 15%|█▌        | 192/1248 [18:47<1:43:15,  5.87s/it]
 15%|█▌        | 193/1248 [18:53<1:43:37,  5.89s/it]
                                                    
{'loss': 0.6389, 'learning_rate': 0.0001920109865186052, 'epoch': 0.46}

 15%|█▌        | 193/1248 [18:53<1:43:37,  5.89s/it]
 16%|█▌        | 194/1248 [18:58<1:43:33,  5.89s/it]
                                                    
{'loss': 0.6291, 'learning_rate': 0.0001919089874733332, 'epoch': 0.47}

 16%|█▌        | 194/1248 [18:58<1:43:33,  5.89s/it]
 16%|█▌        | 195/1248 [19:05<1:44:50,  5.97s/it]
                                                    
{'loss': 0.6564, 'learning_rate': 0.00019180636886325164, 'epoch': 0.47}

 16%|█▌        | 195/1248 [19:05<1:44:50,  5.97s/it]
 16%|█▌        | 196/1248 [19:10<1:44:21,  5.95s/it]
                                                    
{'loss': 0.6772, 'learning_rate': 0.00019170313138011964, 'epoch': 0.47}

 16%|█▌        | 196/1248 [19:10<1:44:21,  5.95s/it]
 16%|█▌        | 197/1248 [19:16<1:40:54,  5.76s/it]
                                                    
{'loss': 0.6291, 'learning_rate': 0.00019159927571986814, 'epoch': 0.47}

 16%|█▌        | 197/1248 [19:16<1:40:54,  5.76s/it]
 16%|█▌        | 198/1248 [19:22<1:41:31,  5.80s/it]
                                                    
{'loss': 0.8499, 'learning_rate': 0.00019149480258259533, 'epoch': 0.48}

 16%|█▌        | 198/1248 [19:22<1:41:31,  5.80s/it]
 16%|█▌        | 199/1248 [19:27<1:40:41,  5.76s/it]
                                                    
{'loss': 0.6309, 'learning_rate': 0.00019138971267256179, 'epoch': 0.48}

 16%|█▌        | 199/1248 [19:27<1:40:41,  5.76s/it]
 16%|█▌        | 200/1248 [19:33<1:41:43,  5.82s/it]
                                                    
{'loss': 0.5963, 'learning_rate': 0.00019128400669818585, 'epoch': 0.48}

 16%|█▌        | 200/1248 [19:33<1:41:43,  5.82s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 16%|█▌        | 201/1248 [19:59<3:27:52, 11.91s/it]
                                                    
{'loss': 0.6327, 'learning_rate': 0.00019117768537203872, 'epoch': 0.48}

 16%|█▌        | 201/1248 [19:59<3:27:52, 11.91s/it]
 16%|█▌        | 202/1248 [20:05<2:55:17, 10.05s/it]
                                                    
{'loss': 0.6251, 'learning_rate': 0.00019107074941083983, 'epoch': 0.49}

 16%|█▌        | 202/1248 [20:05<2:55:17, 10.05s/it]
 16%|█▋        | 203/1248 [20:11<2:32:57,  8.78s/it]
                                                    
{'loss': 0.6444, 'learning_rate': 0.00019096319953545185, 'epoch': 0.49}

 16%|█▋        | 203/1248 [20:11<2:32:57,  8.78s/it]
 16%|█▋        | 204/1248 [20:17<2:18:39,  7.97s/it]
                                                    
{'loss': 0.625, 'learning_rate': 0.00019085503647087585, 'epoch': 0.49}

 16%|█▋        | 204/1248 [20:17<2:18:39,  7.97s/it]
 16%|█▋        | 205/1248 [20:23<2:09:19,  7.44s/it]
                                                    
{'loss': 0.7266, 'learning_rate': 0.00019074626094624654, 'epoch': 0.49}

 16%|█▋        | 205/1248 [20:23<2:09:19,  7.44s/it]
 17%|█▋        | 206/1248 [20:30<2:04:27,  7.17s/it]
                                                    
{'loss': 0.849, 'learning_rate': 0.0001906368736948272, 'epoch': 0.5}

 17%|█▋        | 206/1248 [20:30<2:04:27,  7.17s/it]
 17%|█▋        | 207/1248 [20:36<1:57:47,  6.79s/it]
                                                    
{'loss': 0.6445, 'learning_rate': 0.00019052687545400478, 'epoch': 0.5}

 17%|█▋        | 207/1248 [20:36<1:57:47,  6.79s/it]
 17%|█▋        | 208/1248 [20:42<1:53:35,  6.55s/it]
                                                    
{'loss': 0.6529, 'learning_rate': 0.00019041626696528503, 'epoch': 0.5}

 17%|█▋        | 208/1248 [20:42<1:53:35,  6.55s/it]
 17%|█▋        | 209/1248 [20:48<1:50:46,  6.40s/it]
                                                    
{'loss': 0.5926, 'learning_rate': 0.00019030504897428737, 'epoch': 0.5}

 17%|█▋        | 209/1248 [20:48<1:50:46,  6.40s/it]
 17%|█▋        | 210/1248 [20:53<1:46:32,  6.16s/it]
                                                    
{'loss': 0.6267, 'learning_rate': 0.00019019322223073995, 'epoch': 0.5}

 17%|█▋        | 210/1248 [20:53<1:46:32,  6.16s/it]
 17%|█▋        | 211/1248 [21:00<1:48:03,  6.25s/it]
                                                    
{'loss': 0.6003, 'learning_rate': 0.00019008078748847457, 'epoch': 0.51}

 17%|█▋        | 211/1248 [21:00<1:48:03,  6.25s/it]
 17%|█▋        | 212/1248 [21:06<1:48:43,  6.30s/it]
                                                    
{'loss': 0.6566, 'learning_rate': 0.00018996774550542148, 'epoch': 0.51}

 17%|█▋        | 212/1248 [21:06<1:48:43,  6.30s/it]
 17%|█▋        | 213/1248 [21:12<1:46:38,  6.18s/it]
                                                    
{'loss': 0.6589, 'learning_rate': 0.00018985409704360456, 'epoch': 0.51}

 17%|█▋        | 213/1248 [21:12<1:46:38,  6.18s/it]
 17%|█▋        | 214/1248 [21:18<1:42:32,  5.95s/it]
                                                    
{'loss': 0.6236, 'learning_rate': 0.00018973984286913584, 'epoch': 0.51}

 17%|█▋        | 214/1248 [21:18<1:42:32,  5.95s/it]
 17%|█▋        | 215/1248 [21:23<1:42:15,  5.94s/it]
                                                    
{'loss': 0.5786, 'learning_rate': 0.0001896249837522106, 'epoch': 0.52}

 17%|█▋        | 215/1248 [21:23<1:42:15,  5.94s/it]
 17%|█▋        | 216/1248 [21:30<1:45:57,  6.16s/it]
                                                    
{'loss': 0.6331, 'learning_rate': 0.00018950952046710207, 'epoch': 0.52}

 17%|█▋        | 216/1248 [21:30<1:45:57,  6.16s/it]
 17%|█▋        | 217/1248 [21:36<1:44:14,  6.07s/it]
                                                    
{'loss': 0.6194, 'learning_rate': 0.0001893934537921562, 'epoch': 0.52}

 17%|█▋        | 217/1248 [21:36<1:44:14,  6.07s/it]
 17%|█▋        | 218/1248 [21:42<1:43:45,  6.04s/it]
                                                    
{'loss': 0.6423, 'learning_rate': 0.0001892767845097864, 'epoch': 0.52}

 17%|█▋        | 218/1248 [21:42<1:43:45,  6.04s/it]
 18%|█▊        | 219/1248 [21:48<1:41:09,  5.90s/it]
                                                    
{'loss': 0.6159, 'learning_rate': 0.00018915951340646832, 'epoch': 0.53}

 18%|█▊        | 219/1248 [21:48<1:41:09,  5.90s/it]
 18%|█▊        | 220/1248 [21:53<1:39:47,  5.82s/it]
                                                    
{'loss': 0.6211, 'learning_rate': 0.00018904164127273458, 'epoch': 0.53}

 18%|█▊        | 220/1248 [21:53<1:39:47,  5.82s/it]
 18%|█▊        | 221/1248 [21:59<1:38:59,  5.78s/it]
                                                    
{'loss': 0.6812, 'learning_rate': 0.00018892316890316936, 'epoch': 0.53}

 18%|█▊        | 221/1248 [21:59<1:38:59,  5.78s/it]
 18%|█▊        | 222/1248 [22:04<1:38:04,  5.74s/it]
                                                    
{'loss': 0.6746, 'learning_rate': 0.00018880409709640298, 'epoch': 0.53}

 18%|█▊        | 222/1248 [22:04<1:38:04,  5.74s/it]
 18%|█▊        | 223/1248 [22:10<1:39:05,  5.80s/it]
                                                    
{'loss': 0.6726, 'learning_rate': 0.00018868442665510678, 'epoch': 0.54}

 18%|█▊        | 223/1248 [22:10<1:39:05,  5.80s/it]
 18%|█▊        | 224/1248 [22:16<1:40:20,  5.88s/it]
                                                    
{'loss': 0.6365, 'learning_rate': 0.00018856415838598736, 'epoch': 0.54}

 18%|█▊        | 224/1248 [22:16<1:40:20,  5.88s/it]
 18%|█▊        | 225/1248 [22:22<1:39:02,  5.81s/it]
                                                    
{'loss': 0.6251, 'learning_rate': 0.00018844329309978145, 'epoch': 0.54}

 18%|█▊        | 225/1248 [22:22<1:39:02,  5.81s/it]
 18%|█▊        | 226/1248 [22:28<1:38:20,  5.77s/it]
                                                    
{'loss': 0.6039, 'learning_rate': 0.00018832183161125024, 'epoch': 0.54}

 18%|█▊        | 226/1248 [22:28<1:38:20,  5.77s/it]
 18%|█▊        | 227/1248 [22:34<1:38:00,  5.76s/it]
                                                    
{'loss': 0.582, 'learning_rate': 0.000188199774739174, 'epoch': 0.55}

 18%|█▊        | 227/1248 [22:34<1:38:00,  5.76s/it]
 18%|█▊        | 228/1248 [22:39<1:38:02,  5.77s/it]
                                                    
{'loss': 0.6175, 'learning_rate': 0.00018807712330634642, 'epoch': 0.55}

 18%|█▊        | 228/1248 [22:39<1:38:02,  5.77s/it]
 18%|█▊        | 229/1248 [22:45<1:38:29,  5.80s/it]
                                                    
{'loss': 0.6509, 'learning_rate': 0.00018795387813956937, 'epoch': 0.55}

 18%|█▊        | 229/1248 [22:45<1:38:29,  5.80s/it]
 18%|█▊        | 230/1248 [22:51<1:38:42,  5.82s/it]
                                                    
{'loss': 0.6491, 'learning_rate': 0.00018783004006964698, 'epoch': 0.55}

 18%|█▊        | 230/1248 [22:51<1:38:42,  5.82s/it]
 19%|█▊        | 231/1248 [22:57<1:39:16,  5.86s/it]
                                                    
{'loss': 0.6382, 'learning_rate': 0.00018770560993138012, 'epoch': 0.56}

 19%|█▊        | 231/1248 [22:57<1:39:16,  5.86s/it]
 19%|█▊        | 232/1248 [23:03<1:39:04,  5.85s/it]
                                                    
{'loss': 0.6335, 'learning_rate': 0.000187580588563561, 'epoch': 0.56}

 19%|█▊        | 232/1248 [23:03<1:39:04,  5.85s/it]
 19%|█▊        | 233/1248 [23:09<1:38:51,  5.84s/it]
                                                    
{'loss': 0.6194, 'learning_rate': 0.00018745497680896722, 'epoch': 0.56}

 19%|█▊        | 233/1248 [23:09<1:38:51,  5.84s/it]
 19%|█▉        | 234/1248 [23:15<1:38:43,  5.84s/it]
                                                    
{'loss': 0.6474, 'learning_rate': 0.00018732877551435627, 'epoch': 0.56}

 19%|█▉        | 234/1248 [23:15<1:38:43,  5.84s/it]
 19%|█▉        | 235/1248 [23:20<1:38:17,  5.82s/it]
                                                    
{'loss': 0.6281, 'learning_rate': 0.00018720198553045977, 'epoch': 0.56}

 19%|█▉        | 235/1248 [23:20<1:38:17,  5.82s/it]
 19%|█▉        | 236/1248 [23:26<1:38:23,  5.83s/it]
                                                    
{'loss': 0.6282, 'learning_rate': 0.00018707460771197774, 'epoch': 0.57}

 19%|█▉        | 236/1248 [23:26<1:38:23,  5.83s/it]
 19%|█▉        | 237/1248 [23:32<1:37:24,  5.78s/it]
                                                    
{'loss': 0.6057, 'learning_rate': 0.00018694664291757276, 'epoch': 0.57}

 19%|█▉        | 237/1248 [23:32<1:37:24,  5.78s/it]
 19%|█▉        | 238/1248 [23:37<1:35:54,  5.70s/it]
                                                    
{'loss': 0.6164, 'learning_rate': 0.0001868180920098644, 'epoch': 0.57}

 19%|█▉        | 238/1248 [23:37<1:35:54,  5.70s/it]
 19%|█▉        | 239/1248 [23:44<1:40:12,  5.96s/it]
                                                    
{'loss': 0.8259, 'learning_rate': 0.0001866889558554231, 'epoch': 0.57}

 19%|█▉        | 239/1248 [23:44<1:40:12,  5.96s/it]
 19%|█▉        | 240/1248 [23:50<1:38:41,  5.87s/it]
                                                    
{'loss': 0.6116, 'learning_rate': 0.00018655923532476463, 'epoch': 0.58}

 19%|█▉        | 240/1248 [23:50<1:38:41,  5.87s/it]
 19%|█▉        | 241/1248 [23:55<1:36:27,  5.75s/it]
                                                    
{'loss': 0.6605, 'learning_rate': 0.00018642893129234395, 'epoch': 0.58}

 19%|█▉        | 241/1248 [23:55<1:36:27,  5.75s/it]
 19%|█▉        | 242/1248 [24:01<1:35:38,  5.70s/it]
                                                    
{'loss': 0.5657, 'learning_rate': 0.00018629804463654955, 'epoch': 0.58}

 19%|█▉        | 242/1248 [24:01<1:35:38,  5.70s/it]
 19%|█▉        | 243/1248 [24:06<1:34:23,  5.64s/it]
                                                    
{'loss': 0.6318, 'learning_rate': 0.0001861665762396974, 'epoch': 0.58}

 19%|█▉        | 243/1248 [24:06<1:34:23,  5.64s/it]
 20%|█▉        | 244/1248 [24:12<1:34:14,  5.63s/it]
                                                    
{'loss': 0.5924, 'learning_rate': 0.00018603452698802498, 'epoch': 0.59}

 20%|█▉        | 244/1248 [24:12<1:34:14,  5.63s/it]
 20%|█▉        | 245/1248 [24:18<1:36:21,  5.76s/it]
                                                    
{'loss': 0.6241, 'learning_rate': 0.00018590189777168537, 'epoch': 0.59}

 20%|█▉        | 245/1248 [24:18<1:36:21,  5.76s/it]
 20%|█▉        | 246/1248 [24:23<1:33:11,  5.58s/it]
                                                    
{'loss': 0.6331, 'learning_rate': 0.00018576868948474127, 'epoch': 0.59}

 20%|█▉        | 246/1248 [24:23<1:33:11,  5.58s/it]
 20%|█▉        | 247/1248 [24:29<1:34:57,  5.69s/it]
                                                    
{'loss': 0.6414, 'learning_rate': 0.0001856349030251589, 'epoch': 0.59}

 20%|█▉        | 247/1248 [24:29<1:34:57,  5.69s/it]
 20%|█▉        | 248/1248 [24:34<1:34:03,  5.64s/it]
                                                    
{'loss': 0.5914, 'learning_rate': 0.00018550053929480202, 'epoch': 0.6}

 20%|█▉        | 248/1248 [24:34<1:34:03,  5.64s/it]
 20%|█▉        | 249/1248 [24:40<1:35:09,  5.72s/it]
                                                    
{'loss': 0.5968, 'learning_rate': 0.00018536559919942573, 'epoch': 0.6}

 20%|█▉        | 249/1248 [24:40<1:35:09,  5.72s/it]
 20%|██        | 250/1248 [24:46<1:36:03,  5.77s/it]
                                                    
{'loss': 0.6518, 'learning_rate': 0.00018523008364867055, 'epoch': 0.6}

 20%|██        | 250/1248 [24:46<1:36:03,  5.77s/it]
 20%|██        | 251/1248 [24:52<1:36:39,  5.82s/it]
                                                    
{'loss': 0.5975, 'learning_rate': 0.00018509399355605606, 'epoch': 0.6}

 20%|██        | 251/1248 [24:52<1:36:39,  5.82s/it]
 20%|██        | 252/1248 [24:58<1:34:41,  5.70s/it]
                                                    
{'loss': 0.6, 'learning_rate': 0.00018495732983897503, 'epoch': 0.61}

 20%|██        | 252/1248 [24:58<1:34:41,  5.70s/it]
 20%|██        | 253/1248 [25:04<1:36:05,  5.79s/it]
                                                    
{'loss': 0.6532, 'learning_rate': 0.00018482009341868697, 'epoch': 0.61}

 20%|██        | 253/1248 [25:04<1:36:05,  5.79s/it]
 20%|██        | 254/1248 [25:09<1:35:35,  5.77s/it]
                                                    
{'loss': 0.6371, 'learning_rate': 0.00018468228522031195, 'epoch': 0.61}

 20%|██        | 254/1248 [25:09<1:35:35,  5.77s/it]
 20%|██        | 255/1248 [25:15<1:35:48,  5.79s/it]
                                                    
{'loss': 0.6154, 'learning_rate': 0.0001845439061728246, 'epoch': 0.61}

 20%|██        | 255/1248 [25:15<1:35:48,  5.79s/it]
 21%|██        | 256/1248 [25:21<1:37:34,  5.90s/it]
                                                    
{'loss': 0.6278, 'learning_rate': 0.00018440495720904756, 'epoch': 0.62}

 21%|██        | 256/1248 [25:21<1:37:34,  5.90s/it]
 21%|██        | 257/1248 [25:27<1:36:40,  5.85s/it]
                                                    
{'loss': 0.603, 'learning_rate': 0.0001842654392656454, 'epoch': 0.62}

 21%|██        | 257/1248 [25:27<1:36:40,  5.85s/it]
 21%|██        | 258/1248 [25:33<1:36:46,  5.87s/it]
                                                    
{'loss': 0.586, 'learning_rate': 0.00018412535328311814, 'epoch': 0.62}

 21%|██        | 258/1248 [25:33<1:36:46,  5.87s/it]
 21%|██        | 259/1248 [25:39<1:36:04,  5.83s/it]
                                                    
{'loss': 0.608, 'learning_rate': 0.000183984700205795, 'epoch': 0.62}

 21%|██        | 259/1248 [25:39<1:36:04,  5.83s/it]
 21%|██        | 260/1248 [25:45<1:36:53,  5.88s/it]
                                                    
{'loss': 0.5748, 'learning_rate': 0.00018384348098182815, 'epoch': 0.62}

 21%|██        | 260/1248 [25:45<1:36:53,  5.88s/it]
 21%|██        | 261/1248 [25:51<1:37:33,  5.93s/it]
                                                    
{'loss': 0.5885, 'learning_rate': 0.00018370169656318602, 'epoch': 0.63}

 21%|██        | 261/1248 [25:51<1:37:33,  5.93s/it]
 21%|██        | 262/1248 [25:57<1:37:31,  5.93s/it]
                                                    
{'loss': 0.6253, 'learning_rate': 0.00018355934790564718, 'epoch': 0.63}

 21%|██        | 262/1248 [25:57<1:37:31,  5.93s/it]
 21%|██        | 263/1248 [26:03<1:37:09,  5.92s/it]
                                                    
{'loss': 0.6006, 'learning_rate': 0.00018341643596879367, 'epoch': 0.63}

 21%|██        | 263/1248 [26:03<1:37:09,  5.92s/it]
 21%|██        | 264/1248 [26:08<1:36:17,  5.87s/it]
                                                    
{'loss': 0.7325, 'learning_rate': 0.00018327296171600471, 'epoch': 0.63}

 21%|██        | 264/1248 [26:08<1:36:17,  5.87s/it]
 21%|██        | 265/1248 [26:14<1:37:10,  5.93s/it]
                                                    
{'loss': 0.6077, 'learning_rate': 0.00018312892611445017, 'epoch': 0.64}

 21%|██        | 265/1248 [26:14<1:37:10,  5.93s/it]
 21%|██▏       | 266/1248 [26:21<1:40:10,  6.12s/it]
                                                    
{'loss': 0.6185, 'learning_rate': 0.00018298433013508384, 'epoch': 0.64}

 21%|██▏       | 266/1248 [26:21<1:40:10,  6.12s/it]
 21%|██▏       | 267/1248 [26:27<1:37:20,  5.95s/it]
                                                    
{'loss': 0.6024, 'learning_rate': 0.0001828391747526373, 'epoch': 0.64}

 21%|██▏       | 267/1248 [26:27<1:37:20,  5.95s/it]
 21%|██▏       | 268/1248 [26:33<1:37:47,  5.99s/it]
                                                    
{'loss': 0.6211, 'learning_rate': 0.0001826934609456129, 'epoch': 0.64}

 21%|██▏       | 268/1248 [26:33<1:37:47,  5.99s/it]
 22%|██▏       | 269/1248 [26:38<1:35:29,  5.85s/it]
                                                    
{'loss': 0.6719, 'learning_rate': 0.0001825471896962774, 'epoch': 0.65}

 22%|██▏       | 269/1248 [26:38<1:35:29,  5.85s/it]
 22%|██▏       | 270/1248 [26:44<1:36:05,  5.89s/it]
                                                    
{'loss': 0.6054, 'learning_rate': 0.00018240036199065546, 'epoch': 0.65}

 22%|██▏       | 270/1248 [26:44<1:36:05,  5.89s/it]
 22%|██▏       | 271/1248 [26:51<1:38:52,  6.07s/it]
                                                    
{'loss': 0.5946, 'learning_rate': 0.00018225297881852264, 'epoch': 0.65}

 22%|██▏       | 271/1248 [26:51<1:38:52,  6.07s/it]
 22%|██▏       | 272/1248 [26:56<1:37:20,  5.98s/it]
                                                    
{'loss': 0.6277, 'learning_rate': 0.00018210504117339914, 'epoch': 0.65}

 22%|██▏       | 272/1248 [26:56<1:37:20,  5.98s/it]
 22%|██▏       | 273/1248 [27:02<1:36:37,  5.95s/it]
                                                    
{'loss': 0.6139, 'learning_rate': 0.00018195655005254273, 'epoch': 0.66}

 22%|██▏       | 273/1248 [27:02<1:36:37,  5.95s/it]
 22%|██▏       | 274/1248 [27:09<1:39:04,  6.10s/it]
                                                    
{'loss': 0.6688, 'learning_rate': 0.00018180750645694236, 'epoch': 0.66}

 22%|██▏       | 274/1248 [27:09<1:39:04,  6.10s/it]
 22%|██▏       | 275/1248 [27:15<1:38:11,  6.06s/it]
                                                    
{'loss': 0.572, 'learning_rate': 0.00018165791139131108, 'epoch': 0.66}

 22%|██▏       | 275/1248 [27:15<1:38:11,  6.06s/it]
 22%|██▏       | 276/1248 [27:21<1:38:16,  6.07s/it]
                                                    
{'loss': 0.5978, 'learning_rate': 0.00018150776586407956, 'epoch': 0.66}

 22%|██▏       | 276/1248 [27:21<1:38:16,  6.07s/it]
 22%|██▏       | 277/1248 [27:27<1:36:43,  5.98s/it]
                                                    
{'loss': 0.5662, 'learning_rate': 0.00018135707088738913, 'epoch': 0.67}

 22%|██▏       | 277/1248 [27:27<1:36:43,  5.98s/it]
 22%|██▏       | 278/1248 [27:32<1:35:48,  5.93s/it]
                                                    
{'loss': 0.5981, 'learning_rate': 0.00018120582747708502, 'epoch': 0.67}

 22%|██▏       | 278/1248 [27:32<1:35:48,  5.93s/it]
 22%|██▏       | 279/1248 [27:38<1:35:18,  5.90s/it]
                                                    
{'loss': 0.622, 'learning_rate': 0.00018105403665270942, 'epoch': 0.67}

 22%|██▏       | 279/1248 [27:38<1:35:18,  5.90s/it]
 22%|██▏       | 280/1248 [27:44<1:34:39,  5.87s/it]
                                                    
{'loss': 0.5896, 'learning_rate': 0.00018090169943749476, 'epoch': 0.67}

 22%|██▏       | 280/1248 [27:44<1:34:39,  5.87s/it]
 23%|██▎       | 281/1248 [27:50<1:35:34,  5.93s/it]
                                                    
{'loss': 0.6195, 'learning_rate': 0.00018074881685835667, 'epoch': 0.68}

 23%|██▎       | 281/1248 [27:50<1:35:34,  5.93s/it]
 23%|██▎       | 282/1248 [27:56<1:34:29,  5.87s/it]
                                                    
{'loss': 0.6592, 'learning_rate': 0.00018059538994588716, 'epoch': 0.68}

 23%|██▎       | 282/1248 [27:56<1:34:29,  5.87s/it]
 23%|██▎       | 283/1248 [28:02<1:35:59,  5.97s/it]
                                                    
{'loss': 0.6254, 'learning_rate': 0.00018044141973434758, 'epoch': 0.68}

 23%|██▎       | 283/1248 [28:02<1:35:59,  5.97s/it]
 23%|██▎       | 284/1248 [28:08<1:36:26,  6.00s/it]
                                                    
{'loss': 0.5942, 'learning_rate': 0.00018028690726166173, 'epoch': 0.68}

 23%|██▎       | 284/1248 [28:08<1:36:26,  6.00s/it]
 23%|██▎       | 285/1248 [28:14<1:33:49,  5.85s/it]
                                                    
{'loss': 0.6749, 'learning_rate': 0.00018013185356940885, 'epoch': 0.69}

 23%|██▎       | 285/1248 [28:14<1:33:49,  5.85s/it]
 23%|██▎       | 286/1248 [28:19<1:34:14,  5.88s/it]
                                                    
{'loss': 0.6086, 'learning_rate': 0.0001799762597028165, 'epoch': 0.69}

 23%|██▎       | 286/1248 [28:19<1:34:14,  5.88s/it]
 23%|██▎       | 287/1248 [28:26<1:35:54,  5.99s/it]
                                                    
{'loss': 0.6122, 'learning_rate': 0.00017982012671075367, 'epoch': 0.69}

 23%|██▎       | 287/1248 [28:26<1:35:54,  5.99s/it]
 23%|██▎       | 288/1248 [28:32<1:36:26,  6.03s/it]
                                                    
{'loss': 0.5842, 'learning_rate': 0.0001796634556457236, 'epoch': 0.69}

 23%|██▎       | 288/1248 [28:32<1:36:26,  6.03s/it]
 23%|██▎       | 289/1248 [28:38<1:36:32,  6.04s/it]
                                                    
{'loss': 0.6024, 'learning_rate': 0.00017950624756385674, 'epoch': 0.69}

 23%|██▎       | 289/1248 [28:38<1:36:32,  6.04s/it]
 23%|██▎       | 290/1248 [28:44<1:35:10,  5.96s/it]
                                                    
{'loss': 0.6145, 'learning_rate': 0.00017934850352490357, 'epoch': 0.7}

 23%|██▎       | 290/1248 [28:44<1:35:10,  5.96s/it]
 23%|██▎       | 291/1248 [28:50<1:34:57,  5.95s/it]
                                                    
{'loss': 0.6224, 'learning_rate': 0.00017919022459222752, 'epoch': 0.7}

 23%|██▎       | 291/1248 [28:50<1:34:57,  5.95s/it]
 23%|██▎       | 292/1248 [28:56<1:34:56,  5.96s/it]
                                                    
{'loss': 0.642, 'learning_rate': 0.00017903141183279778, 'epoch': 0.7}

 23%|██▎       | 292/1248 [28:56<1:34:56,  5.96s/it]
 23%|██▎       | 293/1248 [29:01<1:34:29,  5.94s/it]
                                                    
{'loss': 0.6279, 'learning_rate': 0.00017887206631718203, 'epoch': 0.7}

 23%|██▎       | 293/1248 [29:01<1:34:29,  5.94s/it]
 24%|██▎       | 294/1248 [29:07<1:32:32,  5.82s/it]
                                                    
{'loss': 0.5691, 'learning_rate': 0.0001787121891195394, 'epoch': 0.71}

 24%|██▎       | 294/1248 [29:07<1:32:32,  5.82s/it]
 24%|██▎       | 295/1248 [29:13<1:33:31,  5.89s/it]
                                                    
{'loss': 0.6362, 'learning_rate': 0.0001785517813176131, 'epoch': 0.71}

 24%|██▎       | 295/1248 [29:13<1:33:31,  5.89s/it]
 24%|██▎       | 296/1248 [29:19<1:34:52,  5.98s/it]
                                                    
{'loss': 0.595, 'learning_rate': 0.00017839084399272315, 'epoch': 0.71}

 24%|██▎       | 296/1248 [29:19<1:34:52,  5.98s/it]
 24%|██▍       | 297/1248 [29:25<1:35:27,  6.02s/it]
                                                    
{'loss': 0.5751, 'learning_rate': 0.00017822937822975908, 'epoch': 0.71}

 24%|██▍       | 297/1248 [29:25<1:35:27,  6.02s/it]
 24%|██▍       | 298/1248 [29:31<1:34:27,  5.97s/it]
                                                    
{'loss': 0.5987, 'learning_rate': 0.0001780673851171728, 'epoch': 0.72}

 24%|██▍       | 298/1248 [29:31<1:34:27,  5.97s/it]
 24%|██▍       | 299/1248 [29:37<1:35:36,  6.05s/it]
                                                    
{'loss': 0.648, 'learning_rate': 0.000177904865746971, 'epoch': 0.72}

 24%|██▍       | 299/1248 [29:37<1:35:36,  6.05s/it]
 24%|██▍       | 300/1248 [29:43<1:34:29,  5.98s/it]
                                                    
{'loss': 0.6032, 'learning_rate': 0.0001777418212147079, 'epoch': 0.72}

 24%|██▍       | 300/1248 [29:43<1:34:29,  5.98s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 24%|██▍       | 301/1248 [30:09<3:08:03, 11.91s/it]
                                                    
{'loss': 0.6024, 'learning_rate': 0.00017757825261947795, 'epoch': 0.72}

 24%|██▍       | 301/1248 [30:09<3:08:03, 11.91s/it]
 24%|██▍       | 302/1248 [30:15<2:38:06, 10.03s/it]
                                                    
{'loss': 0.6086, 'learning_rate': 0.00017741416106390826, 'epoch': 0.73}

 24%|██▍       | 302/1248 [30:15<2:38:06, 10.03s/it]
 24%|██▍       | 303/1248 [30:21<2:19:09,  8.84s/it]
                                                    
{'loss': 0.5682, 'learning_rate': 0.00017724954765415137, 'epoch': 0.73}

 24%|██▍       | 303/1248 [30:21<2:19:09,  8.84s/it]
 24%|██▍       | 304/1248 [30:26<2:04:10,  7.89s/it]
                                                    
{'loss': 0.5644, 'learning_rate': 0.00017708441349987753, 'epoch': 0.73}

 24%|██▍       | 304/1248 [30:26<2:04:10,  7.89s/it]
 24%|██▍       | 305/1248 [30:33<1:56:16,  7.40s/it]
                                                    
{'loss': 0.6576, 'learning_rate': 0.0001769187597142675, 'epoch': 0.73}

 24%|██▍       | 305/1248 [30:33<1:56:16,  7.40s/it]
 25%|██▍       | 306/1248 [30:39<1:49:46,  6.99s/it]
                                                    
{'loss': 0.6283, 'learning_rate': 0.0001767525874140048, 'epoch': 0.74}

 25%|██▍       | 306/1248 [30:39<1:49:46,  6.99s/it]
 25%|██▍       | 307/1248 [30:44<1:42:35,  6.54s/it]
                                                    
{'loss': 0.6383, 'learning_rate': 0.00017658589771926838, 'epoch': 0.74}

 25%|██▍       | 307/1248 [30:44<1:42:35,  6.54s/it]
 25%|██▍       | 308/1248 [30:50<1:40:39,  6.43s/it]
                                                    
{'loss': 0.7395, 'learning_rate': 0.00017641869175372493, 'epoch': 0.74}

 25%|██▍       | 308/1248 [30:50<1:40:39,  6.43s/it]
 25%|██▍       | 309/1248 [30:57<1:39:34,  6.36s/it]
                                                    
{'loss': 0.6187, 'learning_rate': 0.00017625097064452136, 'epoch': 0.74}

 25%|██▍       | 309/1248 [30:57<1:39:34,  6.36s/it]
 25%|██▍       | 310/1248 [31:02<1:37:14,  6.22s/it]
                                                    
{'loss': 0.6082, 'learning_rate': 0.0001760827355222772, 'epoch': 0.75}

 25%|██▍       | 310/1248 [31:02<1:37:14,  6.22s/it]
 25%|██▍       | 311/1248 [31:08<1:35:14,  6.10s/it]
                                                    
{'loss': 0.6113, 'learning_rate': 0.00017591398752107705, 'epoch': 0.75}

 25%|██▍       | 311/1248 [31:08<1:35:14,  6.10s/it]
 25%|██▌       | 312/1248 [31:14<1:34:19,  6.05s/it]
                                                    
{'loss': 0.593, 'learning_rate': 0.00017574472777846274, 'epoch': 0.75}

 25%|██▌       | 312/1248 [31:14<1:34:19,  6.05s/it]
 25%|██▌       | 313/1248 [31:19<1:30:48,  5.83s/it]
                                                    
{'loss': 0.6006, 'learning_rate': 0.00017557495743542585, 'epoch': 0.75}

 25%|██▌       | 313/1248 [31:20<1:30:48,  5.83s/it]
 25%|██▌       | 314/1248 [31:26<1:31:44,  5.89s/it]
                                                    
{'loss': 0.6639, 'learning_rate': 0.00017540467763639994, 'epoch': 0.75}

 25%|██▌       | 314/1248 [31:26<1:31:44,  5.89s/it]
 25%|██▌       | 315/1248 [31:31<1:31:50,  5.91s/it]
                                                    
{'loss': 0.6242, 'learning_rate': 0.0001752338895292529, 'epoch': 0.76}

 25%|██▌       | 315/1248 [31:31<1:31:50,  5.91s/it]
 25%|██▌       | 316/1248 [31:37<1:31:58,  5.92s/it]
                                                    
{'loss': 0.5859, 'learning_rate': 0.00017506259426527902, 'epoch': 0.76}

 25%|██▌       | 316/1248 [31:37<1:31:58,  5.92s/it]
 25%|██▌       | 317/1248 [31:44<1:32:41,  5.97s/it]
                                                    
{'loss': 0.5968, 'learning_rate': 0.00017489079299919157, 'epoch': 0.76}

 25%|██▌       | 317/1248 [31:44<1:32:41,  5.97s/it]
 25%|██▌       | 318/1248 [31:49<1:32:34,  5.97s/it]
                                                    
{'loss': 0.6095, 'learning_rate': 0.00017471848688911464, 'epoch': 0.76}

 25%|██▌       | 318/1248 [31:50<1:32:34,  5.97s/it]
 26%|██▌       | 319/1248 [31:55<1:30:17,  5.83s/it]
                                                    
{'loss': 0.7362, 'learning_rate': 0.0001745456770965756, 'epoch': 0.77}

 26%|██▌       | 319/1248 [31:55<1:30:17,  5.83s/it]
 26%|██▌       | 320/1248 [32:01<1:29:58,  5.82s/it]
                                                    
{'loss': 0.6354, 'learning_rate': 0.00017437236478649716, 'epoch': 0.77}

 26%|██▌       | 320/1248 [32:01<1:29:58,  5.82s/it]
 26%|██▌       | 321/1248 [32:07<1:30:02,  5.83s/it]
                                                    
{'loss': 0.5866, 'learning_rate': 0.00017419855112718951, 'epoch': 0.77}

 26%|██▌       | 321/1248 [32:07<1:30:02,  5.83s/it]
 26%|██▌       | 322/1248 [32:13<1:33:17,  6.04s/it]
                                                    
{'loss': 0.6317, 'learning_rate': 0.0001740242372903425, 'epoch': 0.77}

 26%|██▌       | 322/1248 [32:13<1:33:17,  6.04s/it]
 26%|██▌       | 323/1248 [32:19<1:32:38,  6.01s/it]
                                                    
{'loss': 0.5817, 'learning_rate': 0.00017384942445101772, 'epoch': 0.78}

 26%|██▌       | 323/1248 [32:19<1:32:38,  6.01s/it]
 26%|██▌       | 324/1248 [32:25<1:29:49,  5.83s/it]
                                                    
{'loss': 0.552, 'learning_rate': 0.0001736741137876405, 'epoch': 0.78}

 26%|██▌       | 324/1248 [32:25<1:29:49,  5.83s/it]
 26%|██▌       | 325/1248 [32:31<1:31:17,  5.93s/it]
                                                    
{'loss': 0.5898, 'learning_rate': 0.0001734983064819921, 'epoch': 0.78}

 26%|██▌       | 325/1248 [32:31<1:31:17,  5.93s/it]
 26%|██▌       | 326/1248 [32:37<1:31:31,  5.96s/it]
                                                    
{'loss': 0.606, 'learning_rate': 0.00017332200371920174, 'epoch': 0.78}

 26%|██▌       | 326/1248 [32:37<1:31:31,  5.96s/it]
 26%|██▌       | 327/1248 [32:43<1:31:28,  5.96s/it]
                                                    
{'loss': 0.5737, 'learning_rate': 0.00017314520668773836, 'epoch': 0.79}

 26%|██▌       | 327/1248 [32:43<1:31:28,  5.96s/it]
 26%|██▋       | 328/1248 [32:49<1:31:57,  6.00s/it]
                                                    
{'loss': 0.5998, 'learning_rate': 0.000172967916579403, 'epoch': 0.79}

 26%|██▋       | 328/1248 [32:49<1:31:57,  6.00s/it]
 26%|██▋       | 329/1248 [32:55<1:31:51,  6.00s/it]
                                                    
{'loss': 0.6053, 'learning_rate': 0.00017279013458932046, 'epoch': 0.79}

 26%|██▋       | 329/1248 [32:55<1:31:51,  6.00s/it]
 26%|██▋       | 330/1248 [33:00<1:30:12,  5.90s/it]
                                                    
{'loss': 0.6214, 'learning_rate': 0.00017261186191593135, 'epoch': 0.79}

 26%|██▋       | 330/1248 [33:00<1:30:12,  5.90s/it]
 27%|██▋       | 331/1248 [33:07<1:31:42,  6.00s/it]
                                                    
{'loss': 0.6721, 'learning_rate': 0.00017243309976098405, 'epoch': 0.8}

 27%|██▋       | 331/1248 [33:07<1:31:42,  6.00s/it]
 27%|██▋       | 332/1248 [33:13<1:31:39,  6.00s/it]
                                                    
{'loss': 0.5842, 'learning_rate': 0.00017225384932952656, 'epoch': 0.8}

 27%|██▋       | 332/1248 [33:13<1:31:39,  6.00s/it]
 27%|██▋       | 333/1248 [33:18<1:30:26,  5.93s/it]
                                                    
{'loss': 0.5895, 'learning_rate': 0.00017207411182989832, 'epoch': 0.8}

 27%|██▋       | 333/1248 [33:18<1:30:26,  5.93s/it]
 27%|██▋       | 334/1248 [33:24<1:30:40,  5.95s/it]
                                                    
{'loss': 0.6209, 'learning_rate': 0.00017189388847372225, 'epoch': 0.8}

 27%|██▋       | 334/1248 [33:24<1:30:40,  5.95s/it]
 27%|██▋       | 335/1248 [33:30<1:30:10,  5.93s/it]
                                                    
{'loss': 0.587, 'learning_rate': 0.00017171318047589637, 'epoch': 0.81}

 27%|██▋       | 335/1248 [33:30<1:30:10,  5.93s/it]
 27%|██▋       | 336/1248 [33:35<1:26:19,  5.68s/it]
                                                    
{'loss': 0.6052, 'learning_rate': 0.00017153198905458573, 'epoch': 0.81}

 27%|██▋       | 336/1248 [33:35<1:26:19,  5.68s/it]
 27%|██▋       | 337/1248 [33:41<1:26:16,  5.68s/it]
                                                    
{'loss': 0.5814, 'learning_rate': 0.00017135031543121413, 'epoch': 0.81}

 27%|██▋       | 337/1248 [33:41<1:26:16,  5.68s/it]
 27%|██▋       | 338/1248 [33:47<1:25:30,  5.64s/it]
                                                    
{'loss': 0.5686, 'learning_rate': 0.00017116816083045602, 'epoch': 0.81}

 27%|██▋       | 338/1248 [33:47<1:25:30,  5.64s/it]
 27%|██▋       | 339/1248 [33:52<1:23:15,  5.50s/it]
                                                    
{'loss': 0.6086, 'learning_rate': 0.0001709855264802281, 'epoch': 0.81}

 27%|██▋       | 339/1248 [33:52<1:23:15,  5.50s/it]
 27%|██▋       | 340/1248 [33:57<1:21:41,  5.40s/it]
                                                    
{'loss': 0.6128, 'learning_rate': 0.00017080241361168107, 'epoch': 0.82}

 27%|██▋       | 340/1248 [33:57<1:21:41,  5.40s/it]
 27%|██▋       | 341/1248 [34:02<1:20:29,  5.33s/it]
                                                    
{'loss': 0.565, 'learning_rate': 0.0001706188234591914, 'epoch': 0.82}

 27%|██▋       | 341/1248 [34:02<1:20:29,  5.33s/it]
 27%|██▋       | 342/1248 [34:07<1:19:49,  5.29s/it]
                                                    
{'loss': 0.7477, 'learning_rate': 0.00017043475726035288, 'epoch': 0.82}

 27%|██▋       | 342/1248 [34:07<1:19:49,  5.29s/it]
 27%|██▋       | 343/1248 [34:13<1:21:58,  5.43s/it]
                                                    
{'loss': 0.5915, 'learning_rate': 0.00017025021625596853, 'epoch': 0.82}

 27%|██▋       | 343/1248 [34:13<1:21:58,  5.43s/it]
 28%|██▊       | 344/1248 [34:19<1:22:31,  5.48s/it]
                                                    
{'loss': 0.6111, 'learning_rate': 0.00017006520169004187, 'epoch': 0.83}

 28%|██▊       | 344/1248 [34:19<1:22:31,  5.48s/it]
 28%|██▊       | 345/1248 [34:24<1:22:12,  5.46s/it]
                                                    
{'loss': 0.6097, 'learning_rate': 0.0001698797148097689, 'epoch': 0.83}

 28%|██▊       | 345/1248 [34:24<1:22:12,  5.46s/it]
 28%|██▊       | 346/1248 [34:30<1:22:51,  5.51s/it]
                                                    
{'loss': 0.6043, 'learning_rate': 0.00016969375686552937, 'epoch': 0.83}

 28%|██▊       | 346/1248 [34:30<1:22:51,  5.51s/it]
 28%|██▊       | 347/1248 [34:36<1:24:56,  5.66s/it]
                                                    
{'loss': 0.5458, 'learning_rate': 0.00016950732911087858, 'epoch': 0.83}

 28%|██▊       | 347/1248 [34:36<1:24:56,  5.66s/it]
 28%|██▊       | 348/1248 [34:42<1:26:29,  5.77s/it]
                                                    
{'loss': 0.6296, 'learning_rate': 0.0001693204328025389, 'epoch': 0.84}

 28%|██▊       | 348/1248 [34:42<1:26:29,  5.77s/it]
 28%|██▊       | 349/1248 [34:48<1:28:32,  5.91s/it]
                                                    
{'loss': 0.5793, 'learning_rate': 0.0001691330692003912, 'epoch': 0.84}

 28%|██▊       | 349/1248 [34:48<1:28:32,  5.91s/it]
 28%|██▊       | 350/1248 [34:53<1:26:28,  5.78s/it]
                                                    
{'loss': 0.5507, 'learning_rate': 0.00016894523956746639, 'epoch': 0.84}

 28%|██▊       | 350/1248 [34:53<1:26:28,  5.78s/it]
 28%|██▊       | 351/1248 [34:59<1:26:17,  5.77s/it]
                                                    
{'loss': 0.6016, 'learning_rate': 0.000168756945169937, 'epoch': 0.84}

 28%|██▊       | 351/1248 [34:59<1:26:17,  5.77s/it]
 28%|██▊       | 352/1248 [35:05<1:24:45,  5.68s/it]
                                                    
{'loss': 0.5631, 'learning_rate': 0.00016856818727710847, 'epoch': 0.85}

 28%|██▊       | 352/1248 [35:05<1:24:45,  5.68s/it]
 28%|██▊       | 353/1248 [35:10<1:25:16,  5.72s/it]
                                                    
{'loss': 0.6037, 'learning_rate': 0.0001683789671614107, 'epoch': 0.85}

 28%|██▊       | 353/1248 [35:10<1:25:16,  5.72s/it]
 28%|██▊       | 354/1248 [35:16<1:25:53,  5.76s/it]
                                                    
{'loss': 0.5988, 'learning_rate': 0.00016818928609838967, 'epoch': 0.85}

 28%|██▊       | 354/1248 [35:16<1:25:53,  5.76s/it]
 28%|██▊       | 355/1248 [35:22<1:27:12,  5.86s/it]
                                                    
{'loss': 0.5907, 'learning_rate': 0.0001679991453666983, 'epoch': 0.85}

 28%|██▊       | 355/1248 [35:22<1:27:12,  5.86s/it]
 29%|██▊       | 356/1248 [35:28<1:27:41,  5.90s/it]
                                                    
{'loss': 0.5726, 'learning_rate': 0.0001678085462480885, 'epoch': 0.86}

 29%|██▊       | 356/1248 [35:28<1:27:41,  5.90s/it]
 29%|██▊       | 357/1248 [35:34<1:26:08,  5.80s/it]
                                                    
{'loss': 0.6361, 'learning_rate': 0.00016761749002740193, 'epoch': 0.86}

 29%|██▊       | 357/1248 [35:34<1:26:08,  5.80s/it]
 29%|██▊       | 358/1248 [35:40<1:25:49,  5.79s/it]
                                                    
{'loss': 0.6128, 'learning_rate': 0.00016742597799256182, 'epoch': 0.86}

 29%|██▊       | 358/1248 [35:40<1:25:49,  5.79s/it]
 29%|██▉       | 359/1248 [35:46<1:25:37,  5.78s/it]
                                                    
{'loss': 0.657, 'learning_rate': 0.0001672340114345639, 'epoch': 0.86}

 29%|██▉       | 359/1248 [35:46<1:25:37,  5.78s/it]
 29%|██▉       | 360/1248 [35:51<1:26:20,  5.83s/it]
                                                    
{'loss': 0.6186, 'learning_rate': 0.00016704159164746796, 'epoch': 0.87}

 29%|██▉       | 360/1248 [35:51<1:26:20,  5.83s/it]
 29%|██▉       | 361/1248 [35:57<1:24:39,  5.73s/it]
                                                    
{'loss': 0.575, 'learning_rate': 0.00016684871992838905, 'epoch': 0.87}

 29%|██▉       | 361/1248 [35:57<1:24:39,  5.73s/it]
 29%|██▉       | 362/1248 [36:03<1:24:41,  5.73s/it]
                                                    
{'loss': 0.5753, 'learning_rate': 0.00016665539757748868, 'epoch': 0.87}

 29%|██▉       | 362/1248 [36:03<1:24:41,  5.73s/it]
 29%|██▉       | 363/1248 [36:08<1:22:50,  5.62s/it]
                                                    
{'loss': 0.583, 'learning_rate': 0.00016646162589796615, 'epoch': 0.87}

 29%|██▉       | 363/1248 [36:08<1:22:50,  5.62s/it]
 29%|██▉       | 364/1248 [36:14<1:22:48,  5.62s/it]
                                                    
{'loss': 0.6023, 'learning_rate': 0.00016626740619604967, 'epoch': 0.88}

 29%|██▉       | 364/1248 [36:14<1:22:48,  5.62s/it]
 29%|██▉       | 365/1248 [36:20<1:23:43,  5.69s/it]
                                                    
{'loss': 0.6466, 'learning_rate': 0.0001660727397809876, 'epoch': 0.88}

 29%|██▉       | 365/1248 [36:20<1:23:43,  5.69s/it]
 29%|██▉       | 366/1248 [36:26<1:25:16,  5.80s/it]
                                                    
{'loss': 0.5926, 'learning_rate': 0.00016587762796503968, 'epoch': 0.88}

 29%|██▉       | 366/1248 [36:26<1:25:16,  5.80s/it]
 29%|██▉       | 367/1248 [36:31<1:25:32,  5.83s/it]
                                                    
{'loss': 0.5702, 'learning_rate': 0.00016568207206346804, 'epoch': 0.88}

 29%|██▉       | 367/1248 [36:31<1:25:32,  5.83s/it]
 29%|██▉       | 368/1248 [36:37<1:23:53,  5.72s/it]
                                                    
{'loss': 0.6028, 'learning_rate': 0.00016548607339452853, 'epoch': 0.88}

 29%|██▉       | 368/1248 [36:37<1:23:53,  5.72s/it]
 30%|██▉       | 369/1248 [36:43<1:25:18,  5.82s/it]
                                                    
{'loss': 0.7457, 'learning_rate': 0.00016528963327946158, 'epoch': 0.89}

 30%|██▉       | 369/1248 [36:43<1:25:18,  5.82s/it]
 30%|██▉       | 370/1248 [36:49<1:24:41,  5.79s/it]
                                                    
{'loss': 0.5938, 'learning_rate': 0.00016509275304248363, 'epoch': 0.89}

 30%|██▉       | 370/1248 [36:49<1:24:41,  5.79s/it]
 30%|██▉       | 371/1248 [36:54<1:24:06,  5.75s/it]
                                                    
{'loss': 0.6036, 'learning_rate': 0.00016489543401077784, 'epoch': 0.89}

 30%|██▉       | 371/1248 [36:54<1:24:06,  5.75s/it]
 30%|██▉       | 372/1248 [37:00<1:22:43,  5.67s/it]
                                                    
{'loss': 0.5935, 'learning_rate': 0.00016469767751448538, 'epoch': 0.89}

 30%|██▉       | 372/1248 [37:00<1:22:43,  5.67s/it]
 30%|██▉       | 373/1248 [37:05<1:22:16,  5.64s/it]
                                                    
{'loss': 0.5768, 'learning_rate': 0.00016449948488669639, 'epoch': 0.9}

 30%|██▉       | 373/1248 [37:05<1:22:16,  5.64s/it]
 30%|██▉       | 374/1248 [37:11<1:21:25,  5.59s/it]
                                                    
{'loss': 0.5827, 'learning_rate': 0.00016430085746344108, 'epoch': 0.9}

 30%|██▉       | 374/1248 [37:11<1:21:25,  5.59s/it]
 30%|███       | 375/1248 [37:16<1:20:32,  5.54s/it]
                                                    
{'loss': 0.5882, 'learning_rate': 0.0001641017965836805, 'epoch': 0.9}

 30%|███       | 375/1248 [37:16<1:20:32,  5.54s/it]
 30%|███       | 376/1248 [37:22<1:21:26,  5.60s/it]
                                                    
{'loss': 0.5742, 'learning_rate': 0.0001639023035892978, 'epoch': 0.9}

 30%|███       | 376/1248 [37:22<1:21:26,  5.60s/it]
 30%|███       | 377/1248 [37:28<1:22:04,  5.65s/it]
                                                    
{'loss': 0.5665, 'learning_rate': 0.00016370237982508897, 'epoch': 0.91}

 30%|███       | 377/1248 [37:28<1:22:04,  5.65s/it]
 30%|███       | 378/1248 [37:33<1:20:39,  5.56s/it]
                                                    
{'loss': 0.5986, 'learning_rate': 0.00016350202663875386, 'epoch': 0.91}

 30%|███       | 378/1248 [37:33<1:20:39,  5.56s/it]
 30%|███       | 379/1248 [37:39<1:21:54,  5.66s/it]
                                                    
{'loss': 0.5976, 'learning_rate': 0.00016330124538088705, 'epoch': 0.91}

 30%|███       | 379/1248 [37:39<1:21:54,  5.66s/it]
 30%|███       | 380/1248 [37:45<1:22:00,  5.67s/it]
                                                    
{'loss': 0.624, 'learning_rate': 0.00016310003740496886, 'epoch': 0.91}

 30%|███       | 380/1248 [37:45<1:22:00,  5.67s/it]
 31%|███       | 381/1248 [37:50<1:21:38,  5.65s/it]
                                                    
{'loss': 0.5604, 'learning_rate': 0.0001628984040673561, 'epoch': 0.92}

 31%|███       | 381/1248 [37:50<1:21:38,  5.65s/it]
 31%|███       | 382/1248 [37:57<1:23:42,  5.80s/it]
                                                    
{'loss': 0.6067, 'learning_rate': 0.00016269634672727294, 'epoch': 0.92}

 31%|███       | 382/1248 [37:57<1:23:42,  5.80s/it]
 31%|███       | 383/1248 [38:02<1:22:55,  5.75s/it]
                                                    
{'loss': 0.625, 'learning_rate': 0.00016249386674680184, 'epoch': 0.92}

 31%|███       | 383/1248 [38:02<1:22:55,  5.75s/it]
 31%|███       | 384/1248 [38:08<1:23:25,  5.79s/it]
                                                    
{'loss': 0.5753, 'learning_rate': 0.00016229096549087434, 'epoch': 0.92}

 31%|███       | 384/1248 [38:08<1:23:25,  5.79s/it]
 31%|███       | 385/1248 [38:14<1:23:08,  5.78s/it]
                                                    
{'loss': 0.585, 'learning_rate': 0.00016208764432726165, 'epoch': 0.93}

 31%|███       | 385/1248 [38:14<1:23:08,  5.78s/it]
 31%|███       | 386/1248 [38:19<1:20:30,  5.60s/it]
                                                    
{'loss': 0.549, 'learning_rate': 0.0001618839046265658, 'epoch': 0.93}

 31%|███       | 386/1248 [38:19<1:20:30,  5.60s/it]
 31%|███       | 387/1248 [38:25<1:20:06,  5.58s/it]
                                                    
{'loss': 0.5921, 'learning_rate': 0.0001616797477622101, 'epoch': 0.93}

 31%|███       | 387/1248 [38:25<1:20:06,  5.58s/it]
 31%|███       | 388/1248 [38:30<1:18:23,  5.47s/it]
                                                    
{'loss': 0.5852, 'learning_rate': 0.0001614751751104301, 'epoch': 0.93}

 31%|███       | 388/1248 [38:30<1:18:23,  5.47s/it]
 31%|███       | 389/1248 [38:35<1:17:28,  5.41s/it]
                                                    
{'loss': 0.584, 'learning_rate': 0.00016127018805026403, 'epoch': 0.94}

 31%|███       | 389/1248 [38:35<1:17:28,  5.41s/it]
 31%|███▏      | 390/1248 [38:40<1:15:39,  5.29s/it]
                                                    
{'loss': 0.5914, 'learning_rate': 0.00016106478796354382, 'epoch': 0.94}

 31%|███▏      | 390/1248 [38:40<1:15:39,  5.29s/it]
 31%|███▏      | 391/1248 [38:46<1:18:59,  5.53s/it]
                                                    
{'loss': 0.7402, 'learning_rate': 0.00016085897623488557, 'epoch': 0.94}

 31%|███▏      | 391/1248 [38:46<1:18:59,  5.53s/it]
 31%|███▏      | 392/1248 [38:52<1:19:27,  5.57s/it]
                                                    
{'loss': 0.5806, 'learning_rate': 0.00016065275425168032, 'epoch': 0.94}

 31%|███▏      | 392/1248 [38:52<1:19:27,  5.57s/it]
 31%|███▏      | 393/1248 [38:58<1:20:02,  5.62s/it]
                                                    
{'loss': 0.6149, 'learning_rate': 0.00016044612340408466, 'epoch': 0.94}

 31%|███▏      | 393/1248 [38:58<1:20:02,  5.62s/it]
 32%|███▏      | 394/1248 [39:03<1:18:38,  5.53s/it]
                                                    
{'loss': 0.6675, 'learning_rate': 0.00016023908508501128, 'epoch': 0.95}

 32%|███▏      | 394/1248 [39:03<1:18:38,  5.53s/it]
 32%|███▏      | 395/1248 [39:08<1:17:07,  5.42s/it]
                                                    
{'loss': 0.5708, 'learning_rate': 0.00016003164069011984, 'epoch': 0.95}

 32%|███▏      | 395/1248 [39:08<1:17:07,  5.42s/it]
 32%|███▏      | 396/1248 [39:13<1:16:34,  5.39s/it]
                                                    
{'loss': 0.5286, 'learning_rate': 0.00015982379161780724, 'epoch': 0.95}

 32%|███▏      | 396/1248 [39:13<1:16:34,  5.39s/it]
 32%|███▏      | 397/1248 [39:19<1:17:05,  5.44s/it]
                                                    
{'loss': 0.6597, 'learning_rate': 0.00015961553926919836, 'epoch': 0.95}

 32%|███▏      | 397/1248 [39:19<1:17:05,  5.44s/it]
 32%|███▏      | 398/1248 [39:24<1:17:50,  5.49s/it]
                                                    
{'loss': 0.6326, 'learning_rate': 0.00015940688504813662, 'epoch': 0.96}

 32%|███▏      | 398/1248 [39:24<1:17:50,  5.49s/it]
 32%|███▏      | 399/1248 [39:30<1:17:38,  5.49s/it]
                                                    
{'loss': 0.5579, 'learning_rate': 0.00015919783036117451, 'epoch': 0.96}

 32%|███▏      | 399/1248 [39:30<1:17:38,  5.49s/it]
 32%|███▏      | 400/1248 [39:35<1:17:13,  5.46s/it]
                                                    
{'loss': 0.5498, 'learning_rate': 0.00015898837661756406, 'epoch': 0.96}

 32%|███▏      | 400/1248 [39:35<1:17:13,  5.46s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 32%|███▏      | 401/1248 [40:01<2:41:58, 11.47s/it]
                                                    
{'loss': 0.5653, 'learning_rate': 0.00015877852522924732, 'epoch': 0.96}

 32%|███▏      | 401/1248 [40:01<2:41:58, 11.47s/it]
 32%|███▏      | 402/1248 [40:08<2:21:52, 10.06s/it]
                                                    
{'loss': 0.6716, 'learning_rate': 0.00015856827761084698, 'epoch': 0.97}

 32%|███▏      | 402/1248 [40:08<2:21:52, 10.06s/it]
 32%|███▏      | 403/1248 [40:13<2:01:29,  8.63s/it]
                                                    
{'loss': 0.5683, 'learning_rate': 0.00015835763517965673, 'epoch': 0.97}

 32%|███▏      | 403/1248 [40:13<2:01:29,  8.63s/it]
 32%|███▏      | 404/1248 [40:18<1:46:08,  7.55s/it]
                                                    
{'loss': 0.5865, 'learning_rate': 0.00015814659935563163, 'epoch': 0.97}

 32%|███▏      | 404/1248 [40:18<1:46:08,  7.55s/it]
 32%|███▏      | 405/1248 [40:23<1:35:32,  6.80s/it]
                                                    
{'loss': 0.5702, 'learning_rate': 0.00015793517156137876, 'epoch': 0.97}

 32%|███▏      | 405/1248 [40:23<1:35:32,  6.80s/it]
 33%|███▎      | 406/1248 [40:28<1:29:07,  6.35s/it]
                                                    
{'loss': 0.5685, 'learning_rate': 0.00015772335322214738, 'epoch': 0.98}

 33%|███▎      | 406/1248 [40:28<1:29:07,  6.35s/it]
 33%|███▎      | 407/1248 [40:34<1:27:11,  6.22s/it]
                                                    
{'loss': 0.5978, 'learning_rate': 0.00015751114576581952, 'epoch': 0.98}

 33%|███▎      | 407/1248 [40:34<1:27:11,  6.22s/it]
 33%|███▎      | 408/1248 [40:39<1:22:17,  5.88s/it]
                                                    
{'loss': 0.5814, 'learning_rate': 0.00015729855062290022, 'epoch': 0.98}

 33%|███▎      | 408/1248 [40:39<1:22:17,  5.88s/it]
 33%|███▎      | 409/1248 [40:44<1:18:17,  5.60s/it]
                                                    
{'loss': 0.5971, 'learning_rate': 0.000157085569226508, 'epoch': 0.98}

 33%|███▎      | 409/1248 [40:44<1:18:17,  5.60s/it]
 33%|███▎      | 410/1248 [40:50<1:19:18,  5.68s/it]
                                                    
{'loss': 0.5362, 'learning_rate': 0.0001568722030123651, 'epoch': 0.99}

 33%|███▎      | 410/1248 [40:50<1:19:18,  5.68s/it]
 33%|███▎      | 411/1248 [40:56<1:19:41,  5.71s/it]
                                                    
{'loss': 0.5512, 'learning_rate': 0.00015665845341878782, 'epoch': 0.99}

 33%|███▎      | 411/1248 [40:56<1:19:41,  5.71s/it]
 33%|███▎      | 412/1248 [41:02<1:20:15,  5.76s/it]
                                                    
{'loss': 0.5579, 'learning_rate': 0.00015644432188667695, 'epoch': 0.99}

 33%|███▎      | 412/1248 [41:02<1:20:15,  5.76s/it]
 33%|███▎      | 413/1248 [41:08<1:20:27,  5.78s/it]
                                                    
{'loss': 0.6071, 'learning_rate': 0.0001562298098595078, 'epoch': 0.99}

 33%|███▎      | 413/1248 [41:08<1:20:27,  5.78s/it]
 33%|███▎      | 414/1248 [41:14<1:21:44,  5.88s/it]
                                                    
{'loss': 0.5524, 'learning_rate': 0.00015601491878332077, 'epoch': 1.0}

 33%|███▎      | 414/1248 [41:14<1:21:44,  5.88s/it]
 33%|███▎      | 415/1248 [41:19<1:20:35,  5.80s/it]
                                                    
{'loss': 0.5751, 'learning_rate': 0.0001557996501067114, 'epoch': 1.0}

 33%|███▎      | 415/1248 [41:19<1:20:35,  5.80s/it]
 33%|███▎      | 416/1248 [41:26<1:22:31,  5.95s/it]
                                                    
{'loss': 0.5283, 'learning_rate': 0.00015558400528082057, 'epoch': 1.0}

 33%|███▎      | 416/1248 [41:26<1:22:31,  5.95s/it]
 33%|███▎      | 417/1248 [41:32<1:24:15,  6.08s/it]
                                                    
{'loss': 0.5527, 'learning_rate': 0.000155367985759325, 'epoch': 1.0}

 33%|███▎      | 417/1248 [41:32<1:24:15,  6.08s/it]
 33%|███▎      | 418/1248 [41:38<1:22:48,  5.99s/it]
                                                    
{'loss': 0.5643, 'learning_rate': 0.00015515159299842707, 'epoch': 1.0}

 33%|███▎      | 418/1248 [41:38<1:22:48,  5.99s/it]
 34%|███▎      | 419/1248 [41:44<1:22:01,  5.94s/it]
                                                    
{'loss': 0.5512, 'learning_rate': 0.0001549348284568453, 'epoch': 1.01}

 34%|███▎      | 419/1248 [41:44<1:22:01,  5.94s/it]
 34%|███▎      | 420/1248 [41:49<1:19:51,  5.79s/it]
                                                    
{'loss': 0.568, 'learning_rate': 0.0001547176935958044, 'epoch': 1.01}

 34%|███▎      | 420/1248 [41:49<1:19:51,  5.79s/it]
 34%|███▎      | 421/1248 [41:55<1:20:45,  5.86s/it]
                                                    
{'loss': 0.6586, 'learning_rate': 0.0001545001898790254, 'epoch': 1.01}

 34%|███▎      | 421/1248 [41:55<1:20:45,  5.86s/it]
 34%|███▍      | 422/1248 [42:01<1:21:41,  5.93s/it]
                                                    
{'loss': 0.5899, 'learning_rate': 0.00015428231877271582, 'epoch': 1.01}

 34%|███▍      | 422/1248 [42:01<1:21:41,  5.93s/it]
 34%|███▍      | 423/1248 [42:07<1:20:56,  5.89s/it]
                                                    
{'loss': 0.5851, 'learning_rate': 0.00015406408174555976, 'epoch': 1.02}

 34%|███▍      | 423/1248 [42:07<1:20:56,  5.89s/it]
 34%|███▍      | 424/1248 [42:13<1:20:28,  5.86s/it]
                                                    
{'loss': 0.5189, 'learning_rate': 0.00015384548026870805, 'epoch': 1.02}

 34%|███▍      | 424/1248 [42:13<1:20:28,  5.86s/it]
 34%|███▍      | 425/1248 [42:19<1:20:10,  5.85s/it]
                                                    
{'loss': 0.5658, 'learning_rate': 0.00015362651581576832, 'epoch': 1.02}

 34%|███▍      | 425/1248 [42:19<1:20:10,  5.85s/it]
 34%|███▍      | 426/1248 [42:24<1:20:17,  5.86s/it]
                                                    
{'loss': 0.572, 'learning_rate': 0.00015340718986279502, 'epoch': 1.02}

 34%|███▍      | 426/1248 [42:24<1:20:17,  5.86s/it]
 34%|███▍      | 427/1248 [42:30<1:20:41,  5.90s/it]
                                                    
{'loss': 0.5627, 'learning_rate': 0.00015318750388827943, 'epoch': 1.03}

 34%|███▍      | 427/1248 [42:30<1:20:41,  5.90s/it]
 34%|███▍      | 428/1248 [42:36<1:20:31,  5.89s/it]
                                                    
{'loss': 0.55, 'learning_rate': 0.00015296745937313987, 'epoch': 1.03}

 34%|███▍      | 428/1248 [42:36<1:20:31,  5.89s/it]
 34%|███▍      | 429/1248 [42:42<1:20:39,  5.91s/it]
                                                    
{'loss': 0.5728, 'learning_rate': 0.00015274705780071152, 'epoch': 1.03}

 34%|███▍      | 429/1248 [42:42<1:20:39,  5.91s/it]
 34%|███▍      | 430/1248 [42:48<1:20:27,  5.90s/it]
                                                    
{'loss': 0.5647, 'learning_rate': 0.00015252630065673662, 'epoch': 1.03}

 34%|███▍      | 430/1248 [42:48<1:20:27,  5.90s/it]
 35%|███▍      | 431/1248 [42:54<1:20:45,  5.93s/it]
                                                    
{'loss': 0.5733, 'learning_rate': 0.00015230518942935421, 'epoch': 1.04}

 35%|███▍      | 431/1248 [42:54<1:20:45,  5.93s/it]
 35%|███▍      | 432/1248 [43:00<1:21:11,  5.97s/it]
                                                    
{'loss': 0.5739, 'learning_rate': 0.0001520837256090903, 'epoch': 1.04}

 35%|███▍      | 432/1248 [43:00<1:21:11,  5.97s/it]
 35%|███▍      | 433/1248 [43:06<1:20:30,  5.93s/it]
                                                    
{'loss': 0.6221, 'learning_rate': 0.00015186191068884775, 'epoch': 1.04}

 35%|███▍      | 433/1248 [43:06<1:20:30,  5.93s/it]
 35%|███▍      | 434/1248 [43:12<1:20:13,  5.91s/it]
                                                    
{'loss': 0.5973, 'learning_rate': 0.0001516397461638962, 'epoch': 1.04}

 35%|███▍      | 434/1248 [43:12<1:20:13,  5.91s/it]
 35%|███▍      | 435/1248 [43:18<1:20:16,  5.92s/it]
                                                    
{'loss': 0.5773, 'learning_rate': 0.00015141723353186202, 'epoch': 1.05}

 35%|███▍      | 435/1248 [43:18<1:20:16,  5.92s/it]
 35%|███▍      | 436/1248 [43:24<1:19:49,  5.90s/it]
                                                    
{'loss': 0.5695, 'learning_rate': 0.00015119437429271813, 'epoch': 1.05}

 35%|███▍      | 436/1248 [43:24<1:19:49,  5.90s/it]
 35%|███▌      | 437/1248 [43:30<1:19:27,  5.88s/it]
                                                    
{'loss': 0.5998, 'learning_rate': 0.00015097116994877404, 'epoch': 1.05}

 35%|███▌      | 437/1248 [43:30<1:19:27,  5.88s/it]
 35%|███▌      | 438/1248 [43:36<1:19:53,  5.92s/it]
                                                    
{'loss': 0.5724, 'learning_rate': 0.00015074762200466556, 'epoch': 1.05}

 35%|███▌      | 438/1248 [43:36<1:19:53,  5.92s/it]
 35%|███▌      | 439/1248 [43:42<1:20:18,  5.96s/it]
                                                    
{'loss': 0.5441, 'learning_rate': 0.00015052373196734484, 'epoch': 1.06}

 35%|███▌      | 439/1248 [43:42<1:20:18,  5.96s/it]
 35%|███▌      | 440/1248 [43:48<1:22:24,  6.12s/it]
                                                    
{'loss': 0.52, 'learning_rate': 0.00015029950134606992, 'epoch': 1.06}

 35%|███▌      | 440/1248 [43:48<1:22:24,  6.12s/it]
 35%|███▌      | 441/1248 [43:54<1:22:41,  6.15s/it]
                                                    
{'loss': 0.5605, 'learning_rate': 0.00015007493165239492, 'epoch': 1.06}

 35%|███▌      | 441/1248 [43:54<1:22:41,  6.15s/it]
 35%|███▌      | 442/1248 [44:00<1:21:45,  6.09s/it]
                                                    
{'loss': 0.5392, 'learning_rate': 0.00014985002440015958, 'epoch': 1.06}

 35%|███▌      | 442/1248 [44:00<1:21:45,  6.09s/it]
 35%|███▌      | 443/1248 [44:07<1:22:56,  6.18s/it]
                                                    
{'loss': 0.5445, 'learning_rate': 0.00014962478110547918, 'epoch': 1.06}

 35%|███▌      | 443/1248 [44:07<1:22:56,  6.18s/it]
 36%|███▌      | 444/1248 [44:13<1:21:34,  6.09s/it]
                                                    
{'loss': 0.5459, 'learning_rate': 0.00014939920328673422, 'epoch': 1.07}

 36%|███▌      | 444/1248 [44:13<1:21:34,  6.09s/it]
 36%|███▌      | 445/1248 [44:18<1:20:43,  6.03s/it]
                                                    
{'loss': 0.5699, 'learning_rate': 0.0001491732924645604, 'epoch': 1.07}

 36%|███▌      | 445/1248 [44:18<1:20:43,  6.03s/it]
 36%|███▌      | 446/1248 [44:24<1:19:51,  5.97s/it]
                                                    
{'loss': 0.5858, 'learning_rate': 0.00014894705016183803, 'epoch': 1.07}

 36%|███▌      | 446/1248 [44:24<1:19:51,  5.97s/it]
 36%|███▌      | 447/1248 [44:30<1:19:42,  5.97s/it]
                                                    
{'loss': 0.5595, 'learning_rate': 0.00014872047790368204, 'epoch': 1.07}

 36%|███▌      | 447/1248 [44:30<1:19:42,  5.97s/it]
 36%|███▌      | 448/1248 [44:36<1:20:29,  6.04s/it]
                                                    
{'loss': 0.5867, 'learning_rate': 0.00014849357721743168, 'epoch': 1.08}

 36%|███▌      | 448/1248 [44:36<1:20:29,  6.04s/it]
 36%|███▌      | 449/1248 [44:42<1:19:50,  6.00s/it]
                                                    
{'loss': 0.5823, 'learning_rate': 0.00014826634963264004, 'epoch': 1.08}

 36%|███▌      | 449/1248 [44:42<1:19:50,  6.00s/it]
 36%|███▌      | 450/1248 [44:48<1:18:46,  5.92s/it]
                                                    
{'loss': 0.5578, 'learning_rate': 0.00014803879668106394, 'epoch': 1.08}

 36%|███▌      | 450/1248 [44:48<1:18:46,  5.92s/it]
 36%|███▌      | 451/1248 [44:53<1:16:35,  5.77s/it]
                                                    
{'loss': 0.5899, 'learning_rate': 0.00014781091989665343, 'epoch': 1.08}

 36%|███▌      | 451/1248 [44:53<1:16:35,  5.77s/it]
 36%|███▌      | 452/1248 [44:59<1:16:24,  5.76s/it]
                                                    
{'loss': 0.5535, 'learning_rate': 0.00014758272081554167, 'epoch': 1.09}

 36%|███▌      | 452/1248 [44:59<1:16:24,  5.76s/it]
 36%|███▋      | 453/1248 [45:05<1:15:46,  5.72s/it]
                                                    
{'loss': 0.5844, 'learning_rate': 0.0001473542009760343, 'epoch': 1.09}

 36%|███▋      | 453/1248 [45:05<1:15:46,  5.72s/it]
 36%|███▋      | 454/1248 [45:11<1:17:28,  5.85s/it]
                                                    
{'loss': 0.5513, 'learning_rate': 0.00014712536191859932, 'epoch': 1.09}

 36%|███▋      | 454/1248 [45:11<1:17:28,  5.85s/it]
 36%|███▋      | 455/1248 [45:17<1:17:27,  5.86s/it]
                                                    
{'loss': 0.5793, 'learning_rate': 0.00014689620518585657, 'epoch': 1.09}

 36%|███▋      | 455/1248 [45:17<1:17:27,  5.86s/it]
 37%|███▋      | 456/1248 [45:23<1:17:22,  5.86s/it]
                                                    
{'loss': 0.5838, 'learning_rate': 0.00014666673232256738, 'epoch': 1.1}

 37%|███▋      | 456/1248 [45:23<1:17:22,  5.86s/it]
 37%|███▋      | 457/1248 [45:28<1:16:16,  5.79s/it]
                                                    
{'loss': 0.5777, 'learning_rate': 0.00014643694487562404, 'epoch': 1.1}

 37%|███▋      | 457/1248 [45:28<1:16:16,  5.79s/it]
 37%|███▋      | 458/1248 [45:34<1:16:04,  5.78s/it]
                                                    
{'loss': 0.5552, 'learning_rate': 0.00014620684439403962, 'epoch': 1.1}

 37%|███▋      | 458/1248 [45:34<1:16:04,  5.78s/it]
 37%|███▋      | 459/1248 [45:40<1:14:45,  5.69s/it]
                                                    
{'loss': 0.5407, 'learning_rate': 0.00014597643242893725, 'epoch': 1.1}

 37%|███▋      | 459/1248 [45:40<1:14:45,  5.69s/it]
 37%|███▋      | 460/1248 [45:45<1:14:41,  5.69s/it]
                                                    
{'loss': 0.5665, 'learning_rate': 0.00014574571053353988, 'epoch': 1.11}

 37%|███▋      | 460/1248 [45:45<1:14:41,  5.69s/it]
 37%|███▋      | 461/1248 [45:51<1:15:58,  5.79s/it]
                                                    
{'loss': 0.538, 'learning_rate': 0.00014551468026315962, 'epoch': 1.11}

 37%|███▋      | 461/1248 [45:51<1:15:58,  5.79s/it]
 37%|███▋      | 462/1248 [45:57<1:16:35,  5.85s/it]
                                                    
{'loss': 0.5862, 'learning_rate': 0.00014528334317518747, 'epoch': 1.11}

 37%|███▋      | 462/1248 [45:57<1:16:35,  5.85s/it]
 37%|███▋      | 463/1248 [46:03<1:16:33,  5.85s/it]
                                                    
{'loss': 0.5762, 'learning_rate': 0.0001450517008290827, 'epoch': 1.11}

 37%|███▋      | 463/1248 [46:03<1:16:33,  5.85s/it]
 37%|███▋      | 464/1248 [46:09<1:17:43,  5.95s/it]
                                                    
{'loss': 0.5566, 'learning_rate': 0.0001448197547863622, 'epoch': 1.12}

 37%|███▋      | 464/1248 [46:09<1:17:43,  5.95s/it]
 37%|███▋      | 465/1248 [46:15<1:16:23,  5.85s/it]
                                                    
{'loss': 0.5661, 'learning_rate': 0.00014458750661059034, 'epoch': 1.12}

 37%|███▋      | 465/1248 [46:15<1:16:23,  5.85s/it]
 37%|███▋      | 466/1248 [46:20<1:14:26,  5.71s/it]
                                                    
{'loss': 0.6448, 'learning_rate': 0.00014435495786736794, 'epoch': 1.12}

 37%|███▋      | 466/1248 [46:20<1:14:26,  5.71s/it]
 37%|███▋      | 467/1248 [46:26<1:15:28,  5.80s/it]
                                                    
{'loss': 0.5839, 'learning_rate': 0.00014412211012432212, 'epoch': 1.12}

 37%|███▋      | 467/1248 [46:26<1:15:28,  5.80s/it]
 38%|███▊      | 468/1248 [46:32<1:16:15,  5.87s/it]
                                                    
{'loss': 0.5553, 'learning_rate': 0.0001438889649510956, 'epoch': 1.12}

 38%|███▊      | 468/1248 [46:32<1:16:15,  5.87s/it]
 38%|███▊      | 469/1248 [46:38<1:15:57,  5.85s/it]
                                                    
{'loss': 0.5909, 'learning_rate': 0.0001436555239193359, 'epoch': 1.13}

 38%|███▊      | 469/1248 [46:38<1:15:57,  5.85s/it]
 38%|███▊      | 470/1248 [46:44<1:14:00,  5.71s/it]
                                                    
{'loss': 0.5701, 'learning_rate': 0.00014342178860268524, 'epoch': 1.13}

 38%|███▊      | 470/1248 [46:44<1:14:00,  5.71s/it]
 38%|███▊      | 471/1248 [46:49<1:14:34,  5.76s/it]
                                                    
{'loss': 0.5727, 'learning_rate': 0.00014318776057676934, 'epoch': 1.13}

 38%|███▊      | 471/1248 [46:49<1:14:34,  5.76s/it]
 38%|███▊      | 472/1248 [46:55<1:13:00,  5.65s/it]
                                                    
{'loss': 0.6375, 'learning_rate': 0.00014295344141918733, 'epoch': 1.13}

 38%|███▊      | 472/1248 [46:55<1:13:00,  5.65s/it]
 38%|███▊      | 473/1248 [47:01<1:13:32,  5.69s/it]
                                                    
{'loss': 0.554, 'learning_rate': 0.00014271883270950073, 'epoch': 1.14}

 38%|███▊      | 473/1248 [47:01<1:13:32,  5.69s/it]
 38%|███▊      | 474/1248 [47:06<1:14:06,  5.75s/it]
                                                    
{'loss': 0.5592, 'learning_rate': 0.000142483936029223, 'epoch': 1.14}

 38%|███▊      | 474/1248 [47:06<1:14:06,  5.75s/it]
 38%|███▊      | 475/1248 [47:12<1:13:57,  5.74s/it]
                                                    
{'loss': 0.5706, 'learning_rate': 0.0001422487529618088, 'epoch': 1.14}

 38%|███▊      | 475/1248 [47:12<1:13:57,  5.74s/it]
 38%|███▊      | 476/1248 [47:18<1:14:20,  5.78s/it]
                                                    
{'loss': 0.542, 'learning_rate': 0.0001420132850926434, 'epoch': 1.14}

 38%|███▊      | 476/1248 [47:18<1:14:20,  5.78s/it]
 38%|███▊      | 477/1248 [47:24<1:12:59,  5.68s/it]
                                                    
{'loss': 0.5689, 'learning_rate': 0.00014177753400903195, 'epoch': 1.15}

 38%|███▊      | 477/1248 [47:24<1:12:59,  5.68s/it]
 38%|███▊      | 478/1248 [47:29<1:13:22,  5.72s/it]
                                                    
{'loss': 0.5552, 'learning_rate': 0.00014154150130018866, 'epoch': 1.15}

 38%|███▊      | 478/1248 [47:29<1:13:22,  5.72s/it]
 38%|███▊      | 479/1248 [47:35<1:13:31,  5.74s/it]
                                                    
{'loss': 0.5629, 'learning_rate': 0.0001413051885572263, 'epoch': 1.15}

 38%|███▊      | 479/1248 [47:35<1:13:31,  5.74s/it]
 38%|███▊      | 480/1248 [47:41<1:13:36,  5.75s/it]
                                                    
{'loss': 0.5245, 'learning_rate': 0.0001410685973731453, 'epoch': 1.15}

 38%|███▊      | 480/1248 [47:41<1:13:36,  5.75s/it]
 39%|███▊      | 481/1248 [47:47<1:14:22,  5.82s/it]
                                                    
{'loss': 0.5395, 'learning_rate': 0.00014083172934282318, 'epoch': 1.16}

 39%|███▊      | 481/1248 [47:47<1:14:22,  5.82s/it]
 39%|███▊      | 482/1248 [47:52<1:13:21,  5.75s/it]
                                                    
{'loss': 0.5828, 'learning_rate': 0.00014059458606300356, 'epoch': 1.16}

 39%|███▊      | 482/1248 [47:52<1:13:21,  5.75s/it]
 39%|███▊      | 483/1248 [47:58<1:11:47,  5.63s/it]
                                                    
{'loss': 0.5313, 'learning_rate': 0.00014035716913228568, 'epoch': 1.16}

 39%|███▊      | 483/1248 [47:58<1:11:47,  5.63s/it]
 39%|███▉      | 484/1248 [48:04<1:13:34,  5.78s/it]
                                                    
{'loss': 0.5626, 'learning_rate': 0.00014011948015111333, 'epoch': 1.16}

 39%|███▉      | 484/1248 [48:04<1:13:34,  5.78s/it]
 39%|███▉      | 485/1248 [48:10<1:13:28,  5.78s/it]
                                                    
{'loss': 0.5704, 'learning_rate': 0.00013988152072176436, 'epoch': 1.17}

 39%|███▉      | 485/1248 [48:10<1:13:28,  5.78s/it]
 39%|███▉      | 486/1248 [48:15<1:13:20,  5.77s/it]
                                                    
{'loss': 0.5364, 'learning_rate': 0.0001396432924483396, 'epoch': 1.17}

 39%|███▉      | 486/1248 [48:15<1:13:20,  5.77s/it]
 39%|███▉      | 487/1248 [48:21<1:13:04,  5.76s/it]
                                                    
{'loss': 0.5619, 'learning_rate': 0.00013940479693675228, 'epoch': 1.17}

 39%|███▉      | 487/1248 [48:21<1:13:04,  5.76s/it]
 39%|███▉      | 488/1248 [48:28<1:15:11,  5.94s/it]
                                                    
{'loss': 0.5896, 'learning_rate': 0.00013916603579471705, 'epoch': 1.17}

 39%|███▉      | 488/1248 [48:28<1:15:11,  5.94s/it]
 39%|███▉      | 489/1248 [48:34<1:15:34,  5.97s/it]
                                                    
{'loss': 0.5306, 'learning_rate': 0.00013892701063173918, 'epoch': 1.18}

 39%|███▉      | 489/1248 [48:34<1:15:34,  5.97s/it]
 39%|███▉      | 490/1248 [48:39<1:14:38,  5.91s/it]
                                                    
{'loss': 0.5204, 'learning_rate': 0.00013868772305910377, 'epoch': 1.18}

 39%|███▉      | 490/1248 [48:39<1:14:38,  5.91s/it]
 39%|███▉      | 491/1248 [48:45<1:14:39,  5.92s/it]
                                                    
{'loss': 0.5811, 'learning_rate': 0.00013844817468986476, 'epoch': 1.18}

 39%|███▉      | 491/1248 [48:45<1:14:39,  5.92s/it]
 39%|███▉      | 492/1248 [48:51<1:13:15,  5.81s/it]
                                                    
{'loss': 0.5498, 'learning_rate': 0.00013820836713883422, 'epoch': 1.18}

 39%|███▉      | 492/1248 [48:51<1:13:15,  5.81s/it]
 40%|███▉      | 493/1248 [48:57<1:15:46,  6.02s/it]
                                                    
{'loss': 0.5666, 'learning_rate': 0.0001379683020225714, 'epoch': 1.19}

 40%|███▉      | 493/1248 [48:57<1:15:46,  6.02s/it]
 40%|███▉      | 494/1248 [49:03<1:15:22,  6.00s/it]
                                                    
{'loss': 0.5549, 'learning_rate': 0.0001377279809593717, 'epoch': 1.19}

 40%|███▉      | 494/1248 [49:03<1:15:22,  6.00s/it]
 40%|███▉      | 495/1248 [49:09<1:15:35,  6.02s/it]
                                                    
{'loss': 0.5501, 'learning_rate': 0.00013748740556925607, 'epoch': 1.19}

 40%|███▉      | 495/1248 [49:09<1:15:35,  6.02s/it]
 40%|███▉      | 496/1248 [49:15<1:14:37,  5.95s/it]
                                                    
{'loss': 0.5524, 'learning_rate': 0.00013724657747395957, 'epoch': 1.19}

 40%|███▉      | 496/1248 [49:15<1:14:37,  5.95s/it]
 40%|███▉      | 497/1248 [49:21<1:13:42,  5.89s/it]
                                                    
{'loss': 0.5661, 'learning_rate': 0.00013700549829692116, 'epoch': 1.19}

 40%|███▉      | 497/1248 [49:21<1:13:42,  5.89s/it]
 40%|███▉      | 498/1248 [49:27<1:13:21,  5.87s/it]
                                                    
{'loss': 0.553, 'learning_rate': 0.000136764169663272, 'epoch': 1.2}

 40%|███▉      | 498/1248 [49:27<1:13:21,  5.87s/it]
 40%|███▉      | 499/1248 [49:33<1:12:56,  5.84s/it]
                                                    
{'loss': 0.5201, 'learning_rate': 0.00013652259319982518, 'epoch': 1.2}

 40%|███▉      | 499/1248 [49:33<1:12:56,  5.84s/it]
 40%|████      | 500/1248 [49:39<1:13:42,  5.91s/it]
                                                    
{'loss': 0.5978, 'learning_rate': 0.0001362807705350641, 'epoch': 1.2}

 40%|████      | 500/1248 [49:39<1:13:42,  5.91s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 40%|████      | 501/1248 [50:05<2:30:17, 12.07s/it]
                                                    
{'loss': 0.5702, 'learning_rate': 0.00013603870329913212, 'epoch': 1.2}

 40%|████      | 501/1248 [50:05<2:30:17, 12.07s/it]
 40%|████      | 502/1248 [50:11<2:05:43, 10.11s/it]
                                                    
{'loss': 0.5578, 'learning_rate': 0.00013579639312382105, 'epoch': 1.21}

 40%|████      | 502/1248 [50:11<2:05:43, 10.11s/it]
 40%|████      | 503/1248 [50:16<1:47:41,  8.67s/it]
                                                    
{'loss': 0.6488, 'learning_rate': 0.00013555384164256048, 'epoch': 1.21}

 40%|████      | 503/1248 [50:16<1:47:41,  8.67s/it]
 40%|████      | 504/1248 [50:21<1:34:53,  7.65s/it]
                                                    
{'loss': 0.5617, 'learning_rate': 0.00013531105049040666, 'epoch': 1.21}

 40%|████      | 504/1248 [50:21<1:34:53,  7.65s/it]
 40%|████      | 505/1248 [50:26<1:25:04,  6.87s/it]
                                                    
{'loss': 0.5553, 'learning_rate': 0.00013506802130403144, 'epoch': 1.21}

 40%|████      | 505/1248 [50:26<1:25:04,  6.87s/it]
 41%|████      | 506/1248 [50:31<1:18:24,  6.34s/it]
                                                    
{'loss': 0.5314, 'learning_rate': 0.0001348247557217113, 'epoch': 1.22}

 41%|████      | 506/1248 [50:31<1:18:24,  6.34s/it]
 41%|████      | 507/1248 [50:37<1:14:07,  6.00s/it]
                                                    
{'loss': 0.5416, 'learning_rate': 0.00013458125538331626, 'epoch': 1.22}

 41%|████      | 507/1248 [50:37<1:14:07,  6.00s/it]
 41%|████      | 508/1248 [50:42<1:12:21,  5.87s/it]
                                                    
{'loss': 0.5344, 'learning_rate': 0.00013433752193029886, 'epoch': 1.22}

 41%|████      | 508/1248 [50:42<1:12:21,  5.87s/it]
 41%|████      | 509/1248 [50:47<1:08:47,  5.59s/it]
                                                    
{'loss': 0.5263, 'learning_rate': 0.00013409355700568306, 'epoch': 1.22}

 41%|████      | 509/1248 [50:47<1:08:47,  5.59s/it]
 41%|████      | 510/1248 [50:53<1:09:43,  5.67s/it]
                                                    
{'loss': 0.5763, 'learning_rate': 0.00013384936225405326, 'epoch': 1.23}

 41%|████      | 510/1248 [50:53<1:09:43,  5.67s/it]
 41%|████      | 511/1248 [50:59<1:09:42,  5.68s/it]
                                                    
{'loss': 0.5353, 'learning_rate': 0.00013360493932154302, 'epoch': 1.23}

 41%|████      | 511/1248 [50:59<1:09:42,  5.68s/it]
 41%|████      | 512/1248 [51:04<1:10:12,  5.72s/it]
                                                    
{'loss': 0.5442, 'learning_rate': 0.0001333602898558242, 'epoch': 1.23}

 41%|████      | 512/1248 [51:04<1:10:12,  5.72s/it]
 41%|████      | 513/1248 [51:10<1:10:07,  5.72s/it]
                                                    
{'loss': 0.5787, 'learning_rate': 0.00013311541550609565, 'epoch': 1.23}

 41%|████      | 513/1248 [51:10<1:10:07,  5.72s/it]
 41%|████      | 514/1248 [51:16<1:10:14,  5.74s/it]
                                                    
{'loss': 0.5471, 'learning_rate': 0.00013287031792307225, 'epoch': 1.24}

 41%|████      | 514/1248 [51:16<1:10:14,  5.74s/it]
 41%|████▏     | 515/1248 [51:22<1:10:09,  5.74s/it]
                                                    
{'loss': 0.5441, 'learning_rate': 0.00013262499875897366, 'epoch': 1.24}

 41%|████▏     | 515/1248 [51:22<1:10:09,  5.74s/it]
 41%|████▏     | 516/1248 [51:28<1:10:46,  5.80s/it]
                                                    
{'loss': 0.5521, 'learning_rate': 0.0001323794596675132, 'epoch': 1.24}

 41%|████▏     | 516/1248 [51:28<1:10:46,  5.80s/it]
 41%|████▏     | 517/1248 [51:33<1:09:53,  5.74s/it]
                                                    
{'loss': 0.5135, 'learning_rate': 0.00013213370230388683, 'epoch': 1.24}

 41%|████▏     | 517/1248 [51:33<1:09:53,  5.74s/it]
 42%|████▏     | 518/1248 [51:39<1:10:39,  5.81s/it]
                                                    
{'loss': 0.5896, 'learning_rate': 0.00013188772832476188, 'epoch': 1.25}

 42%|████▏     | 518/1248 [51:39<1:10:39,  5.81s/it]
 42%|████▏     | 519/1248 [51:45<1:11:20,  5.87s/it]
                                                    
{'loss': 0.5512, 'learning_rate': 0.00013164153938826582, 'epoch': 1.25}

 42%|████▏     | 519/1248 [51:45<1:11:20,  5.87s/it]
 42%|████▏     | 520/1248 [51:51<1:12:29,  5.98s/it]
                                                    
{'loss': 0.553, 'learning_rate': 0.00013139513715397521, 'epoch': 1.25}

 42%|████▏     | 520/1248 [51:51<1:12:29,  5.98s/it]
 42%|████▏     | 521/1248 [51:58<1:13:50,  6.09s/it]
                                                    
{'loss': 0.5654, 'learning_rate': 0.00013114852328290451, 'epoch': 1.25}

 42%|████▏     | 521/1248 [51:58<1:13:50,  6.09s/it]
 42%|████▏     | 522/1248 [52:04<1:13:19,  6.06s/it]
                                                    
{'loss': 0.542, 'learning_rate': 0.00013090169943749476, 'epoch': 1.25}

 42%|████▏     | 522/1248 [52:04<1:13:19,  6.06s/it]
 42%|████▏     | 523/1248 [52:10<1:12:42,  6.02s/it]
                                                    
{'loss': 0.5402, 'learning_rate': 0.00013065466728160252, 'epoch': 1.26}

 42%|████▏     | 523/1248 [52:10<1:12:42,  6.02s/it]
 42%|████▏     | 524/1248 [52:16<1:13:03,  6.05s/it]
                                                    
{'loss': 0.5816, 'learning_rate': 0.0001304074284804885, 'epoch': 1.26}

 42%|████▏     | 524/1248 [52:16<1:13:03,  6.05s/it]
 42%|████▏     | 525/1248 [52:22<1:12:20,  6.00s/it]
                                                    
{'loss': 0.5484, 'learning_rate': 0.00013015998470080654, 'epoch': 1.26}

 42%|████▏     | 525/1248 [52:22<1:12:20,  6.00s/it]
 42%|████▏     | 526/1248 [52:27<1:10:40,  5.87s/it]
                                                    
{'loss': 0.5357, 'learning_rate': 0.00012991233761059214, 'epoch': 1.26}

 42%|████▏     | 526/1248 [52:27<1:10:40,  5.87s/it]
 42%|████▏     | 527/1248 [52:34<1:12:59,  6.07s/it]
                                                    
{'loss': 0.5819, 'learning_rate': 0.00012966448887925138, 'epoch': 1.27}

 42%|████▏     | 527/1248 [52:34<1:12:59,  6.07s/it]
 42%|████▏     | 528/1248 [52:40<1:12:54,  6.08s/it]
                                                    
{'loss': 0.5947, 'learning_rate': 0.00012941644017754964, 'epoch': 1.27}

 42%|████▏     | 528/1248 [52:40<1:12:54,  6.08s/it]
 42%|████▏     | 529/1248 [52:46<1:11:25,  5.96s/it]
                                                    
{'loss': 0.5477, 'learning_rate': 0.00012916819317760028, 'epoch': 1.27}

 42%|████▏     | 529/1248 [52:46<1:11:25,  5.96s/it]
 42%|████▏     | 530/1248 [52:51<1:10:50,  5.92s/it]
                                                    
{'loss': 0.5811, 'learning_rate': 0.0001289197495528534, 'epoch': 1.27}

 42%|████▏     | 530/1248 [52:51<1:10:50,  5.92s/it]
 43%|████▎     | 531/1248 [52:57<1:09:21,  5.80s/it]
                                                    
{'loss': 0.5636, 'learning_rate': 0.00012867111097808457, 'epoch': 1.28}

 43%|████▎     | 531/1248 [52:57<1:09:21,  5.80s/it]
 43%|████▎     | 532/1248 [53:03<1:09:31,  5.83s/it]
                                                    
{'loss': 0.5382, 'learning_rate': 0.00012842227912938359, 'epoch': 1.28}

 43%|████▎     | 532/1248 [53:03<1:09:31,  5.83s/it]
 43%|████▎     | 533/1248 [53:09<1:09:18,  5.82s/it]
                                                    
{'loss': 0.5271, 'learning_rate': 0.00012817325568414297, 'epoch': 1.28}

 43%|████▎     | 533/1248 [53:09<1:09:18,  5.82s/it]
 43%|████▎     | 534/1248 [53:14<1:08:55,  5.79s/it]
                                                    
{'loss': 0.5341, 'learning_rate': 0.00012792404232104697, 'epoch': 1.28}

 43%|████▎     | 534/1248 [53:14<1:08:55,  5.79s/it]
 43%|████▎     | 535/1248 [53:20<1:09:07,  5.82s/it]
                                                    
{'loss': 0.5848, 'learning_rate': 0.00012767464072006, 'epoch': 1.29}

 43%|████▎     | 535/1248 [53:20<1:09:07,  5.82s/it]
 43%|████▎     | 536/1248 [53:26<1:08:10,  5.75s/it]
                                                    
{'loss': 0.552, 'learning_rate': 0.00012742505256241543, 'epoch': 1.29}

 43%|████▎     | 536/1248 [53:26<1:08:10,  5.75s/it]
 43%|████▎     | 537/1248 [53:32<1:09:18,  5.85s/it]
                                                    
{'loss': 0.515, 'learning_rate': 0.00012717527953060416, 'epoch': 1.29}

 43%|████▎     | 537/1248 [53:32<1:09:18,  5.85s/it]
 43%|████▎     | 538/1248 [53:38<1:08:34,  5.79s/it]
                                                    
{'loss': 0.4876, 'learning_rate': 0.00012692532330836346, 'epoch': 1.29}

 43%|████▎     | 538/1248 [53:38<1:08:34,  5.79s/it]
 43%|████▎     | 539/1248 [53:43<1:08:07,  5.77s/it]
                                                    
{'loss': 0.555, 'learning_rate': 0.00012667518558066537, 'epoch': 1.3}

 43%|████▎     | 539/1248 [53:43<1:08:07,  5.77s/it]
 43%|████▎     | 540/1248 [53:50<1:10:03,  5.94s/it]
                                                    
{'loss': 0.5637, 'learning_rate': 0.00012642486803370552, 'epoch': 1.3}

 43%|████▎     | 540/1248 [53:50<1:10:03,  5.94s/it]
 43%|████▎     | 541/1248 [53:55<1:09:31,  5.90s/it]
                                                    
{'loss': 0.5986, 'learning_rate': 0.00012617437235489177, 'epoch': 1.3}

 43%|████▎     | 541/1248 [53:55<1:09:31,  5.90s/it]
 43%|████▎     | 542/1248 [54:01<1:08:09,  5.79s/it]
                                                    
{'loss': 0.5467, 'learning_rate': 0.0001259237002328327, 'epoch': 1.3}

 43%|████▎     | 542/1248 [54:01<1:08:09,  5.79s/it]
 44%|████▎     | 543/1248 [54:07<1:07:57,  5.78s/it]
                                                    
{'loss': 0.5322, 'learning_rate': 0.00012567285335732633, 'epoch': 1.31}

 44%|████▎     | 543/1248 [54:07<1:07:57,  5.78s/it]
 44%|████▎     | 544/1248 [54:13<1:08:17,  5.82s/it]
                                                    
{'loss': 0.5225, 'learning_rate': 0.00012542183341934872, 'epoch': 1.31}

 44%|████▎     | 544/1248 [54:13<1:08:17,  5.82s/it]
 44%|████▎     | 545/1248 [54:19<1:08:22,  5.84s/it]
                                                    
{'loss': 0.5503, 'learning_rate': 0.00012517064211104253, 'epoch': 1.31}

 44%|████▎     | 545/1248 [54:19<1:08:22,  5.84s/it]
 44%|████▍     | 546/1248 [54:24<1:08:20,  5.84s/it]
                                                    
{'loss': 0.5568, 'learning_rate': 0.00012491928112570567, 'epoch': 1.31}

 44%|████▍     | 546/1248 [54:24<1:08:20,  5.84s/it]
 44%|████▍     | 547/1248 [54:30<1:07:24,  5.77s/it]
                                                    
{'loss': 0.5122, 'learning_rate': 0.00012466775215777987, 'epoch': 1.31}

 44%|████▍     | 547/1248 [54:30<1:07:24,  5.77s/it]
 44%|████▍     | 548/1248 [54:36<1:07:32,  5.79s/it]
                                                    
{'loss': 0.5571, 'learning_rate': 0.00012441605690283915, 'epoch': 1.32}

 44%|████▍     | 548/1248 [54:36<1:07:32,  5.79s/it]
 44%|████▍     | 549/1248 [54:41<1:06:39,  5.72s/it]
                                                    
{'loss': 0.5471, 'learning_rate': 0.00012416419705757857, 'epoch': 1.32}

 44%|████▍     | 549/1248 [54:41<1:06:39,  5.72s/it]
 44%|████▍     | 550/1248 [54:47<1:07:28,  5.80s/it]
                                                    
{'loss': 0.5488, 'learning_rate': 0.00012391217431980274, 'epoch': 1.32}

 44%|████▍     | 550/1248 [54:47<1:07:28,  5.80s/it]
 44%|████▍     | 551/1248 [54:53<1:06:34,  5.73s/it]
                                                    
{'loss': 0.5242, 'learning_rate': 0.00012365999038841419, 'epoch': 1.32}

 44%|████▍     | 551/1248 [54:53<1:06:34,  5.73s/it]
 44%|████▍     | 552/1248 [54:59<1:07:29,  5.82s/it]
                                                    
{'loss': 0.5364, 'learning_rate': 0.0001234076469634022, 'epoch': 1.33}

 44%|████▍     | 552/1248 [54:59<1:07:29,  5.82s/it]
 44%|████▍     | 553/1248 [55:05<1:06:49,  5.77s/it]
                                                    
{'loss': 0.5348, 'learning_rate': 0.00012315514574583113, 'epoch': 1.33}

 44%|████▍     | 553/1248 [55:05<1:06:49,  5.77s/it]
 44%|████▍     | 554/1248 [55:10<1:06:54,  5.78s/it]
                                                    
{'loss': 0.5793, 'learning_rate': 0.00012290248843782915, 'epoch': 1.33}

 44%|████▍     | 554/1248 [55:10<1:06:54,  5.78s/it]
 44%|████▍     | 555/1248 [55:16<1:07:09,  5.81s/it]
                                                    
{'loss': 0.5986, 'learning_rate': 0.00012264967674257646, 'epoch': 1.33}

 44%|████▍     | 555/1248 [55:16<1:07:09,  5.81s/it]
 45%|████▍     | 556/1248 [55:23<1:09:35,  6.03s/it]
                                                    
{'loss': 0.5801, 'learning_rate': 0.00012239671236429414, 'epoch': 1.34}

 45%|████▍     | 556/1248 [55:23<1:09:35,  6.03s/it]
 45%|████▍     | 557/1248 [55:29<1:09:16,  6.01s/it]
                                                    
{'loss': 0.5842, 'learning_rate': 0.00012214359700823247, 'epoch': 1.34}

 45%|████▍     | 557/1248 [55:29<1:09:16,  6.01s/it]
 45%|████▍     | 558/1248 [55:34<1:07:43,  5.89s/it]
                                                    
{'loss': 0.5615, 'learning_rate': 0.0001218903323806595, 'epoch': 1.34}

 45%|████▍     | 558/1248 [55:34<1:07:43,  5.89s/it]
 45%|████▍     | 559/1248 [55:40<1:07:31,  5.88s/it]
                                                    
{'loss': 0.5227, 'learning_rate': 0.00012163692018884947, 'epoch': 1.34}

 45%|████▍     | 559/1248 [55:40<1:07:31,  5.88s/it]
 45%|████▍     | 560/1248 [55:46<1:08:06,  5.94s/it]
                                                    
{'loss': 0.5398, 'learning_rate': 0.00012138336214107147, 'epoch': 1.35}

 45%|████▍     | 560/1248 [55:46<1:08:06,  5.94s/it]
 45%|████▍     | 561/1248 [55:53<1:09:30,  6.07s/it]
                                                    
{'loss': 0.5567, 'learning_rate': 0.00012112965994657768, 'epoch': 1.35}

 45%|████▍     | 561/1248 [55:53<1:09:30,  6.07s/it]
 45%|████▌     | 562/1248 [55:58<1:08:15,  5.97s/it]
                                                    
{'loss': 0.5385, 'learning_rate': 0.00012087581531559207, 'epoch': 1.35}

 45%|████▌     | 562/1248 [55:58<1:08:15,  5.97s/it]
 45%|████▌     | 563/1248 [56:04<1:07:50,  5.94s/it]
                                                    
{'loss': 0.5147, 'learning_rate': 0.00012062182995929882, 'epoch': 1.35}

 45%|████▌     | 563/1248 [56:04<1:07:50,  5.94s/it]
 45%|████▌     | 564/1248 [56:10<1:07:55,  5.96s/it]
                                                    
{'loss': 0.5458, 'learning_rate': 0.00012036770558983066, 'epoch': 1.36}

 45%|████▌     | 564/1248 [56:10<1:07:55,  5.96s/it]
 45%|████▌     | 565/1248 [56:16<1:07:34,  5.94s/it]
                                                    
{'loss': 0.4938, 'learning_rate': 0.00012011344392025741, 'epoch': 1.36}

 45%|████▌     | 565/1248 [56:16<1:07:34,  5.94s/it]
 45%|████▌     | 566/1248 [56:22<1:07:36,  5.95s/it]
                                                    
{'loss': 0.5161, 'learning_rate': 0.00011985904666457455, 'epoch': 1.36}

 45%|████▌     | 566/1248 [56:22<1:07:36,  5.95s/it]
 45%|████▌     | 567/1248 [56:28<1:06:52,  5.89s/it]
                                                    
{'loss': 0.5089, 'learning_rate': 0.00011960451553769145, 'epoch': 1.36}

 45%|████▌     | 567/1248 [56:28<1:06:52,  5.89s/it]
 46%|████▌     | 568/1248 [56:34<1:06:16,  5.85s/it]
                                                    
{'loss': 0.5347, 'learning_rate': 0.00011934985225541998, 'epoch': 1.37}

 46%|████▌     | 568/1248 [56:34<1:06:16,  5.85s/it]
 46%|████▌     | 569/1248 [56:40<1:06:13,  5.85s/it]
                                                    
{'loss': 0.6665, 'learning_rate': 0.00011909505853446281, 'epoch': 1.37}

 46%|████▌     | 569/1248 [56:40<1:06:13,  5.85s/it]
 46%|████▌     | 570/1248 [56:45<1:06:07,  5.85s/it]
                                                    
{'loss': 0.5709, 'learning_rate': 0.00011884013609240199, 'epoch': 1.37}

 46%|████▌     | 570/1248 [56:45<1:06:07,  5.85s/it]
 46%|████▌     | 571/1248 [56:51<1:06:46,  5.92s/it]
                                                    
{'loss': 0.5883, 'learning_rate': 0.0001185850866476872, 'epoch': 1.37}

 46%|████▌     | 571/1248 [56:51<1:06:46,  5.92s/it]
 46%|████▌     | 572/1248 [56:57<1:06:33,  5.91s/it]
                                                    
{'loss': 0.5915, 'learning_rate': 0.00011832991191962435, 'epoch': 1.38}

 46%|████▌     | 572/1248 [56:57<1:06:33,  5.91s/it]
 46%|████▌     | 573/1248 [57:03<1:06:09,  5.88s/it]
                                                    
{'loss': 0.5294, 'learning_rate': 0.0001180746136283638, 'epoch': 1.38}

 46%|████▌     | 573/1248 [57:03<1:06:09,  5.88s/it]
 46%|████▌     | 574/1248 [57:09<1:06:24,  5.91s/it]
                                                    
{'loss': 0.5104, 'learning_rate': 0.00011781919349488895, 'epoch': 1.38}

 46%|████▌     | 574/1248 [57:09<1:06:24,  5.91s/it]
 46%|████▌     | 575/1248 [57:15<1:05:39,  5.85s/it]
                                                    
{'loss': 0.5632, 'learning_rate': 0.00011756365324100445, 'epoch': 1.38}

 46%|████▌     | 575/1248 [57:15<1:05:39,  5.85s/it]
 46%|████▌     | 576/1248 [57:21<1:06:37,  5.95s/it]
                                                    
{'loss': 0.555, 'learning_rate': 0.00011730799458932474, 'epoch': 1.38}

 46%|████▌     | 576/1248 [57:21<1:06:37,  5.95s/it]
 46%|████▌     | 577/1248 [57:27<1:05:38,  5.87s/it]
                                                    
{'loss': 0.5403, 'learning_rate': 0.0001170522192632624, 'epoch': 1.39}

 46%|████▌     | 577/1248 [57:27<1:05:38,  5.87s/it]
 46%|████▋     | 578/1248 [57:33<1:05:59,  5.91s/it]
                                                    
{'loss': 0.5778, 'learning_rate': 0.00011679632898701649, 'epoch': 1.39}

 46%|████▋     | 578/1248 [57:33<1:05:59,  5.91s/it]
 46%|████▋     | 579/1248 [57:38<1:04:39,  5.80s/it]
                                                    
{'loss': 0.5593, 'learning_rate': 0.00011654032548556102, 'epoch': 1.39}

 46%|████▋     | 579/1248 [57:38<1:04:39,  5.80s/it]
 46%|████▋     | 580/1248 [57:45<1:06:42,  5.99s/it]
                                                    
{'loss': 0.5031, 'learning_rate': 0.00011628421048463314, 'epoch': 1.39}

 46%|████▋     | 580/1248 [57:45<1:06:42,  5.99s/it]
 47%|████▋     | 581/1248 [57:51<1:07:10,  6.04s/it]
                                                    
{'loss': 0.538, 'learning_rate': 0.00011602798571072175, 'epoch': 1.4}

 47%|████▋     | 581/1248 [57:51<1:07:10,  6.04s/it]
 47%|████▋     | 582/1248 [57:57<1:06:31,  5.99s/it]
                                                    
{'loss': 0.5518, 'learning_rate': 0.00011577165289105565, 'epoch': 1.4}

 47%|████▋     | 582/1248 [57:57<1:06:31,  5.99s/it]
 47%|████▋     | 583/1248 [58:03<1:06:46,  6.02s/it]
                                                    
{'loss': 0.5423, 'learning_rate': 0.00011551521375359206, 'epoch': 1.4}

 47%|████▋     | 583/1248 [58:03<1:06:46,  6.02s/it]
 47%|████▋     | 584/1248 [58:09<1:06:20,  6.00s/it]
                                                    
{'loss': 0.5722, 'learning_rate': 0.00011525867002700484, 'epoch': 1.4}

 47%|████▋     | 584/1248 [58:09<1:06:20,  6.00s/it]
 47%|████▋     | 585/1248 [58:15<1:05:48,  5.96s/it]
                                                    
{'loss': 0.536, 'learning_rate': 0.00011500202344067286, 'epoch': 1.41}

 47%|████▋     | 585/1248 [58:15<1:05:48,  5.96s/it]
 47%|████▋     | 586/1248 [58:21<1:05:36,  5.95s/it]
                                                    
{'loss': 0.5657, 'learning_rate': 0.00011474527572466847, 'epoch': 1.41}

 47%|████▋     | 586/1248 [58:21<1:05:36,  5.95s/it]
 47%|████▋     | 587/1248 [58:27<1:06:16,  6.02s/it]
                                                    
{'loss': 0.5385, 'learning_rate': 0.00011448842860974564, 'epoch': 1.41}

 47%|████▋     | 587/1248 [58:27<1:06:16,  6.02s/it]
 47%|████▋     | 588/1248 [58:33<1:05:26,  5.95s/it]
                                                    
{'loss': 0.6045, 'learning_rate': 0.00011423148382732853, 'epoch': 1.41}

 47%|████▋     | 588/1248 [58:33<1:05:26,  5.95s/it]
 47%|████▋     | 589/1248 [58:39<1:06:19,  6.04s/it]
                                                    
{'loss': 0.5394, 'learning_rate': 0.00011397444310949954, 'epoch': 1.42}

 47%|████▋     | 589/1248 [58:39<1:06:19,  6.04s/it]
 47%|████▋     | 590/1248 [58:45<1:05:35,  5.98s/it]
                                                    
{'loss': 0.5824, 'learning_rate': 0.00011371730818898785, 'epoch': 1.42}

 47%|████▋     | 590/1248 [58:45<1:05:35,  5.98s/it]
 47%|████▋     | 591/1248 [58:51<1:05:14,  5.96s/it]
                                                    
{'loss': 0.5885, 'learning_rate': 0.00011346008079915764, 'epoch': 1.42}

 47%|████▋     | 591/1248 [58:51<1:05:14,  5.96s/it]
 47%|████▋     | 592/1248 [58:57<1:05:44,  6.01s/it]
                                                    
{'loss': 0.5701, 'learning_rate': 0.0001132027626739965, 'epoch': 1.42}

 47%|████▋     | 592/1248 [58:57<1:05:44,  6.01s/it]
 48%|████▊     | 593/1248 [59:03<1:07:26,  6.18s/it]
                                                    
{'loss': 0.5018, 'learning_rate': 0.00011294535554810354, 'epoch': 1.43}

 48%|████▊     | 593/1248 [59:03<1:07:26,  6.18s/it]
 48%|████▊     | 594/1248 [59:09<1:06:37,  6.11s/it]
                                                    
{'loss': 0.5265, 'learning_rate': 0.00011268786115667798, 'epoch': 1.43}

 48%|████▊     | 594/1248 [59:09<1:06:37,  6.11s/it]
 48%|████▊     | 595/1248 [59:15<1:06:11,  6.08s/it]
                                                    
{'loss': 0.5213, 'learning_rate': 0.0001124302812355072, 'epoch': 1.43}

 48%|████▊     | 595/1248 [59:15<1:06:11,  6.08s/it]
 48%|████▊     | 596/1248 [59:21<1:05:24,  6.02s/it]
                                                    
{'loss': 0.5354, 'learning_rate': 0.00011217261752095518, 'epoch': 1.43}

 48%|████▊     | 596/1248 [59:21<1:05:24,  6.02s/it]
 48%|████▊     | 597/1248 [59:27<1:06:17,  6.11s/it]
                                                    
{'loss': 0.5632, 'learning_rate': 0.00011191487174995079, 'epoch': 1.44}

 48%|████▊     | 597/1248 [59:27<1:06:17,  6.11s/it]
 48%|████▊     | 598/1248 [59:33<1:05:02,  6.00s/it]
                                                    
{'loss': 0.5421, 'learning_rate': 0.00011165704565997593, 'epoch': 1.44}

 48%|████▊     | 598/1248 [59:33<1:05:02,  6.00s/it]
 48%|████▊     | 599/1248 [59:39<1:05:25,  6.05s/it]
                                                    
{'loss': 0.6741, 'learning_rate': 0.00011139914098905406, 'epoch': 1.44}

 48%|████▊     | 599/1248 [59:39<1:05:25,  6.05s/it]
 48%|████▊     | 600/1248 [59:45<1:04:37,  5.98s/it]
                                                    
{'loss': 0.5066, 'learning_rate': 0.00011114115947573833, 'epoch': 1.44}

 48%|████▊     | 600/1248 [59:45<1:04:37,  5.98s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 48%|████▊     | 601/1248 [1:00:11<2:07:50, 11.86s/it]
                                                      
{'loss': 0.5716, 'learning_rate': 0.00011088310285909986, 'epoch': 1.44}

 48%|████▊     | 601/1248 [1:00:11<2:07:50, 11.86s/it]
 48%|████▊     | 602/1248 [1:00:16<1:47:26,  9.98s/it]
                                                      
{'loss': 0.5342, 'learning_rate': 0.00011062497287871605, 'epoch': 1.45}

 48%|████▊     | 602/1248 [1:00:16<1:47:26,  9.98s/it]
 48%|████▊     | 603/1248 [1:00:22<1:34:04,  8.75s/it]
                                                      
{'loss': 0.5593, 'learning_rate': 0.00011036677127465889, 'epoch': 1.45}

 48%|████▊     | 603/1248 [1:00:22<1:34:04,  8.75s/it]
 48%|████▊     | 604/1248 [1:00:28<1:25:04,  7.93s/it]
                                                      
{'loss': 0.5777, 'learning_rate': 0.00011010849978748314, 'epoch': 1.45}

 48%|████▊     | 604/1248 [1:00:28<1:25:04,  7.93s/it]
 48%|████▊     | 605/1248 [1:00:34<1:18:43,  7.35s/it]
                                                      
{'loss': 0.5331, 'learning_rate': 0.00010985016015821465, 'epoch': 1.45}

 48%|████▊     | 605/1248 [1:00:34<1:18:43,  7.35s/it]
 49%|████▊     | 606/1248 [1:00:40<1:13:49,  6.90s/it]
                                                      
{'loss': 0.5734, 'learning_rate': 0.00010959175412833869, 'epoch': 1.46}

 49%|████▊     | 606/1248 [1:00:40<1:13:49,  6.90s/it]
 49%|████▊     | 607/1248 [1:00:46<1:09:03,  6.46s/it]
                                                      
{'loss': 0.5309, 'learning_rate': 0.00010933328343978804, 'epoch': 1.46}

 49%|████▊     | 607/1248 [1:00:46<1:09:03,  6.46s/it]
 49%|████▊     | 608/1248 [1:00:51<1:06:37,  6.25s/it]
                                                      
{'loss': 0.5464, 'learning_rate': 0.00010907474983493144, 'epoch': 1.46}

 49%|████▊     | 608/1248 [1:00:51<1:06:37,  6.25s/it]
 49%|████▉     | 609/1248 [1:00:58<1:07:23,  6.33s/it]
                                                      
{'loss': 0.5359, 'learning_rate': 0.00010881615505656169, 'epoch': 1.46}

 49%|████▉     | 609/1248 [1:00:58<1:07:23,  6.33s/it]
 49%|████▉     | 610/1248 [1:01:04<1:05:32,  6.16s/it]
                                                      
{'loss': 0.5527, 'learning_rate': 0.00010855750084788398, 'epoch': 1.47}

 49%|████▉     | 610/1248 [1:01:04<1:05:32,  6.16s/it]
 49%|████▉     | 611/1248 [1:01:09<1:04:11,  6.05s/it]
                                                      
{'loss': 0.5464, 'learning_rate': 0.00010829878895250416, 'epoch': 1.47}

 49%|████▉     | 611/1248 [1:01:09<1:04:11,  6.05s/it]
 49%|████▉     | 612/1248 [1:01:15<1:02:41,  5.91s/it]
                                                      
{'loss': 0.5475, 'learning_rate': 0.00010804002111441689, 'epoch': 1.47}

 49%|████▉     | 612/1248 [1:01:15<1:02:41,  5.91s/it]
 49%|████▉     | 613/1248 [1:01:20<1:00:39,  5.73s/it]
                                                      
{'loss': 0.5444, 'learning_rate': 0.00010778119907799398, 'epoch': 1.47}

 49%|████▉     | 613/1248 [1:01:20<1:00:39,  5.73s/it]
 49%|████▉     | 614/1248 [1:01:26<1:01:54,  5.86s/it]
                                                      
{'loss': 0.5852, 'learning_rate': 0.00010752232458797262, 'epoch': 1.48}

 49%|████▉     | 614/1248 [1:01:26<1:01:54,  5.86s/it]
 49%|████▉     | 615/1248 [1:01:32<1:02:14,  5.90s/it]
                                                      
{'loss': 0.567, 'learning_rate': 0.00010726339938944355, 'epoch': 1.48}

 49%|████▉     | 615/1248 [1:01:32<1:02:14,  5.90s/it]
 49%|████▉     | 616/1248 [1:01:39<1:04:57,  6.17s/it]
                                                      
{'loss': 0.6864, 'learning_rate': 0.00010700442522783932, 'epoch': 1.48}

 49%|████▉     | 616/1248 [1:01:39<1:04:57,  6.17s/it]
 49%|████▉     | 617/1248 [1:01:45<1:04:25,  6.13s/it]
                                                      
{'loss': 0.5188, 'learning_rate': 0.0001067454038489226, 'epoch': 1.48}

 49%|████▉     | 617/1248 [1:01:45<1:04:25,  6.13s/it]
 50%|████▉     | 618/1248 [1:01:51<1:03:32,  6.05s/it]
                                                      
{'loss': 0.5058, 'learning_rate': 0.0001064863369987743, 'epoch': 1.49}

 50%|████▉     | 618/1248 [1:01:51<1:03:32,  6.05s/it]
 50%|████▉     | 619/1248 [1:01:57<1:03:38,  6.07s/it]
                                                      
{'loss': 0.566, 'learning_rate': 0.00010622722642378196, 'epoch': 1.49}

 50%|████▉     | 619/1248 [1:01:57<1:03:38,  6.07s/it]
 50%|████▉     | 620/1248 [1:02:03<1:02:49,  6.00s/it]
                                                      
{'loss': 0.5482, 'learning_rate': 0.0001059680738706277, 'epoch': 1.49}

 50%|████▉     | 620/1248 [1:02:03<1:02:49,  6.00s/it]
 50%|████▉     | 621/1248 [1:02:09<1:02:59,  6.03s/it]
                                                      
{'loss': 0.548, 'learning_rate': 0.00010570888108627681, 'epoch': 1.49}

 50%|████▉     | 621/1248 [1:02:09<1:02:59,  6.03s/it]
 50%|████▉     | 622/1248 [1:02:15<1:02:11,  5.96s/it]
                                                      
{'loss': 0.5629, 'learning_rate': 0.00010544964981796563, 'epoch': 1.5}

 50%|████▉     | 622/1248 [1:02:15<1:02:11,  5.96s/it]
 50%|████▉     | 623/1248 [1:02:21<1:03:31,  6.10s/it]
                                                      
{'loss': 0.5262, 'learning_rate': 0.00010519038181318999, 'epoch': 1.5}

 50%|████▉     | 623/1248 [1:02:21<1:03:31,  6.10s/it]
 50%|█████     | 624/1248 [1:02:27<1:02:42,  6.03s/it]
                                                      
{'loss': 0.591, 'learning_rate': 0.00010493107881969336, 'epoch': 1.5}

 50%|█████     | 624/1248 [1:02:27<1:02:42,  6.03s/it]
 50%|█████     | 625/1248 [1:02:33<1:02:11,  5.99s/it]
                                                      
{'loss': 0.5621, 'learning_rate': 0.00010467174258545504, 'epoch': 1.5}

 50%|█████     | 625/1248 [1:02:33<1:02:11,  5.99s/it]
 50%|█████     | 626/1248 [1:02:39<1:02:27,  6.02s/it]
                                                      
{'loss': 0.5167, 'learning_rate': 0.00010441237485867846, 'epoch': 1.5}

 50%|█████     | 626/1248 [1:02:39<1:02:27,  6.02s/it]
 50%|█████     | 627/1248 [1:02:45<1:01:55,  5.98s/it]
                                                      
{'loss': 0.5441, 'learning_rate': 0.00010415297738777931, 'epoch': 1.51}

 50%|█████     | 627/1248 [1:02:45<1:01:55,  5.98s/it]
 50%|█████     | 628/1248 [1:02:51<1:01:57,  6.00s/it]
                                                      
{'loss': 0.5274, 'learning_rate': 0.00010389355192137377, 'epoch': 1.51}

 50%|█████     | 628/1248 [1:02:51<1:01:57,  6.00s/it]
 50%|█████     | 629/1248 [1:02:56<59:49,  5.80s/it]  
                                                    
{'loss': 0.5496, 'learning_rate': 0.0001036341002082668, 'epoch': 1.51}

 50%|█████     | 629/1248 [1:02:56<59:49,  5.80s/it]
 50%|█████     | 630/1248 [1:03:03<1:00:27,  5.87s/it]
                                                      
{'loss': 0.5572, 'learning_rate': 0.00010337462399744024, 'epoch': 1.51}

 50%|█████     | 630/1248 [1:03:03<1:00:27,  5.87s/it]
 51%|█████     | 631/1248 [1:03:09<1:02:04,  6.04s/it]
                                                      
{'loss': 0.5629, 'learning_rate': 0.00010311512503804106, 'epoch': 1.52}

 51%|█████     | 631/1248 [1:03:09<1:02:04,  6.04s/it]
 51%|█████     | 632/1248 [1:03:15<1:03:10,  6.15s/it]
                                                      
{'loss': 0.5432, 'learning_rate': 0.00010285560507936961, 'epoch': 1.52}

 51%|█████     | 632/1248 [1:03:15<1:03:10,  6.15s/it]
 51%|█████     | 633/1248 [1:03:21<1:01:49,  6.03s/it]
                                                      
{'loss': 0.5642, 'learning_rate': 0.00010259606587086783, 'epoch': 1.52}

 51%|█████     | 633/1248 [1:03:21<1:01:49,  6.03s/it]
 51%|█████     | 634/1248 [1:03:27<1:01:02,  5.97s/it]
                                                      
{'loss': 0.5358, 'learning_rate': 0.00010233650916210735, 'epoch': 1.52}

 51%|█████     | 634/1248 [1:03:27<1:01:02,  5.97s/it]
 51%|█████     | 635/1248 [1:03:33<1:00:13,  5.90s/it]
                                                      
{'loss': 0.5383, 'learning_rate': 0.00010207693670277782, 'epoch': 1.53}

 51%|█████     | 635/1248 [1:03:33<1:00:13,  5.90s/it]
 51%|█████     | 636/1248 [1:03:38<59:43,  5.86s/it]  
                                                    
{'loss': 0.5779, 'learning_rate': 0.00010181735024267505, 'epoch': 1.53}

 51%|█████     | 636/1248 [1:03:38<59:43,  5.86s/it]
 51%|█████     | 637/1248 [1:03:44<59:17,  5.82s/it]
                                                    
{'loss': 0.5124, 'learning_rate': 0.0001015577515316892, 'epoch': 1.53}

 51%|█████     | 637/1248 [1:03:44<59:17,  5.82s/it]
 51%|█████     | 638/1248 [1:03:50<59:29,  5.85s/it]
                                                    
{'loss': 0.5526, 'learning_rate': 0.0001012981423197931, 'epoch': 1.53}

 51%|█████     | 638/1248 [1:03:50<59:29,  5.85s/it]
 51%|█████     | 639/1248 [1:03:56<59:30,  5.86s/it]
                                                    
{'loss': 0.5509, 'learning_rate': 0.00010103852435703027, 'epoch': 1.54}

 51%|█████     | 639/1248 [1:03:56<59:30,  5.86s/it]
 51%|█████▏    | 640/1248 [1:04:01<58:25,  5.77s/it]
                                                    
{'loss': 0.5303, 'learning_rate': 0.0001007788993935033, 'epoch': 1.54}

 51%|█████▏    | 640/1248 [1:04:02<58:25,  5.77s/it]
 51%|█████▏    | 641/1248 [1:04:07<58:16,  5.76s/it]
                                                    
{'loss': 0.573, 'learning_rate': 0.0001005192691793619, 'epoch': 1.54}

 51%|█████▏    | 641/1248 [1:04:07<58:16,  5.76s/it]
 51%|█████▏    | 642/1248 [1:04:13<58:27,  5.79s/it]
                                                    
{'loss': 0.4889, 'learning_rate': 0.0001002596354647912, 'epoch': 1.54}

 51%|█████▏    | 642/1248 [1:04:13<58:27,  5.79s/it]
 52%|█████▏    | 643/1248 [1:04:19<57:51,  5.74s/it]
                                                    
{'loss': 0.5216, 'learning_rate': 0.0001, 'epoch': 1.55}

 52%|█████▏    | 643/1248 [1:04:19<57:51,  5.74s/it]
 52%|█████▏    | 644/1248 [1:04:25<58:09,  5.78s/it]
                                                    
{'loss': 0.5481, 'learning_rate': 9.974036453520881e-05, 'epoch': 1.55}

 52%|█████▏    | 644/1248 [1:04:25<58:09,  5.78s/it]
 52%|█████▏    | 645/1248 [1:04:30<57:02,  5.68s/it]
                                                    
{'loss': 0.5227, 'learning_rate': 9.948073082063814e-05, 'epoch': 1.55}

 52%|█████▏    | 645/1248 [1:04:30<57:02,  5.68s/it]
 52%|█████▏    | 646/1248 [1:04:36<56:46,  5.66s/it]
                                                    
{'loss': 0.5152, 'learning_rate': 9.922110060649672e-05, 'epoch': 1.55}

 52%|█████▏    | 646/1248 [1:04:36<56:46,  5.66s/it]
 52%|█████▏    | 647/1248 [1:04:42<57:19,  5.72s/it]
                                                    
{'loss': 0.5511, 'learning_rate': 9.896147564296974e-05, 'epoch': 1.56}

 52%|█████▏    | 647/1248 [1:04:42<57:19,  5.72s/it]
 52%|█████▏    | 648/1248 [1:04:47<57:33,  5.76s/it]
                                                    
{'loss': 0.5603, 'learning_rate': 9.870185768020693e-05, 'epoch': 1.56}

 52%|█████▏    | 648/1248 [1:04:47<57:33,  5.76s/it]
 52%|█████▏    | 649/1248 [1:04:53<56:05,  5.62s/it]
                                                    
{'loss': 0.5474, 'learning_rate': 9.844224846831083e-05, 'epoch': 1.56}

 52%|█████▏    | 649/1248 [1:04:53<56:05,  5.62s/it]
 52%|█████▏    | 650/1248 [1:04:58<55:36,  5.58s/it]
                                                    
{'loss': 0.5047, 'learning_rate': 9.818264975732496e-05, 'epoch': 1.56}

 52%|█████▏    | 650/1248 [1:04:58<55:36,  5.58s/it]
 52%|█████▏    | 651/1248 [1:05:04<56:48,  5.71s/it]
                                                    
{'loss': 0.547, 'learning_rate': 9.792306329722219e-05, 'epoch': 1.56}

 52%|█████▏    | 651/1248 [1:05:04<56:48,  5.71s/it]
 52%|█████▏    | 652/1248 [1:05:10<55:55,  5.63s/it]
                                                    
{'loss': 0.5165, 'learning_rate': 9.766349083789266e-05, 'epoch': 1.57}

 52%|█████▏    | 652/1248 [1:05:10<55:55,  5.63s/it]
 52%|█████▏    | 653/1248 [1:05:21<1:14:07,  7.48s/it]
                                                      
{'loss': 0.6636, 'learning_rate': 9.740393412913219e-05, 'epoch': 1.57}

 52%|█████▏    | 653/1248 [1:05:21<1:14:07,  7.48s/it]
 52%|█████▏    | 654/1248 [1:05:27<1:08:59,  6.97s/it]
                                                      
{'loss': 0.5357, 'learning_rate': 9.71443949206304e-05, 'epoch': 1.57}

 52%|█████▏    | 654/1248 [1:05:27<1:08:59,  6.97s/it]
 52%|█████▏    | 655/1248 [1:05:33<1:04:41,  6.55s/it]
                                                      
{'loss': 0.5651, 'learning_rate': 9.688487496195895e-05, 'epoch': 1.57}

 52%|█████▏    | 655/1248 [1:05:33<1:04:41,  6.55s/it]
 53%|█████▎    | 656/1248 [1:05:38<1:02:01,  6.29s/it]
                                                      
{'loss': 0.5291, 'learning_rate': 9.662537600255978e-05, 'epoch': 1.58}

 53%|█████▎    | 656/1248 [1:05:38<1:02:01,  6.29s/it]
 53%|█████▎    | 657/1248 [1:05:44<1:00:09,  6.11s/it]
                                                      
{'loss': 0.5371, 'learning_rate': 9.636589979173323e-05, 'epoch': 1.58}

 53%|█████▎    | 657/1248 [1:05:44<1:00:09,  6.11s/it]
 53%|█████▎    | 658/1248 [1:05:50<1:00:35,  6.16s/it]
                                                      
{'loss': 0.533, 'learning_rate': 9.610644807862625e-05, 'epoch': 1.58}

 53%|█████▎    | 658/1248 [1:05:50<1:00:35,  6.16s/it]
 53%|█████▎    | 659/1248 [1:05:56<59:29,  6.06s/it]  
                                                    
{'loss': 0.5996, 'learning_rate': 9.584702261222071e-05, 'epoch': 1.58}

 53%|█████▎    | 659/1248 [1:05:56<59:29,  6.06s/it]
 53%|█████▎    | 660/1248 [1:06:02<58:06,  5.93s/it]
                                                    
{'loss': 0.4974, 'learning_rate': 9.558762514132157e-05, 'epoch': 1.59}

 53%|█████▎    | 660/1248 [1:06:02<58:06,  5.93s/it]
 53%|█████▎    | 661/1248 [1:06:08<57:16,  5.85s/it]
                                                    
{'loss': 0.5603, 'learning_rate': 9.532825741454498e-05, 'epoch': 1.59}

 53%|█████▎    | 661/1248 [1:06:08<57:16,  5.85s/it]
 53%|█████▎    | 662/1248 [1:06:13<56:00,  5.73s/it]
                                                    
{'loss': 0.493, 'learning_rate': 9.506892118030668e-05, 'epoch': 1.59}

 53%|█████▎    | 662/1248 [1:06:13<56:00,  5.73s/it]
 53%|█████▎    | 663/1248 [1:06:19<56:47,  5.82s/it]
                                                    
{'loss': 0.5313, 'learning_rate': 9.480961818681004e-05, 'epoch': 1.59}

 53%|█████▎    | 663/1248 [1:06:19<56:47,  5.82s/it]
 53%|█████▎    | 664/1248 [1:06:25<56:22,  5.79s/it]
                                                    
{'loss': 0.5348, 'learning_rate': 9.455035018203438e-05, 'epoch': 1.6}

 53%|█████▎    | 664/1248 [1:06:25<56:22,  5.79s/it]
 53%|█████▎    | 665/1248 [1:06:30<56:08,  5.78s/it]
                                                    
{'loss': 0.5715, 'learning_rate': 9.42911189137232e-05, 'epoch': 1.6}

 53%|█████▎    | 665/1248 [1:06:30<56:08,  5.78s/it]
 53%|█████▎    | 666/1248 [1:06:36<55:38,  5.74s/it]
                                                    
{'loss': 0.5114, 'learning_rate': 9.403192612937231e-05, 'epoch': 1.6}

 53%|█████▎    | 666/1248 [1:06:36<55:38,  5.74s/it]
 53%|█████▎    | 667/1248 [1:06:42<55:32,  5.74s/it]
                                                    
{'loss': 0.562, 'learning_rate': 9.37727735762181e-05, 'epoch': 1.6}

 53%|█████▎    | 667/1248 [1:06:42<55:32,  5.74s/it]
 54%|█████▎    | 668/1248 [1:06:48<56:10,  5.81s/it]
                                                    
{'loss': 0.5579, 'learning_rate': 9.35136630012257e-05, 'epoch': 1.61}

 54%|█████▎    | 668/1248 [1:06:48<56:10,  5.81s/it]
 54%|█████▎    | 669/1248 [1:06:53<55:36,  5.76s/it]
                                                    
{'loss': 0.5559, 'learning_rate': 9.325459615107742e-05, 'epoch': 1.61}

 54%|█████▎    | 669/1248 [1:06:53<55:36,  5.76s/it]
 54%|█████▎    | 670/1248 [1:06:59<54:47,  5.69s/it]
                                                    
{'loss': 0.5199, 'learning_rate': 9.299557477216072e-05, 'epoch': 1.61}

 54%|█████▎    | 670/1248 [1:06:59<54:47,  5.69s/it]
 54%|█████▍    | 671/1248 [1:07:05<55:01,  5.72s/it]
                                                    
{'loss': 0.519, 'learning_rate': 9.27366006105565e-05, 'epoch': 1.61}

 54%|█████▍    | 671/1248 [1:07:05<55:01,  5.72s/it]
 54%|█████▍    | 672/1248 [1:07:11<55:37,  5.79s/it]
                                                    
{'loss': 0.5237, 'learning_rate': 9.247767541202738e-05, 'epoch': 1.62}

 54%|█████▍    | 672/1248 [1:07:11<55:37,  5.79s/it]
 54%|█████▍    | 673/1248 [1:07:17<56:18,  5.88s/it]
                                                    
{'loss': 0.5525, 'learning_rate': 9.221880092200601e-05, 'epoch': 1.62}

 54%|█████▍    | 673/1248 [1:07:17<56:18,  5.88s/it]
 54%|█████▍    | 674/1248 [1:07:23<57:26,  6.00s/it]
                                                    
{'loss': 0.5481, 'learning_rate': 9.195997888558312e-05, 'epoch': 1.62}

 54%|█████▍    | 674/1248 [1:07:23<57:26,  6.00s/it]
 54%|█████▍    | 675/1248 [1:07:29<57:38,  6.04s/it]
                                                    
{'loss': 0.5321, 'learning_rate': 9.170121104749587e-05, 'epoch': 1.62}

 54%|█████▍    | 675/1248 [1:07:29<57:38,  6.04s/it]
 54%|█████▍    | 676/1248 [1:07:35<57:32,  6.04s/it]
                                                    
{'loss': 0.553, 'learning_rate': 9.144249915211605e-05, 'epoch': 1.62}

 54%|█████▍    | 676/1248 [1:07:35<57:32,  6.04s/it]
 54%|█████▍    | 677/1248 [1:07:41<56:51,  5.97s/it]
                                                    
{'loss': 0.5262, 'learning_rate': 9.118384494343832e-05, 'epoch': 1.63}

 54%|█████▍    | 677/1248 [1:07:41<56:51,  5.97s/it]
 54%|█████▍    | 678/1248 [1:07:47<56:41,  5.97s/it]
                                                    
{'loss': 0.5193, 'learning_rate': 9.092525016506858e-05, 'epoch': 1.63}

 54%|█████▍    | 678/1248 [1:07:47<56:41,  5.97s/it]
 54%|█████▍    | 679/1248 [1:07:53<57:21,  6.05s/it]
                                                    
{'loss': 0.5065, 'learning_rate': 9.066671656021198e-05, 'epoch': 1.63}

 54%|█████▍    | 679/1248 [1:07:53<57:21,  6.05s/it]
 54%|█████▍    | 680/1248 [1:07:59<56:26,  5.96s/it]
                                                    
{'loss': 0.5319, 'learning_rate': 9.040824587166136e-05, 'epoch': 1.63}

 54%|█████▍    | 680/1248 [1:07:59<56:26,  5.96s/it]
 55%|█████▍    | 681/1248 [1:08:05<55:55,  5.92s/it]
                                                    
{'loss': 0.5091, 'learning_rate': 9.014983984178536e-05, 'epoch': 1.64}

 55%|█████▍    | 681/1248 [1:08:05<55:55,  5.92s/it]
 55%|█████▍    | 682/1248 [1:08:11<55:25,  5.88s/it]
                                                    
{'loss': 0.5528, 'learning_rate': 8.989150021251689e-05, 'epoch': 1.64}

 55%|█████▍    | 682/1248 [1:08:11<55:25,  5.88s/it]
 55%|█████▍    | 683/1248 [1:08:17<55:40,  5.91s/it]
                                                    
{'loss': 0.5289, 'learning_rate': 8.963322872534114e-05, 'epoch': 1.64}

 55%|█████▍    | 683/1248 [1:08:17<55:40,  5.91s/it]
 55%|█████▍    | 684/1248 [1:08:23<55:41,  5.92s/it]
                                                    
{'loss': 0.5626, 'learning_rate': 8.937502712128398e-05, 'epoch': 1.64}

 55%|█████▍    | 684/1248 [1:08:23<55:41,  5.92s/it]
 55%|█████▍    | 685/1248 [1:08:28<55:25,  5.91s/it]
                                                    
{'loss': 0.5014, 'learning_rate': 8.911689714090018e-05, 'epoch': 1.65}

 55%|█████▍    | 685/1248 [1:08:28<55:25,  5.91s/it]
 55%|█████▍    | 686/1248 [1:08:34<54:11,  5.79s/it]
                                                    
{'loss': 0.5642, 'learning_rate': 8.885884052426168e-05, 'epoch': 1.65}

 55%|█████▍    | 686/1248 [1:08:34<54:11,  5.79s/it]
 55%|█████▌    | 687/1248 [1:08:40<55:05,  5.89s/it]
                                                    
{'loss': 0.5157, 'learning_rate': 8.860085901094595e-05, 'epoch': 1.65}

 55%|█████▌    | 687/1248 [1:08:40<55:05,  5.89s/it]
 55%|█████▌    | 688/1248 [1:08:46<54:15,  5.81s/it]
                                                    
{'loss': 0.5501, 'learning_rate': 8.83429543400241e-05, 'epoch': 1.65}

 55%|█████▌    | 688/1248 [1:08:46<54:15,  5.81s/it]
 55%|█████▌    | 689/1248 [1:08:52<54:45,  5.88s/it]
                                                    
{'loss': 0.5215, 'learning_rate': 8.808512825004925e-05, 'epoch': 1.66}

 55%|█████▌    | 689/1248 [1:08:52<54:45,  5.88s/it]
 55%|█████▌    | 690/1248 [1:08:57<54:05,  5.82s/it]
                                                    
{'loss': 0.5461, 'learning_rate': 8.782738247904481e-05, 'epoch': 1.66}

 55%|█████▌    | 690/1248 [1:08:57<54:05,  5.82s/it]
 55%|█████▌    | 691/1248 [1:09:03<54:14,  5.84s/it]
                                                    
{'loss': 0.5618, 'learning_rate': 8.75697187644928e-05, 'epoch': 1.66}

 55%|█████▌    | 691/1248 [1:09:03<54:14,  5.84s/it]
 55%|█████▌    | 692/1248 [1:09:09<53:49,  5.81s/it]
                                                    
{'loss': 0.5672, 'learning_rate': 8.731213884332205e-05, 'epoch': 1.66}

 55%|█████▌    | 692/1248 [1:09:09<53:49,  5.81s/it]
 56%|█████▌    | 693/1248 [1:09:15<53:51,  5.82s/it]
                                                    
{'loss': 0.5325, 'learning_rate': 8.705464445189647e-05, 'epoch': 1.67}

 56%|█████▌    | 693/1248 [1:09:15<53:51,  5.82s/it]
 56%|█████▌    | 694/1248 [1:09:21<53:45,  5.82s/it]
                                                    
{'loss': 0.5193, 'learning_rate': 8.679723732600354e-05, 'epoch': 1.67}

 56%|█████▌    | 694/1248 [1:09:21<53:45,  5.82s/it]
 56%|█████▌    | 695/1248 [1:09:27<53:41,  5.82s/it]
                                                    
{'loss': 0.5062, 'learning_rate': 8.653991920084237e-05, 'epoch': 1.67}

 56%|█████▌    | 695/1248 [1:09:27<53:41,  5.82s/it]
 56%|█████▌    | 696/1248 [1:09:32<53:14,  5.79s/it]
                                                    
{'loss': 0.5522, 'learning_rate': 8.628269181101216e-05, 'epoch': 1.67}

 56%|█████▌    | 696/1248 [1:09:32<53:14,  5.79s/it]
 56%|█████▌    | 697/1248 [1:09:38<53:09,  5.79s/it]
                                                    
{'loss': 0.5266, 'learning_rate': 8.602555689050049e-05, 'epoch': 1.68}

 56%|█████▌    | 697/1248 [1:09:38<53:09,  5.79s/it]
 56%|█████▌    | 698/1248 [1:09:44<53:17,  5.81s/it]
                                                    
{'loss': 0.5729, 'learning_rate': 8.57685161726715e-05, 'epoch': 1.68}

 56%|█████▌    | 698/1248 [1:09:44<53:17,  5.81s/it]
 56%|█████▌    | 699/1248 [1:09:50<53:27,  5.84s/it]
                                                    
{'loss': 0.5057, 'learning_rate': 8.551157139025437e-05, 'epoch': 1.68}

 56%|█████▌    | 699/1248 [1:09:50<53:27,  5.84s/it]
 56%|█████▌    | 700/1248 [1:09:56<53:30,  5.86s/it]
                                                    
{'loss': 0.5386, 'learning_rate': 8.525472427533156e-05, 'epoch': 1.68}

 56%|█████▌    | 700/1248 [1:09:56<53:30,  5.86s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 56%|█████▌    | 701/1248 [1:10:21<1:46:45, 11.71s/it]
                                                      
{'loss': 0.5719, 'learning_rate': 8.499797655932716e-05, 'epoch': 1.69}

 56%|█████▌    | 701/1248 [1:10:21<1:46:45, 11.71s/it]
 56%|█████▋    | 702/1248 [1:10:26<1:28:59,  9.78s/it]
                                                      
{'loss': 0.5724, 'learning_rate': 8.474132997299521e-05, 'epoch': 1.69}

 56%|█████▋    | 702/1248 [1:10:26<1:28:59,  9.78s/it]
 56%|█████▋    | 703/1248 [1:10:32<1:18:36,  8.65s/it]
                                                      
{'loss': 0.5662, 'learning_rate': 8.448478624640797e-05, 'epoch': 1.69}

 56%|█████▋    | 703/1248 [1:10:32<1:18:36,  8.65s/it]
 56%|█████▋    | 704/1248 [1:10:38<1:08:55,  7.60s/it]
                                                      
{'loss': 0.5049, 'learning_rate': 8.422834710894434e-05, 'epoch': 1.69}

 56%|█████▋    | 704/1248 [1:10:38<1:08:55,  7.60s/it]
 56%|█████▋    | 705/1248 [1:10:43<1:02:18,  6.88s/it]
                                                      
{'loss': 0.5413, 'learning_rate': 8.397201428927827e-05, 'epoch': 1.69}

 56%|█████▋    | 705/1248 [1:10:43<1:02:18,  6.88s/it]
 57%|█████▋    | 706/1248 [1:10:48<57:43,  6.39s/it]  
                                                    
{'loss': 0.5586, 'learning_rate': 8.371578951536688e-05, 'epoch': 1.7}

 57%|█████▋    | 706/1248 [1:10:48<57:43,  6.39s/it]
 57%|█████▋    | 707/1248 [1:10:53<54:38,  6.06s/it]
                                                    
{'loss': 0.5168, 'learning_rate': 8.345967451443903e-05, 'epoch': 1.7}

 57%|█████▋    | 707/1248 [1:10:53<54:38,  6.06s/it]
 57%|█████▋    | 708/1248 [1:10:59<53:01,  5.89s/it]
                                                    
{'loss': 0.543, 'learning_rate': 8.320367101298351e-05, 'epoch': 1.7}

 57%|█████▋    | 708/1248 [1:10:59<53:01,  5.89s/it]
 57%|█████▋    | 709/1248 [1:11:05<52:30,  5.85s/it]
                                                    
{'loss': 0.5529, 'learning_rate': 8.294778073673762e-05, 'epoch': 1.7}

 57%|█████▋    | 709/1248 [1:11:05<52:30,  5.85s/it]
 57%|█████▋    | 710/1248 [1:11:11<53:03,  5.92s/it]
                                                    
{'loss': 0.5328, 'learning_rate': 8.26920054106753e-05, 'epoch': 1.71}

 57%|█████▋    | 710/1248 [1:11:11<53:03,  5.92s/it]
 57%|█████▋    | 711/1248 [1:11:16<52:48,  5.90s/it]
                                                    
{'loss': 0.5031, 'learning_rate': 8.24363467589956e-05, 'epoch': 1.71}

 57%|█████▋    | 711/1248 [1:11:16<52:48,  5.90s/it]
 57%|█████▋    | 712/1248 [1:11:22<52:27,  5.87s/it]
                                                    
{'loss': 0.6333, 'learning_rate': 8.218080650511106e-05, 'epoch': 1.71}

 57%|█████▋    | 712/1248 [1:11:22<52:27,  5.87s/it]
 57%|█████▋    | 713/1248 [1:11:28<52:13,  5.86s/it]
                                                    
{'loss': 0.5576, 'learning_rate': 8.192538637163621e-05, 'epoch': 1.71}

 57%|█████▋    | 713/1248 [1:11:28<52:13,  5.86s/it]
 57%|█████▋    | 714/1248 [1:11:34<51:58,  5.84s/it]
                                                    
{'loss': 0.5456, 'learning_rate': 8.167008808037567e-05, 'epoch': 1.72}

 57%|█████▋    | 714/1248 [1:11:34<51:58,  5.84s/it]
 57%|█████▋    | 715/1248 [1:11:39<50:45,  5.71s/it]
                                                    
{'loss': 0.5488, 'learning_rate': 8.141491335231281e-05, 'epoch': 1.72}

 57%|█████▋    | 715/1248 [1:11:39<50:45,  5.71s/it]
 57%|█████▋    | 716/1248 [1:11:45<50:32,  5.70s/it]
                                                    
{'loss': 0.5076, 'learning_rate': 8.115986390759806e-05, 'epoch': 1.72}

 57%|█████▋    | 716/1248 [1:11:45<50:32,  5.70s/it]
 57%|█████▋    | 717/1248 [1:11:50<49:54,  5.64s/it]
                                                    
{'loss': 0.5572, 'learning_rate': 8.09049414655372e-05, 'epoch': 1.72}

 57%|█████▋    | 717/1248 [1:11:50<49:54,  5.64s/it]
 58%|█████▊    | 718/1248 [1:11:56<50:44,  5.75s/it]
                                                    
{'loss': 0.5326, 'learning_rate': 8.065014774458003e-05, 'epoch': 1.73}

 58%|█████▊    | 718/1248 [1:11:56<50:44,  5.75s/it]
 58%|█████▊    | 719/1248 [1:12:02<50:46,  5.76s/it]
                                                    
{'loss': 0.5088, 'learning_rate': 8.039548446230857e-05, 'epoch': 1.73}

 58%|█████▊    | 719/1248 [1:12:02<50:46,  5.76s/it]
 58%|█████▊    | 720/1248 [1:12:08<51:27,  5.85s/it]
                                                    
{'loss': 0.5004, 'learning_rate': 8.014095333542548e-05, 'epoch': 1.73}

 58%|█████▊    | 720/1248 [1:12:08<51:27,  5.85s/it]
 58%|█████▊    | 721/1248 [1:12:14<51:18,  5.84s/it]
                                                    
{'loss': 0.4873, 'learning_rate': 7.988655607974258e-05, 'epoch': 1.73}

 58%|█████▊    | 721/1248 [1:12:14<51:18,  5.84s/it]
 58%|█████▊    | 722/1248 [1:12:20<50:06,  5.72s/it]
                                                    
{'loss': 0.528, 'learning_rate': 7.963229441016937e-05, 'epoch': 1.74}

 58%|█████▊    | 722/1248 [1:12:20<50:06,  5.72s/it]
 58%|█████▊    | 723/1248 [1:12:25<49:32,  5.66s/it]
                                                    
{'loss': 0.5222, 'learning_rate': 7.93781700407012e-05, 'epoch': 1.74}

 58%|█████▊    | 723/1248 [1:12:25<49:32,  5.66s/it]
 58%|█████▊    | 724/1248 [1:12:31<49:18,  5.65s/it]
                                                    
{'loss': 0.5484, 'learning_rate': 7.912418468440794e-05, 'epoch': 1.74}

 58%|█████▊    | 724/1248 [1:12:31<49:18,  5.65s/it]
 58%|█████▊    | 725/1248 [1:12:36<49:20,  5.66s/it]
                                                    
{'loss': 0.5131, 'learning_rate': 7.887034005342236e-05, 'epoch': 1.74}

 58%|█████▊    | 725/1248 [1:12:36<49:20,  5.66s/it]
 58%|█████▊    | 726/1248 [1:12:42<47:52,  5.50s/it]
                                                    
{'loss': 0.5439, 'learning_rate': 7.861663785892857e-05, 'epoch': 1.75}

 58%|█████▊    | 726/1248 [1:12:42<47:52,  5.50s/it]
 58%|█████▊    | 727/1248 [1:12:47<48:13,  5.55s/it]
                                                    
{'loss': 0.5357, 'learning_rate': 7.836307981115055e-05, 'epoch': 1.75}

 58%|█████▊    | 727/1248 [1:12:47<48:13,  5.55s/it]
 58%|█████▊    | 728/1248 [1:12:53<49:19,  5.69s/it]
                                                    
{'loss': 0.5757, 'learning_rate': 7.810966761934053e-05, 'epoch': 1.75}

 58%|█████▊    | 728/1248 [1:12:53<49:19,  5.69s/it]
 58%|█████▊    | 729/1248 [1:12:59<48:46,  5.64s/it]
                                                    
{'loss': 0.5494, 'learning_rate': 7.785640299176756e-05, 'epoch': 1.75}

 58%|█████▊    | 729/1248 [1:12:59<48:46,  5.64s/it]
 58%|█████▊    | 730/1248 [1:13:05<49:23,  5.72s/it]
                                                    
{'loss': 0.5349, 'learning_rate': 7.760328763570588e-05, 'epoch': 1.75}

 58%|█████▊    | 730/1248 [1:13:05<49:23,  5.72s/it]
 59%|█████▊    | 731/1248 [1:13:10<49:21,  5.73s/it]
                                                    
{'loss': 0.5191, 'learning_rate': 7.735032325742355e-05, 'epoch': 1.76}

 59%|█████▊    | 731/1248 [1:13:10<49:21,  5.73s/it]
 59%|█████▊    | 732/1248 [1:13:16<49:56,  5.81s/it]
                                                    
{'loss': 0.4989, 'learning_rate': 7.709751156217089e-05, 'epoch': 1.76}

 59%|█████▊    | 732/1248 [1:13:16<49:56,  5.81s/it]
 59%|█████▊    | 733/1248 [1:13:22<49:24,  5.76s/it]
                                                    
{'loss': 0.5106, 'learning_rate': 7.684485425416888e-05, 'epoch': 1.76}

 59%|█████▊    | 733/1248 [1:13:22<49:24,  5.76s/it]
 59%|█████▉    | 734/1248 [1:13:28<49:57,  5.83s/it]
                                                    
{'loss': 0.4986, 'learning_rate': 7.659235303659784e-05, 'epoch': 1.76}

 59%|█████▉    | 734/1248 [1:13:28<49:57,  5.83s/it]
 59%|█████▉    | 735/1248 [1:13:34<50:23,  5.89s/it]
                                                    
{'loss': 0.5473, 'learning_rate': 7.634000961158581e-05, 'epoch': 1.77}

 59%|█████▉    | 735/1248 [1:13:34<50:23,  5.89s/it]
 59%|█████▉    | 736/1248 [1:13:40<50:23,  5.91s/it]
                                                    
{'loss': 0.5277, 'learning_rate': 7.608782568019729e-05, 'epoch': 1.77}

 59%|█████▉    | 736/1248 [1:13:40<50:23,  5.91s/it]
 59%|█████▉    | 737/1248 [1:13:45<49:11,  5.78s/it]
                                                    
{'loss': 0.5492, 'learning_rate': 7.583580294242144e-05, 'epoch': 1.77}

 59%|█████▉    | 737/1248 [1:13:45<49:11,  5.78s/it]
 59%|█████▉    | 738/1248 [1:13:51<48:32,  5.71s/it]
                                                    
{'loss': 0.5281, 'learning_rate': 7.558394309716088e-05, 'epoch': 1.77}

 59%|█████▉    | 738/1248 [1:13:51<48:32,  5.71s/it]
 59%|█████▉    | 739/1248 [1:13:57<49:15,  5.81s/it]
                                                    
{'loss': 0.5226, 'learning_rate': 7.533224784222015e-05, 'epoch': 1.78}

 59%|█████▉    | 739/1248 [1:13:57<49:15,  5.81s/it]
 59%|█████▉    | 740/1248 [1:14:03<49:38,  5.86s/it]
                                                    
{'loss': 0.5597, 'learning_rate': 7.508071887429433e-05, 'epoch': 1.78}

 59%|█████▉    | 740/1248 [1:14:03<49:38,  5.86s/it]
 59%|█████▉    | 741/1248 [1:14:09<50:13,  5.94s/it]
                                                    
{'loss': 0.5192, 'learning_rate': 7.48293578889575e-05, 'epoch': 1.78}

 59%|█████▉    | 741/1248 [1:14:09<50:13,  5.94s/it]
 59%|█████▉    | 742/1248 [1:14:15<50:06,  5.94s/it]
                                                    
{'loss': 0.5241, 'learning_rate': 7.457816658065134e-05, 'epoch': 1.78}

 59%|█████▉    | 742/1248 [1:14:15<50:06,  5.94s/it]
 60%|█████▉    | 743/1248 [1:14:21<49:04,  5.83s/it]
                                                    
{'loss': 0.546, 'learning_rate': 7.432714664267373e-05, 'epoch': 1.79}

 60%|█████▉    | 743/1248 [1:14:21<49:04,  5.83s/it]
 60%|█████▉    | 744/1248 [1:14:27<49:25,  5.88s/it]
                                                    
{'loss': 0.5401, 'learning_rate': 7.407629976716732e-05, 'epoch': 1.79}

 60%|█████▉    | 744/1248 [1:14:27<49:25,  5.88s/it]
 60%|█████▉    | 745/1248 [1:14:33<50:01,  5.97s/it]
                                                    
{'loss': 0.5226, 'learning_rate': 7.382562764510826e-05, 'epoch': 1.79}

 60%|█████▉    | 745/1248 [1:14:33<50:01,  5.97s/it]
 60%|█████▉    | 746/1248 [1:14:39<49:32,  5.92s/it]
                                                    
{'loss': 0.4968, 'learning_rate': 7.35751319662945e-05, 'epoch': 1.79}

 60%|█████▉    | 746/1248 [1:14:39<49:32,  5.92s/it]
 60%|█████▉    | 747/1248 [1:14:45<49:11,  5.89s/it]
                                                    
{'loss': 0.5144, 'learning_rate': 7.332481441933467e-05, 'epoch': 1.8}

 60%|█████▉    | 747/1248 [1:14:45<49:11,  5.89s/it]
 60%|█████▉    | 748/1248 [1:14:50<49:03,  5.89s/it]
                                                    
{'loss': 0.5528, 'learning_rate': 7.307467669163655e-05, 'epoch': 1.8}

 60%|█████▉    | 748/1248 [1:14:50<49:03,  5.89s/it]
 60%|██████    | 749/1248 [1:14:56<48:51,  5.87s/it]
                                                    
{'loss': 0.5147, 'learning_rate': 7.282472046939583e-05, 'epoch': 1.8}

 60%|██████    | 749/1248 [1:14:56<48:51,  5.87s/it]
 60%|██████    | 750/1248 [1:15:02<48:44,  5.87s/it]
                                                    
{'loss': 0.5643, 'learning_rate': 7.257494743758459e-05, 'epoch': 1.8}

 60%|██████    | 750/1248 [1:15:02<48:44,  5.87s/it]
 60%|██████    | 751/1248 [1:15:08<48:57,  5.91s/it]
                                                    
{'loss': 0.5208, 'learning_rate': 7.232535927994002e-05, 'epoch': 1.81}

 60%|██████    | 751/1248 [1:15:08<48:57,  5.91s/it]
 60%|██████    | 752/1248 [1:15:14<49:14,  5.96s/it]
                                                    
{'loss': 0.5458, 'learning_rate': 7.207595767895302e-05, 'epoch': 1.81}

 60%|██████    | 752/1248 [1:15:14<49:14,  5.96s/it]
 60%|██████    | 753/1248 [1:15:20<48:45,  5.91s/it]
                                                    
{'loss': 0.5399, 'learning_rate': 7.182674431585704e-05, 'epoch': 1.81}

 60%|██████    | 753/1248 [1:15:20<48:45,  5.91s/it]
 60%|██████    | 754/1248 [1:15:26<48:01,  5.83s/it]
                                                    
{'loss': 0.5607, 'learning_rate': 7.157772087061645e-05, 'epoch': 1.81}

 60%|██████    | 754/1248 [1:15:26<48:01,  5.83s/it]
 60%|██████    | 755/1248 [1:15:31<47:44,  5.81s/it]
                                                    
{'loss': 0.5437, 'learning_rate': 7.132888902191543e-05, 'epoch': 1.81}

 60%|██████    | 755/1248 [1:15:31<47:44,  5.81s/it]
 61%|██████    | 756/1248 [1:15:37<47:58,  5.85s/it]
                                                    
{'loss': 0.5717, 'learning_rate': 7.108025044714661e-05, 'epoch': 1.82}

 61%|██████    | 756/1248 [1:15:37<47:58,  5.85s/it]
 61%|██████    | 757/1248 [1:15:43<47:43,  5.83s/it]
                                                    
{'loss': 0.5097, 'learning_rate': 7.083180682239973e-05, 'epoch': 1.82}

 61%|██████    | 757/1248 [1:15:43<47:43,  5.83s/it]
 61%|██████    | 758/1248 [1:15:49<47:20,  5.80s/it]
                                                    
{'loss': 0.5635, 'learning_rate': 7.058355982245037e-05, 'epoch': 1.82}

 61%|██████    | 758/1248 [1:15:49<47:20,  5.80s/it]
 61%|██████    | 759/1248 [1:15:55<46:57,  5.76s/it]
                                                    
{'loss': 0.6407, 'learning_rate': 7.033551112074865e-05, 'epoch': 1.82}

 61%|██████    | 759/1248 [1:15:55<46:57,  5.76s/it]
 61%|██████    | 760/1248 [1:16:00<46:23,  5.70s/it]
                                                    
{'loss': 0.5192, 'learning_rate': 7.00876623894079e-05, 'epoch': 1.83}

 61%|██████    | 760/1248 [1:16:00<46:23,  5.70s/it]
 61%|██████    | 761/1248 [1:16:06<46:40,  5.75s/it]
                                                    
{'loss': 0.5039, 'learning_rate': 6.984001529919348e-05, 'epoch': 1.83}

 61%|██████    | 761/1248 [1:16:06<46:40,  5.75s/it]
 61%|██████    | 762/1248 [1:16:12<46:44,  5.77s/it]
                                                    
{'loss': 0.5272, 'learning_rate': 6.959257151951153e-05, 'epoch': 1.83}

 61%|██████    | 762/1248 [1:16:12<46:44,  5.77s/it]
 61%|██████    | 763/1248 [1:16:17<46:03,  5.70s/it]
                                                    
{'loss': 0.5452, 'learning_rate': 6.934533271839752e-05, 'epoch': 1.83}

 61%|██████    | 763/1248 [1:16:17<46:03,  5.70s/it]
 61%|██████    | 764/1248 [1:16:23<46:19,  5.74s/it]
                                                    
{'loss': 0.5558, 'learning_rate': 6.909830056250527e-05, 'epoch': 1.84}

 61%|██████    | 764/1248 [1:16:23<46:19,  5.74s/it]
 61%|██████▏   | 765/1248 [1:16:29<46:28,  5.77s/it]
                                                    
{'loss': 0.5403, 'learning_rate': 6.885147671709554e-05, 'epoch': 1.84}

 61%|██████▏   | 765/1248 [1:16:29<46:28,  5.77s/it]
 61%|██████▏   | 766/1248 [1:16:35<46:16,  5.76s/it]
                                                    
{'loss': 0.5335, 'learning_rate': 6.860486284602478e-05, 'epoch': 1.84}

 61%|██████▏   | 766/1248 [1:16:35<46:16,  5.76s/it]
 61%|██████▏   | 767/1248 [1:16:41<46:44,  5.83s/it]
                                                    
{'loss': 0.5273, 'learning_rate': 6.83584606117342e-05, 'epoch': 1.84}

 61%|██████▏   | 767/1248 [1:16:41<46:44,  5.83s/it]
 62%|██████▏   | 768/1248 [1:16:47<47:29,  5.94s/it]
                                                    
{'loss': 0.5508, 'learning_rate': 6.811227167523815e-05, 'epoch': 1.85}

 62%|██████▏   | 768/1248 [1:16:47<47:29,  5.94s/it]
 62%|██████▏   | 769/1248 [1:16:53<46:50,  5.87s/it]
                                                    
{'loss': 0.5571, 'learning_rate': 6.78662976961132e-05, 'epoch': 1.85}

 62%|██████▏   | 769/1248 [1:16:53<46:50,  5.87s/it]
 62%|██████▏   | 770/1248 [1:16:58<46:16,  5.81s/it]
                                                    
{'loss': 0.5677, 'learning_rate': 6.762054033248681e-05, 'epoch': 1.85}

 62%|██████▏   | 770/1248 [1:16:58<46:16,  5.81s/it]
 62%|██████▏   | 771/1248 [1:17:04<46:58,  5.91s/it]
                                                    
{'loss': 0.5445, 'learning_rate': 6.737500124102638e-05, 'epoch': 1.85}

 62%|██████▏   | 771/1248 [1:17:04<46:58,  5.91s/it]
 62%|██████▏   | 772/1248 [1:17:10<47:00,  5.93s/it]
                                                    
{'loss': 0.5711, 'learning_rate': 6.712968207692778e-05, 'epoch': 1.86}

 62%|██████▏   | 772/1248 [1:17:10<47:00,  5.93s/it]
 62%|██████▏   | 773/1248 [1:17:16<46:42,  5.90s/it]
                                                    
{'loss': 0.5578, 'learning_rate': 6.688458449390437e-05, 'epoch': 1.86}

 62%|██████▏   | 773/1248 [1:17:16<46:42,  5.90s/it]
 62%|██████▏   | 774/1248 [1:17:22<46:08,  5.84s/it]
                                                    
{'loss': 0.4867, 'learning_rate': 6.663971014417586e-05, 'epoch': 1.86}

 62%|██████▏   | 774/1248 [1:17:22<46:08,  5.84s/it]
 62%|██████▏   | 775/1248 [1:17:28<45:34,  5.78s/it]
                                                    
{'loss': 0.5173, 'learning_rate': 6.639506067845697e-05, 'epoch': 1.86}

 62%|██████▏   | 775/1248 [1:17:28<45:34,  5.78s/it]
 62%|██████▏   | 776/1248 [1:17:33<45:11,  5.75s/it]
                                                    
{'loss': 0.5125, 'learning_rate': 6.615063774594676e-05, 'epoch': 1.87}

 62%|██████▏   | 776/1248 [1:17:33<45:11,  5.75s/it]
 62%|██████▏   | 777/1248 [1:17:39<45:26,  5.79s/it]
                                                    
{'loss': 0.5537, 'learning_rate': 6.590644299431696e-05, 'epoch': 1.87}

 62%|██████▏   | 777/1248 [1:17:39<45:26,  5.79s/it]
 62%|██████▏   | 778/1248 [1:17:45<45:46,  5.84s/it]
                                                    
{'loss': 0.5271, 'learning_rate': 6.566247806970119e-05, 'epoch': 1.87}

 62%|██████▏   | 778/1248 [1:17:45<45:46,  5.84s/it]
 62%|██████▏   | 779/1248 [1:17:51<44:47,  5.73s/it]
                                                    
{'loss': 0.5398, 'learning_rate': 6.541874461668373e-05, 'epoch': 1.87}

 62%|██████▏   | 779/1248 [1:17:51<44:47,  5.73s/it]
 62%|██████▎   | 780/1248 [1:17:56<44:04,  5.65s/it]
                                                    
{'loss': 0.5575, 'learning_rate': 6.51752442782887e-05, 'epoch': 1.88}

 62%|██████▎   | 780/1248 [1:17:56<44:04,  5.65s/it]
 63%|██████▎   | 781/1248 [1:18:01<43:21,  5.57s/it]
                                                    
{'loss': 0.5267, 'learning_rate': 6.493197869596857e-05, 'epoch': 1.88}

 63%|██████▎   | 781/1248 [1:18:01<43:21,  5.57s/it]
 63%|██████▎   | 782/1248 [1:18:06<42:10,  5.43s/it]
                                                    
{'loss': 0.5017, 'learning_rate': 6.468894950959336e-05, 'epoch': 1.88}

 63%|██████▎   | 782/1248 [1:18:06<42:10,  5.43s/it]
 63%|██████▎   | 783/1248 [1:18:12<42:40,  5.51s/it]
                                                    
{'loss': 0.5252, 'learning_rate': 6.444615835743955e-05, 'epoch': 1.88}

 63%|██████▎   | 783/1248 [1:18:12<42:40,  5.51s/it]
 63%|██████▎   | 784/1248 [1:18:18<43:18,  5.60s/it]
                                                    
{'loss': 0.5538, 'learning_rate': 6.420360687617897e-05, 'epoch': 1.88}

 63%|██████▎   | 784/1248 [1:18:18<43:18,  5.60s/it]
 63%|██████▎   | 785/1248 [1:18:24<43:06,  5.59s/it]
                                                    
{'loss': 0.5672, 'learning_rate': 6.396129670086791e-05, 'epoch': 1.89}

 63%|██████▎   | 785/1248 [1:18:24<43:06,  5.59s/it]
 63%|██████▎   | 786/1248 [1:18:29<42:57,  5.58s/it]
                                                    
{'loss': 0.5507, 'learning_rate': 6.371922946493591e-05, 'epoch': 1.89}

 63%|██████▎   | 786/1248 [1:18:29<42:57,  5.58s/it]
 63%|██████▎   | 787/1248 [1:18:35<43:16,  5.63s/it]
                                                    
{'loss': 0.5539, 'learning_rate': 6.347740680017489e-05, 'epoch': 1.89}

 63%|██████▎   | 787/1248 [1:18:35<43:16,  5.63s/it]
 63%|██████▎   | 788/1248 [1:18:40<41:33,  5.42s/it]
                                                    
{'loss': 0.5061, 'learning_rate': 6.323583033672799e-05, 'epoch': 1.89}

 63%|██████▎   | 788/1248 [1:18:40<41:33,  5.42s/it]
 63%|██████▎   | 789/1248 [1:18:46<42:10,  5.51s/it]
                                                    
{'loss': 0.5423, 'learning_rate': 6.299450170307888e-05, 'epoch': 1.9}

 63%|██████▎   | 789/1248 [1:18:46<42:10,  5.51s/it]
 63%|██████▎   | 790/1248 [1:18:51<42:31,  5.57s/it]
                                                    
{'loss': 0.532, 'learning_rate': 6.275342252604044e-05, 'epoch': 1.9}

 63%|██████▎   | 790/1248 [1:18:51<42:31,  5.57s/it]
 63%|██████▎   | 791/1248 [1:18:57<42:49,  5.62s/it]
                                                    
{'loss': 0.5159, 'learning_rate': 6.251259443074398e-05, 'epoch': 1.9}

 63%|██████▎   | 791/1248 [1:18:57<42:49,  5.62s/it]
 63%|██████▎   | 792/1248 [1:19:03<44:19,  5.83s/it]
                                                    
{'loss': 0.5775, 'learning_rate': 6.227201904062829e-05, 'epoch': 1.9}

 63%|██████▎   | 792/1248 [1:19:03<44:19,  5.83s/it]
 64%|██████▎   | 793/1248 [1:19:09<44:19,  5.84s/it]
                                                    
{'loss': 0.546, 'learning_rate': 6.203169797742861e-05, 'epoch': 1.91}

 64%|██████▎   | 793/1248 [1:19:09<44:19,  5.84s/it]
 64%|██████▎   | 794/1248 [1:19:15<44:19,  5.86s/it]
                                                    
{'loss': 0.5534, 'learning_rate': 6.179163286116581e-05, 'epoch': 1.91}

 64%|██████▎   | 794/1248 [1:19:15<44:19,  5.86s/it]
 64%|██████▎   | 795/1248 [1:19:21<45:03,  5.97s/it]
                                                    
{'loss': 0.4836, 'learning_rate': 6.155182531013528e-05, 'epoch': 1.91}

 64%|██████▎   | 795/1248 [1:19:21<45:03,  5.97s/it]
 64%|██████▍   | 796/1248 [1:19:27<44:41,  5.93s/it]
                                                    
{'loss': 0.4762, 'learning_rate': 6.13122769408963e-05, 'epoch': 1.91}

 64%|██████▍   | 796/1248 [1:19:27<44:41,  5.93s/it]
 64%|██████▍   | 797/1248 [1:19:33<44:31,  5.92s/it]
                                                    
{'loss': 0.5048, 'learning_rate': 6.107298936826086e-05, 'epoch': 1.92}

 64%|██████▍   | 797/1248 [1:19:33<44:31,  5.92s/it]
 64%|██████▍   | 798/1248 [1:19:39<44:07,  5.88s/it]
                                                    
{'loss': 0.5543, 'learning_rate': 6.083396420528298e-05, 'epoch': 1.92}

 64%|██████▍   | 798/1248 [1:19:39<44:07,  5.88s/it]
 64%|██████▍   | 799/1248 [1:19:45<44:21,  5.93s/it]
                                                    
{'loss': 0.5128, 'learning_rate': 6.059520306324774e-05, 'epoch': 1.92}

 64%|██████▍   | 799/1248 [1:19:45<44:21,  5.93s/it]
 64%|██████▍   | 800/1248 [1:19:51<43:46,  5.86s/it]
                                                    
{'loss': 0.5628, 'learning_rate': 6.035670755166043e-05, 'epoch': 1.92}

 64%|██████▍   | 800/1248 [1:19:51<43:46,  5.86s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 64%|██████▍   | 801/1248 [1:20:17<1:29:19, 11.99s/it]
                                                      
{'loss': 0.4919, 'learning_rate': 6.011847927823567e-05, 'epoch': 1.93}

 64%|██████▍   | 801/1248 [1:20:17<1:29:19, 11.99s/it]
 64%|██████▍   | 802/1248 [1:20:23<1:16:14, 10.26s/it]
                                                      
{'loss': 0.5571, 'learning_rate': 5.988051984888667e-05, 'epoch': 1.93}

 64%|██████▍   | 802/1248 [1:20:23<1:16:14, 10.26s/it]
 64%|██████▍   | 803/1248 [1:20:29<1:06:55,  9.02s/it]
                                                      
{'loss': 0.5824, 'learning_rate': 5.964283086771435e-05, 'epoch': 1.93}

 64%|██████▍   | 803/1248 [1:20:29<1:06:55,  9.02s/it]
 64%|██████▍   | 804/1248 [1:20:35<59:02,  7.98s/it]  
                                                    
{'loss': 0.5059, 'learning_rate': 5.940541393699646e-05, 'epoch': 1.93}

 64%|██████▍   | 804/1248 [1:20:35<59:02,  7.98s/it]
 65%|██████▍   | 805/1248 [1:20:41<54:34,  7.39s/it]
                                                    
{'loss': 0.6107, 'learning_rate': 5.916827065717686e-05, 'epoch': 1.94}

 65%|██████▍   | 805/1248 [1:20:41<54:34,  7.39s/it]
 65%|██████▍   | 806/1248 [1:20:46<50:03,  6.80s/it]
                                                    
{'loss': 0.5222, 'learning_rate': 5.893140262685469e-05, 'epoch': 1.94}

 65%|██████▍   | 806/1248 [1:20:46<50:03,  6.80s/it]
 65%|██████▍   | 807/1248 [1:20:52<47:30,  6.46s/it]
                                                    
{'loss': 0.536, 'learning_rate': 5.869481144277372e-05, 'epoch': 1.94}

 65%|██████▍   | 807/1248 [1:20:52<47:30,  6.46s/it]
 65%|██████▍   | 808/1248 [1:20:59<47:50,  6.52s/it]
                                                    
{'loss': 0.5281, 'learning_rate': 5.845849869981137e-05, 'epoch': 1.94}

 65%|██████▍   | 808/1248 [1:20:59<47:50,  6.52s/it]
 65%|██████▍   | 809/1248 [1:21:04<44:39,  6.10s/it]
                                                    
{'loss': 0.4939, 'learning_rate': 5.8222465990968075e-05, 'epoch': 1.94}

 65%|██████▍   | 809/1248 [1:21:04<44:39,  6.10s/it]
 65%|██████▍   | 810/1248 [1:21:09<43:11,  5.92s/it]
                                                    
{'loss': 0.5198, 'learning_rate': 5.798671490735661e-05, 'epoch': 1.95}

 65%|██████▍   | 810/1248 [1:21:09<43:11,  5.92s/it]
 65%|██████▍   | 811/1248 [1:21:15<42:59,  5.90s/it]
                                                    
{'loss': 0.5373, 'learning_rate': 5.775124703819124e-05, 'epoch': 1.95}

 65%|██████▍   | 811/1248 [1:21:15<42:59,  5.90s/it]
 65%|██████▌   | 812/1248 [1:21:20<41:36,  5.73s/it]
                                                    
{'loss': 0.5254, 'learning_rate': 5.751606397077702e-05, 'epoch': 1.95}

 65%|██████▌   | 812/1248 [1:21:20<41:36,  5.73s/it]
 65%|██████▌   | 813/1248 [1:21:26<41:30,  5.73s/it]
                                                    
{'loss': 0.5531, 'learning_rate': 5.728116729049928e-05, 'epoch': 1.95}

 65%|██████▌   | 813/1248 [1:21:26<41:30,  5.73s/it]
 65%|██████▌   | 814/1248 [1:21:32<40:54,  5.66s/it]
                                                    
{'loss': 0.4936, 'learning_rate': 5.704655858081268e-05, 'epoch': 1.96}

 65%|██████▌   | 814/1248 [1:21:32<40:54,  5.66s/it]
 65%|██████▌   | 815/1248 [1:21:37<40:49,  5.66s/it]
                                                    
{'loss': 0.5251, 'learning_rate': 5.681223942323066e-05, 'epoch': 1.96}

 65%|██████▌   | 815/1248 [1:21:37<40:49,  5.66s/it]
 65%|██████▌   | 816/1248 [1:21:43<41:21,  5.74s/it]
                                                    
{'loss': 0.5315, 'learning_rate': 5.657821139731476e-05, 'epoch': 1.96}

 65%|██████▌   | 816/1248 [1:21:43<41:21,  5.74s/it]
 65%|██████▌   | 817/1248 [1:21:49<42:00,  5.85s/it]
                                                    
{'loss': 0.5475, 'learning_rate': 5.634447608066409e-05, 'epoch': 1.96}

 65%|██████▌   | 817/1248 [1:21:49<42:00,  5.85s/it]
 66%|██████▌   | 818/1248 [1:21:55<42:07,  5.88s/it]
                                                    
{'loss': 0.5086, 'learning_rate': 5.611103504890444e-05, 'epoch': 1.97}

 66%|██████▌   | 818/1248 [1:21:55<42:07,  5.88s/it]
 66%|██████▌   | 819/1248 [1:22:01<41:46,  5.84s/it]
                                                    
{'loss': 0.4843, 'learning_rate': 5.5877889875677845e-05, 'epoch': 1.97}

 66%|██████▌   | 819/1248 [1:22:01<41:46,  5.84s/it]
 66%|██████▌   | 820/1248 [1:22:07<41:10,  5.77s/it]
                                                    
{'loss': 0.4841, 'learning_rate': 5.564504213263205e-05, 'epoch': 1.97}

 66%|██████▌   | 820/1248 [1:22:07<41:10,  5.77s/it]
 66%|██████▌   | 821/1248 [1:22:12<40:31,  5.69s/it]
                                                    
{'loss': 0.5269, 'learning_rate': 5.541249338940968e-05, 'epoch': 1.97}

 66%|██████▌   | 821/1248 [1:22:12<40:31,  5.69s/it]
 66%|██████▌   | 822/1248 [1:22:18<40:38,  5.72s/it]
                                                    
{'loss': 0.5199, 'learning_rate': 5.518024521363778e-05, 'epoch': 1.98}

 66%|██████▌   | 822/1248 [1:22:18<40:38,  5.72s/it]
 66%|██████▌   | 823/1248 [1:22:23<40:04,  5.66s/it]
                                                    
{'loss': 0.5196, 'learning_rate': 5.4948299170917325e-05, 'epoch': 1.98}

 66%|██████▌   | 823/1248 [1:22:23<40:04,  5.66s/it]
 66%|██████▌   | 824/1248 [1:22:30<41:11,  5.83s/it]
                                                    
{'loss': 0.5408, 'learning_rate': 5.4716656824812505e-05, 'epoch': 1.98}

 66%|██████▌   | 824/1248 [1:22:30<41:11,  5.83s/it]
 66%|██████▌   | 825/1248 [1:22:35<40:48,  5.79s/it]
                                                    
{'loss': 0.5438, 'learning_rate': 5.448531973684039e-05, 'epoch': 1.98}

 66%|██████▌   | 825/1248 [1:22:35<40:48,  5.79s/it]
 66%|██████▌   | 826/1248 [1:22:41<40:27,  5.75s/it]
                                                    
{'loss': 0.4914, 'learning_rate': 5.425428946646016e-05, 'epoch': 1.99}

 66%|██████▌   | 826/1248 [1:22:41<40:27,  5.75s/it]
 66%|██████▋   | 827/1248 [1:22:47<40:04,  5.71s/it]
                                                    
{'loss': 0.5556, 'learning_rate': 5.4023567571062774e-05, 'epoch': 1.99}

 66%|██████▋   | 827/1248 [1:22:47<40:04,  5.71s/it]
 66%|██████▋   | 828/1248 [1:22:52<39:56,  5.71s/it]
                                                    
{'loss': 0.5119, 'learning_rate': 5.379315560596038e-05, 'epoch': 1.99}

 66%|██████▋   | 828/1248 [1:22:52<39:56,  5.71s/it]
 66%|██████▋   | 829/1248 [1:22:58<40:06,  5.74s/it]
                                                    
{'loss': 0.5408, 'learning_rate': 5.356305512437594e-05, 'epoch': 1.99}

 66%|██████▋   | 829/1248 [1:22:58<40:06,  5.74s/it]
 67%|██████▋   | 830/1248 [1:23:04<39:35,  5.68s/it]
                                                    
{'loss': 0.5287, 'learning_rate': 5.333326767743263e-05, 'epoch': 2.0}

 67%|██████▋   | 830/1248 [1:23:04<39:35,  5.68s/it]
 67%|██████▋   | 831/1248 [1:23:10<40:41,  5.85s/it]
                                                    
{'loss': 0.6645, 'learning_rate': 5.3103794814143425e-05, 'epoch': 2.0}

 67%|██████▋   | 831/1248 [1:23:10<40:41,  5.85s/it]
 67%|██████▋   | 832/1248 [1:23:16<41:43,  6.02s/it]
                                                    
{'loss': 0.5139, 'learning_rate': 5.2874638081400694e-05, 'epoch': 2.0}

 67%|██████▋   | 832/1248 [1:23:16<41:43,  6.02s/it]
 67%|██████▋   | 833/1248 [1:23:22<41:04,  5.94s/it]
                                                    
{'loss': 0.4981, 'learning_rate': 5.26457990239657e-05, 'epoch': 2.0}

 67%|██████▋   | 833/1248 [1:23:22<41:04,  5.94s/it]
 67%|██████▋   | 834/1248 [1:23:28<41:41,  6.04s/it]
                                                    
{'loss': 0.5396, 'learning_rate': 5.241727918445836e-05, 'epoch': 2.0}

 67%|██████▋   | 834/1248 [1:23:28<41:41,  6.04s/it]
 67%|██████▋   | 835/1248 [1:23:34<41:36,  6.04s/it]
                                                    
{'loss': 0.5057, 'learning_rate': 5.21890801033466e-05, 'epoch': 2.01}

 67%|██████▋   | 835/1248 [1:23:34<41:36,  6.04s/it]
 67%|██████▋   | 836/1248 [1:23:40<40:24,  5.88s/it]
                                                    
{'loss': 0.5281, 'learning_rate': 5.1961203318936116e-05, 'epoch': 2.01}

 67%|██████▋   | 836/1248 [1:23:40<40:24,  5.88s/it]
 67%|██████▋   | 837/1248 [1:23:45<39:13,  5.73s/it]
                                                    
{'loss': 0.5175, 'learning_rate': 5.1733650367359964e-05, 'epoch': 2.01}

 67%|██████▋   | 837/1248 [1:23:45<39:13,  5.73s/it]
 67%|██████▋   | 838/1248 [1:23:51<39:13,  5.74s/it]
                                                    
{'loss': 0.515, 'learning_rate': 5.1506422782568345e-05, 'epoch': 2.01}

 67%|██████▋   | 838/1248 [1:23:51<39:13,  5.74s/it]
 67%|██████▋   | 839/1248 [1:23:57<39:21,  5.77s/it]
                                                    
{'loss': 0.5244, 'learning_rate': 5.127952209631797e-05, 'epoch': 2.02}

 67%|██████▋   | 839/1248 [1:23:57<39:21,  5.77s/it]
 67%|██████▋   | 840/1248 [1:24:03<39:25,  5.80s/it]
                                                    
{'loss': 0.5194, 'learning_rate': 5.105294983816202e-05, 'epoch': 2.02}

 67%|██████▋   | 840/1248 [1:24:03<39:25,  5.80s/it]
 67%|██████▋   | 841/1248 [1:24:08<39:14,  5.79s/it]
                                                    
{'loss': 0.526, 'learning_rate': 5.082670753543961e-05, 'epoch': 2.02}

 67%|██████▋   | 841/1248 [1:24:08<39:14,  5.79s/it]
 67%|██████▋   | 842/1248 [1:24:15<40:00,  5.91s/it]
                                                    
{'loss': 0.522, 'learning_rate': 5.0600796713265765e-05, 'epoch': 2.02}

 67%|██████▋   | 842/1248 [1:24:15<40:00,  5.91s/it]
 68%|██████▊   | 843/1248 [1:24:20<39:35,  5.87s/it]
                                                    
{'loss': 0.5434, 'learning_rate': 5.0375218894520834e-05, 'epoch': 2.03}

 68%|██████▊   | 843/1248 [1:24:20<39:35,  5.87s/it]
 68%|██████▊   | 844/1248 [1:24:26<38:37,  5.74s/it]
                                                    
{'loss': 0.4654, 'learning_rate': 5.014997559984045e-05, 'epoch': 2.03}

 68%|██████▊   | 844/1248 [1:24:26<38:37,  5.74s/it]
 68%|██████▊   | 845/1248 [1:24:32<38:35,  5.75s/it]
                                                    
{'loss': 0.5147, 'learning_rate': 4.9925068347605117e-05, 'epoch': 2.03}

 68%|██████▊   | 845/1248 [1:24:32<38:35,  5.75s/it]
 68%|██████▊   | 846/1248 [1:24:37<38:39,  5.77s/it]
                                                    
{'loss': 0.5171, 'learning_rate': 4.970049865393008e-05, 'epoch': 2.03}

 68%|██████▊   | 846/1248 [1:24:37<38:39,  5.77s/it]
 68%|██████▊   | 847/1248 [1:24:43<38:04,  5.70s/it]
                                                    
{'loss': 0.5975, 'learning_rate': 4.947626803265519e-05, 'epoch': 2.04}

 68%|██████▊   | 847/1248 [1:24:43<38:04,  5.70s/it]
 68%|██████▊   | 848/1248 [1:24:49<38:26,  5.77s/it]
                                                    
{'loss': 0.4986, 'learning_rate': 4.9252377995334444e-05, 'epoch': 2.04}

 68%|██████▊   | 848/1248 [1:24:49<38:26,  5.77s/it]
 68%|██████▊   | 849/1248 [1:24:55<38:36,  5.81s/it]
                                                    
{'loss': 0.5162, 'learning_rate': 4.9028830051225994e-05, 'epoch': 2.04}

 68%|██████▊   | 849/1248 [1:24:55<38:36,  5.81s/it]
 68%|██████▊   | 850/1248 [1:25:01<38:32,  5.81s/it]
                                                    
{'loss': 0.4652, 'learning_rate': 4.8805625707281877e-05, 'epoch': 2.04}

 68%|██████▊   | 850/1248 [1:25:01<38:32,  5.81s/it]
 68%|██████▊   | 851/1248 [1:25:06<38:20,  5.80s/it]
                                                    
{'loss': 0.5521, 'learning_rate': 4.8582766468138005e-05, 'epoch': 2.05}

 68%|██████▊   | 851/1248 [1:25:06<38:20,  5.80s/it]
 68%|██████▊   | 852/1248 [1:25:12<37:37,  5.70s/it]
                                                    
{'loss': 0.4842, 'learning_rate': 4.836025383610382e-05, 'epoch': 2.05}

 68%|██████▊   | 852/1248 [1:25:12<37:37,  5.70s/it]
 68%|██████▊   | 853/1248 [1:25:17<36:50,  5.60s/it]
                                                    
{'loss': 0.5294, 'learning_rate': 4.813808931115228e-05, 'epoch': 2.05}

 68%|██████▊   | 853/1248 [1:25:17<36:50,  5.60s/it]
 68%|██████▊   | 854/1248 [1:25:23<36:57,  5.63s/it]
                                                    
{'loss': 0.4995, 'learning_rate': 4.791627439090975e-05, 'epoch': 2.05}

 68%|██████▊   | 854/1248 [1:25:23<36:57,  5.63s/it]
 69%|██████▊   | 855/1248 [1:25:28<35:44,  5.46s/it]
                                                    
{'loss': 0.4943, 'learning_rate': 4.7694810570645795e-05, 'epoch': 2.06}

 69%|██████▊   | 855/1248 [1:25:28<35:44,  5.46s/it]
 69%|██████▊   | 856/1248 [1:25:33<34:58,  5.35s/it]
                                                    
{'loss': 0.5486, 'learning_rate': 4.74736993432634e-05, 'epoch': 2.06}

 69%|██████▊   | 856/1248 [1:25:33<34:58,  5.35s/it]
 69%|██████▊   | 857/1248 [1:25:39<34:59,  5.37s/it]
                                                    
{'loss': 0.4907, 'learning_rate': 4.7252942199288487e-05, 'epoch': 2.06}

 69%|██████▊   | 857/1248 [1:25:39<34:59,  5.37s/it]
 69%|██████▉   | 858/1248 [1:25:44<34:43,  5.34s/it]
                                                    
{'loss': 0.4713, 'learning_rate': 4.703254062686017e-05, 'epoch': 2.06}

 69%|██████▉   | 858/1248 [1:25:44<34:43,  5.34s/it]
 69%|██████▉   | 859/1248 [1:25:49<34:08,  5.27s/it]
                                                    
{'loss': 0.5018, 'learning_rate': 4.6812496111720585e-05, 'epoch': 2.06}

 69%|██████▉   | 859/1248 [1:25:49<34:08,  5.27s/it]
 69%|██████▉   | 860/1248 [1:25:54<34:07,  5.28s/it]
                                                    
{'loss': 0.4636, 'learning_rate': 4.6592810137205e-05, 'epoch': 2.07}

 69%|██████▉   | 860/1248 [1:25:54<34:07,  5.28s/it]
 69%|██████▉   | 861/1248 [1:26:00<34:48,  5.40s/it]
                                                    
{'loss': 0.4841, 'learning_rate': 4.637348418423169e-05, 'epoch': 2.07}

 69%|██████▉   | 861/1248 [1:26:00<34:48,  5.40s/it]
 69%|██████▉   | 862/1248 [1:26:07<37:33,  5.84s/it]
                                                    
{'loss': 0.4869, 'learning_rate': 4.615451973129196e-05, 'epoch': 2.07}

 69%|██████▉   | 862/1248 [1:26:07<37:33,  5.84s/it]
 69%|██████▉   | 863/1248 [1:26:12<36:06,  5.63s/it]
                                                    
{'loss': 0.4933, 'learning_rate': 4.593591825444028e-05, 'epoch': 2.07}

 69%|██████▉   | 863/1248 [1:26:12<36:06,  5.63s/it]
 69%|██████▉   | 864/1248 [1:26:18<36:03,  5.63s/it]
                                                    
{'loss': 0.5078, 'learning_rate': 4.57176812272842e-05, 'epoch': 2.08}

 69%|██████▉   | 864/1248 [1:26:18<36:03,  5.63s/it]
 69%|██████▉   | 865/1248 [1:26:23<35:15,  5.52s/it]
                                                    
{'loss': 0.5275, 'learning_rate': 4.5499810120974616e-05, 'epoch': 2.08}

 69%|██████▉   | 865/1248 [1:26:23<35:15,  5.52s/it]
 69%|██████▉   | 866/1248 [1:26:29<36:12,  5.69s/it]
                                                    
{'loss': 0.5332, 'learning_rate': 4.528230640419562e-05, 'epoch': 2.08}

 69%|██████▉   | 866/1248 [1:26:29<36:12,  5.69s/it]
 69%|██████▉   | 867/1248 [1:26:35<37:22,  5.88s/it]
                                                    
{'loss': 0.5347, 'learning_rate': 4.5065171543154725e-05, 'epoch': 2.08}

 69%|██████▉   | 867/1248 [1:26:35<37:22,  5.88s/it]
 70%|██████▉   | 868/1248 [1:26:41<36:38,  5.79s/it]
                                                    
{'loss': 0.4948, 'learning_rate': 4.484840700157295e-05, 'epoch': 2.09}

 70%|██████▉   | 868/1248 [1:26:41<36:38,  5.79s/it]
 70%|██████▉   | 869/1248 [1:26:47<36:51,  5.83s/it]
                                                    
{'loss': 0.527, 'learning_rate': 4.4632014240675034e-05, 'epoch': 2.09}

 70%|██████▉   | 869/1248 [1:26:47<36:51,  5.83s/it]
 70%|██████▉   | 870/1248 [1:26:52<36:27,  5.79s/it]
                                                    
{'loss': 0.5066, 'learning_rate': 4.4415994719179456e-05, 'epoch': 2.09}

 70%|██████▉   | 870/1248 [1:26:52<36:27,  5.79s/it]
 70%|██████▉   | 871/1248 [1:26:58<36:22,  5.79s/it]
                                                    
{'loss': 0.5595, 'learning_rate': 4.420034989328866e-05, 'epoch': 2.09}

 70%|██████▉   | 871/1248 [1:26:58<36:22,  5.79s/it]
 70%|██████▉   | 872/1248 [1:27:04<36:26,  5.81s/it]
                                                    
{'loss': 0.4583, 'learning_rate': 4.398508121667925e-05, 'epoch': 2.1}

 70%|██████▉   | 872/1248 [1:27:04<36:26,  5.81s/it]
 70%|██████▉   | 873/1248 [1:27:10<36:29,  5.84s/it]
                                                    
{'loss': 0.4733, 'learning_rate': 4.377019014049223e-05, 'epoch': 2.1}

 70%|██████▉   | 873/1248 [1:27:10<36:29,  5.84s/it]
 70%|███████   | 874/1248 [1:27:16<36:10,  5.80s/it]
                                                    
{'loss': 0.526, 'learning_rate': 4.355567811332311e-05, 'epoch': 2.1}

 70%|███████   | 874/1248 [1:27:16<36:10,  5.80s/it]
 70%|███████   | 875/1248 [1:27:21<36:05,  5.81s/it]
                                                    
{'loss': 0.4948, 'learning_rate': 4.334154658121222e-05, 'epoch': 2.1}

 70%|███████   | 875/1248 [1:27:21<36:05,  5.81s/it]
 70%|███████   | 876/1248 [1:27:27<35:46,  5.77s/it]
                                                    
{'loss': 0.5079, 'learning_rate': 4.312779698763493e-05, 'epoch': 2.11}

 70%|███████   | 876/1248 [1:27:27<35:46,  5.77s/it]
 70%|███████   | 877/1248 [1:27:33<35:52,  5.80s/it]
                                                    
{'loss': 0.5342, 'learning_rate': 4.2914430773492035e-05, 'epoch': 2.11}

 70%|███████   | 877/1248 [1:27:33<35:52,  5.80s/it]
 70%|███████   | 878/1248 [1:27:39<36:22,  5.90s/it]
                                                    
{'loss': 0.4851, 'learning_rate': 4.270144937709981e-05, 'epoch': 2.11}

 70%|███████   | 878/1248 [1:27:39<36:22,  5.90s/it]
 70%|███████   | 879/1248 [1:27:45<36:57,  6.01s/it]
                                                    
{'loss': 0.481, 'learning_rate': 4.24888542341805e-05, 'epoch': 2.11}

 70%|███████   | 879/1248 [1:27:45<36:57,  6.01s/it]
 71%|███████   | 880/1248 [1:27:51<36:24,  5.94s/it]
                                                    
{'loss': 0.4784, 'learning_rate': 4.2276646777852636e-05, 'epoch': 2.12}

 71%|███████   | 880/1248 [1:27:51<36:24,  5.94s/it]
 71%|███████   | 881/1248 [1:27:57<36:10,  5.92s/it]
                                                    
{'loss': 0.5263, 'learning_rate': 4.206482843862126e-05, 'epoch': 2.12}

 71%|███████   | 881/1248 [1:27:57<36:10,  5.92s/it]
 71%|███████   | 882/1248 [1:28:03<36:12,  5.93s/it]
                                                    
{'loss': 0.5085, 'learning_rate': 4.185340064436839e-05, 'epoch': 2.12}

 71%|███████   | 882/1248 [1:28:03<36:12,  5.93s/it]
 71%|███████   | 883/1248 [1:28:09<36:23,  5.98s/it]
                                                    
{'loss': 0.4933, 'learning_rate': 4.164236482034327e-05, 'epoch': 2.12}

 71%|███████   | 883/1248 [1:28:09<36:23,  5.98s/it]
 71%|███████   | 884/1248 [1:28:14<34:43,  5.72s/it]
                                                    
{'loss': 0.4742, 'learning_rate': 4.1431722389153016e-05, 'epoch': 2.12}

 71%|███████   | 884/1248 [1:28:14<34:43,  5.72s/it]
 71%|███████   | 885/1248 [1:28:20<33:53,  5.60s/it]
                                                    
{'loss': 0.5245, 'learning_rate': 4.12214747707527e-05, 'epoch': 2.13}

 71%|███████   | 885/1248 [1:28:20<33:53,  5.60s/it]
 71%|███████   | 886/1248 [1:28:25<33:21,  5.53s/it]
                                                    
{'loss': 0.5098, 'learning_rate': 4.101162338243595e-05, 'epoch': 2.13}

 71%|███████   | 886/1248 [1:28:25<33:21,  5.53s/it]
 71%|███████   | 887/1248 [1:28:30<32:47,  5.45s/it]
                                                    
{'loss': 0.497, 'learning_rate': 4.080216963882548e-05, 'epoch': 2.13}

 71%|███████   | 887/1248 [1:28:30<32:47,  5.45s/it]
 71%|███████   | 888/1248 [1:28:36<32:53,  5.48s/it]
                                                    
{'loss': 0.4982, 'learning_rate': 4.059311495186338e-05, 'epoch': 2.13}

 71%|███████   | 888/1248 [1:28:36<32:53,  5.48s/it]
 71%|███████   | 889/1248 [1:28:42<33:51,  5.66s/it]
                                                    
{'loss': 0.5363, 'learning_rate': 4.0384460730801667e-05, 'epoch': 2.14}

 71%|███████   | 889/1248 [1:28:42<33:51,  5.66s/it]
 71%|███████▏  | 890/1248 [1:28:48<34:08,  5.72s/it]
                                                    
{'loss': 0.4962, 'learning_rate': 4.017620838219276e-05, 'epoch': 2.14}

 71%|███████▏  | 890/1248 [1:28:48<34:08,  5.72s/it]
 71%|███████▏  | 891/1248 [1:28:53<33:15,  5.59s/it]
                                                    
{'loss': 0.4941, 'learning_rate': 3.9968359309880156e-05, 'epoch': 2.14}

 71%|███████▏  | 891/1248 [1:28:53<33:15,  5.59s/it]
 71%|███████▏  | 892/1248 [1:28:58<32:38,  5.50s/it]
                                                    
{'loss': 0.5204, 'learning_rate': 3.976091491498871e-05, 'epoch': 2.14}

 71%|███████▏  | 892/1248 [1:28:58<32:38,  5.50s/it]
 72%|███████▏  | 893/1248 [1:29:04<32:09,  5.43s/it]
                                                    
{'loss': 0.4859, 'learning_rate': 3.9553876595915375e-05, 'epoch': 2.15}

 72%|███████▏  | 893/1248 [1:29:04<32:09,  5.43s/it]
 72%|███████▏  | 894/1248 [1:29:09<32:12,  5.46s/it]
                                                    
{'loss': 0.5024, 'learning_rate': 3.9347245748319705e-05, 'epoch': 2.15}

 72%|███████▏  | 894/1248 [1:29:09<32:12,  5.46s/it]
 72%|███████▏  | 895/1248 [1:29:15<32:54,  5.59s/it]
                                                    
{'loss': 0.5205, 'learning_rate': 3.9141023765114426e-05, 'epoch': 2.15}

 72%|███████▏  | 895/1248 [1:29:15<32:54,  5.59s/it]
 72%|███████▏  | 896/1248 [1:29:20<32:06,  5.47s/it]
                                                    
{'loss': 0.4734, 'learning_rate': 3.893521203645618e-05, 'epoch': 2.15}

 72%|███████▏  | 896/1248 [1:29:20<32:06,  5.47s/it]
 72%|███████▏  | 897/1248 [1:29:26<31:51,  5.44s/it]
                                                    
{'loss': 0.5131, 'learning_rate': 3.872981194973597e-05, 'epoch': 2.16}

 72%|███████▏  | 897/1248 [1:29:26<31:51,  5.44s/it]
 72%|███████▏  | 898/1248 [1:29:31<32:23,  5.55s/it]
                                                    
{'loss': 0.5162, 'learning_rate': 3.852482488956992e-05, 'epoch': 2.16}

 72%|███████▏  | 898/1248 [1:29:31<32:23,  5.55s/it]
 72%|███████▏  | 899/1248 [1:29:37<32:25,  5.57s/it]
                                                    
{'loss': 0.5365, 'learning_rate': 3.8320252237789865e-05, 'epoch': 2.16}

 72%|███████▏  | 899/1248 [1:29:37<32:25,  5.57s/it]
 72%|███████▏  | 900/1248 [1:29:43<33:03,  5.70s/it]
                                                    
{'loss': 0.4888, 'learning_rate': 3.81160953734342e-05, 'epoch': 2.16}

 72%|███████▏  | 900/1248 [1:29:43<33:03,  5.70s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 72%|███████▏  | 901/1248 [1:30:08<1:07:24, 11.65s/it]
                                                      
{'loss': 0.5155, 'learning_rate': 3.7912355672738364e-05, 'epoch': 2.17}

 72%|███████▏  | 901/1248 [1:30:09<1:07:24, 11.65s/it]
 72%|███████▏  | 902/1248 [1:30:14<56:49,  9.85s/it]  
                                                    
{'loss': 0.4999, 'learning_rate': 3.770903450912571e-05, 'epoch': 2.17}

 72%|███████▏  | 902/1248 [1:30:14<56:49,  9.85s/it]
 72%|███████▏  | 903/1248 [1:30:19<48:50,  8.50s/it]
                                                    
{'loss': 0.5304, 'learning_rate': 3.750613325319817e-05, 'epoch': 2.17}

 72%|███████▏  | 903/1248 [1:30:19<48:50,  8.50s/it]
 72%|███████▏  | 904/1248 [1:30:25<43:08,  7.53s/it]
                                                    
{'loss': 0.5313, 'learning_rate': 3.7303653272727057e-05, 'epoch': 2.17}

 72%|███████▏  | 904/1248 [1:30:25<43:08,  7.53s/it]
 73%|███████▎  | 905/1248 [1:30:30<39:01,  6.83s/it]
                                                    
{'loss': 0.4928, 'learning_rate': 3.710159593264392e-05, 'epoch': 2.18}

 73%|███████▎  | 905/1248 [1:30:30<39:01,  6.83s/it]
 73%|███████▎  | 906/1248 [1:30:36<36:58,  6.49s/it]
                                                    
{'loss': 0.4939, 'learning_rate': 3.689996259503116e-05, 'epoch': 2.18}

 73%|███████▎  | 906/1248 [1:30:36<36:58,  6.49s/it]
 73%|███████▎  | 907/1248 [1:30:41<35:08,  6.18s/it]
                                                    
{'loss': 0.5545, 'learning_rate': 3.669875461911297e-05, 'epoch': 2.18}

 73%|███████▎  | 907/1248 [1:30:41<35:08,  6.18s/it]
 73%|███████▎  | 908/1248 [1:30:47<34:19,  6.06s/it]
                                                    
{'loss': 0.5103, 'learning_rate': 3.649797336124615e-05, 'epoch': 2.18}

 73%|███████▎  | 908/1248 [1:30:47<34:19,  6.06s/it]
 73%|███████▎  | 909/1248 [1:30:52<33:01,  5.84s/it]
                                                    
{'loss': 0.4847, 'learning_rate': 3.629762017491104e-05, 'epoch': 2.19}

 73%|███████▎  | 909/1248 [1:30:52<33:01,  5.84s/it]
 73%|███████▎  | 910/1248 [1:30:57<31:38,  5.62s/it]
                                                    
{'loss': 0.4809, 'learning_rate': 3.609769641070221e-05, 'epoch': 2.19}

 73%|███████▎  | 910/1248 [1:30:57<31:38,  5.62s/it]
 73%|███████▎  | 911/1248 [1:31:02<30:47,  5.48s/it]
                                                    
{'loss': 0.4572, 'learning_rate': 3.589820341631951e-05, 'epoch': 2.19}

 73%|███████▎  | 911/1248 [1:31:02<30:47,  5.48s/it]
 73%|███████▎  | 912/1248 [1:31:08<31:11,  5.57s/it]
                                                    
{'loss': 0.5014, 'learning_rate': 3.569914253655896e-05, 'epoch': 2.19}

 73%|███████▎  | 912/1248 [1:31:08<31:11,  5.57s/it]
 73%|███████▎  | 913/1248 [1:31:14<31:49,  5.70s/it]
                                                    
{'loss': 0.5029, 'learning_rate': 3.550051511330361e-05, 'epoch': 2.19}

 73%|███████▎  | 913/1248 [1:31:14<31:49,  5.70s/it]
 73%|███████▎  | 914/1248 [1:31:20<32:02,  5.76s/it]
                                                    
{'loss': 0.4783, 'learning_rate': 3.530232248551466e-05, 'epoch': 2.2}

 73%|███████▎  | 914/1248 [1:31:20<32:02,  5.76s/it]
 73%|███████▎  | 915/1248 [1:31:26<32:27,  5.85s/it]
                                                    
{'loss': 0.5933, 'learning_rate': 3.510456598922221e-05, 'epoch': 2.2}

 73%|███████▎  | 915/1248 [1:31:26<32:27,  5.85s/it]
 73%|███████▎  | 916/1248 [1:31:32<32:16,  5.83s/it]
                                                    
{'loss': 0.4969, 'learning_rate': 3.490724695751642e-05, 'epoch': 2.2}

 73%|███████▎  | 916/1248 [1:31:32<32:16,  5.83s/it]
 73%|███████▎  | 917/1248 [1:31:38<32:27,  5.88s/it]
                                                    
{'loss': 0.5163, 'learning_rate': 3.4710366720538415e-05, 'epoch': 2.2}

 73%|███████▎  | 917/1248 [1:31:38<32:27,  5.88s/it]
 74%|███████▎  | 918/1248 [1:31:44<32:43,  5.95s/it]
                                                    
{'loss': 0.4814, 'learning_rate': 3.45139266054715e-05, 'epoch': 2.21}

 74%|███████▎  | 918/1248 [1:31:44<32:43,  5.95s/it]
 74%|███████▎  | 919/1248 [1:31:55<41:33,  7.58s/it]
                                                    
{'loss': 0.6026, 'learning_rate': 3.431792793653198e-05, 'epoch': 2.21}

 74%|███████▎  | 919/1248 [1:31:55<41:33,  7.58s/it]
 74%|███████▎  | 920/1248 [1:32:01<38:33,  7.05s/it]
                                                    
{'loss': 0.5093, 'learning_rate': 3.412237203496036e-05, 'epoch': 2.21}

 74%|███████▎  | 920/1248 [1:32:01<38:33,  7.05s/it]
 74%|███████▍  | 921/1248 [1:32:08<37:08,  6.82s/it]
                                                    
{'loss': 0.5113, 'learning_rate': 3.392726021901244e-05, 'epoch': 2.21}

 74%|███████▍  | 921/1248 [1:32:08<37:08,  6.82s/it]
 74%|███████▍  | 922/1248 [1:32:13<35:12,  6.48s/it]
                                                    
{'loss': 0.513, 'learning_rate': 3.3732593803950355e-05, 'epoch': 2.22}

 74%|███████▍  | 922/1248 [1:32:13<35:12,  6.48s/it]
 74%|███████▍  | 923/1248 [1:32:19<34:31,  6.37s/it]
                                                    
{'loss': 0.5165, 'learning_rate': 3.3538374102033866e-05, 'epoch': 2.22}

 74%|███████▍  | 923/1248 [1:32:19<34:31,  6.37s/it]
 74%|███████▍  | 924/1248 [1:32:25<33:25,  6.19s/it]
                                                    
{'loss': 0.5024, 'learning_rate': 3.334460242251134e-05, 'epoch': 2.22}

 74%|███████▍  | 924/1248 [1:32:25<33:25,  6.19s/it]
 74%|███████▍  | 925/1248 [1:32:31<32:40,  6.07s/it]
                                                    
{'loss': 0.4992, 'learning_rate': 3.315128007161099e-05, 'epoch': 2.22}

 74%|███████▍  | 925/1248 [1:32:31<32:40,  6.07s/it]
 74%|███████▍  | 926/1248 [1:32:37<32:17,  6.02s/it]
                                                    
{'loss': 0.4679, 'learning_rate': 3.295840835253206e-05, 'epoch': 2.23}

 74%|███████▍  | 926/1248 [1:32:37<32:17,  6.02s/it]
 74%|███████▍  | 927/1248 [1:32:43<32:33,  6.09s/it]
                                                    
{'loss': 0.5223, 'learning_rate': 3.276598856543614e-05, 'epoch': 2.23}

 74%|███████▍  | 927/1248 [1:32:43<32:33,  6.09s/it]
 74%|███████▍  | 928/1248 [1:32:49<32:37,  6.12s/it]
                                                    
{'loss': 0.5201, 'learning_rate': 3.257402200743821e-05, 'epoch': 2.23}

 74%|███████▍  | 928/1248 [1:32:49<32:37,  6.12s/it]
 74%|███████▍  | 929/1248 [1:32:55<32:13,  6.06s/it]
                                                    
{'loss': 0.515, 'learning_rate': 3.238250997259808e-05, 'epoch': 2.23}

 74%|███████▍  | 929/1248 [1:32:55<32:13,  6.06s/it]
 75%|███████▍  | 930/1248 [1:33:01<31:33,  5.96s/it]
                                                    
{'loss': 0.5379, 'learning_rate': 3.21914537519115e-05, 'epoch': 2.24}

 75%|███████▍  | 930/1248 [1:33:01<31:33,  5.96s/it]
 75%|███████▍  | 931/1248 [1:33:07<31:19,  5.93s/it]
                                                    
{'loss': 0.5239, 'learning_rate': 3.2000854633301694e-05, 'epoch': 2.24}

 75%|███████▍  | 931/1248 [1:33:07<31:19,  5.93s/it]
 75%|███████▍  | 932/1248 [1:33:13<31:12,  5.93s/it]
                                                    
{'loss': 0.5118, 'learning_rate': 3.181071390161037e-05, 'epoch': 2.24}

 75%|███████▍  | 932/1248 [1:33:13<31:12,  5.93s/it]
 75%|███████▍  | 933/1248 [1:33:19<31:22,  5.98s/it]
                                                    
{'loss': 0.4582, 'learning_rate': 3.1621032838589305e-05, 'epoch': 2.24}

 75%|███████▍  | 933/1248 [1:33:19<31:22,  5.98s/it]
 75%|███████▍  | 934/1248 [1:33:25<31:02,  5.93s/it]
                                                    
{'loss': 0.516, 'learning_rate': 3.1431812722891594e-05, 'epoch': 2.25}

 75%|███████▍  | 934/1248 [1:33:25<31:02,  5.93s/it]
 75%|███████▍  | 935/1248 [1:33:31<31:03,  5.95s/it]
                                                    
{'loss': 0.5142, 'learning_rate': 3.1243054830063035e-05, 'epoch': 2.25}

 75%|███████▍  | 935/1248 [1:33:31<31:03,  5.95s/it]
 75%|███████▌  | 936/1248 [1:33:37<31:58,  6.15s/it]
                                                    
{'loss': 0.596, 'learning_rate': 3.1054760432533624e-05, 'epoch': 2.25}

 75%|███████▌  | 936/1248 [1:33:37<31:58,  6.15s/it]
 75%|███████▌  | 937/1248 [1:33:43<31:40,  6.11s/it]
                                                    
{'loss': 0.5092, 'learning_rate': 3.086693079960883e-05, 'epoch': 2.25}

 75%|███████▌  | 937/1248 [1:33:43<31:40,  6.11s/it]
 75%|███████▌  | 938/1248 [1:33:49<31:32,  6.10s/it]
                                                    
{'loss': 0.4874, 'learning_rate': 3.0679567197461134e-05, 'epoch': 2.25}

 75%|███████▌  | 938/1248 [1:33:49<31:32,  6.10s/it]
 75%|███████▌  | 939/1248 [1:33:55<31:24,  6.10s/it]
                                                    
{'loss': 0.5213, 'learning_rate': 3.0492670889121433e-05, 'epoch': 2.26}

 75%|███████▌  | 939/1248 [1:33:55<31:24,  6.10s/it]
 75%|███████▌  | 940/1248 [1:34:01<31:09,  6.07s/it]
                                                    
{'loss': 0.5306, 'learning_rate': 3.030624313447067e-05, 'epoch': 2.26}

 75%|███████▌  | 940/1248 [1:34:01<31:09,  6.07s/it]
 75%|███████▌  | 941/1248 [1:34:07<30:41,  6.00s/it]
                                                    
{'loss': 0.5029, 'learning_rate': 3.0120285190231144e-05, 'epoch': 2.26}

 75%|███████▌  | 941/1248 [1:34:07<30:41,  6.00s/it]
 75%|███████▌  | 942/1248 [1:34:13<30:37,  6.00s/it]
                                                    
{'loss': 0.5299, 'learning_rate': 2.9934798309958146e-05, 'epoch': 2.26}

 75%|███████▌  | 942/1248 [1:34:13<30:37,  6.00s/it]
 76%|███████▌  | 943/1248 [1:34:19<29:59,  5.90s/it]
                                                    
{'loss': 0.5384, 'learning_rate': 2.974978374403147e-05, 'epoch': 2.27}

 76%|███████▌  | 943/1248 [1:34:19<29:59,  5.90s/it]
 76%|███████▌  | 944/1248 [1:34:25<30:10,  5.96s/it]
                                                    
{'loss': 0.5083, 'learning_rate': 2.9565242739647114e-05, 'epoch': 2.27}

 76%|███████▌  | 944/1248 [1:34:25<30:10,  5.96s/it]
 76%|███████▌  | 945/1248 [1:34:31<29:47,  5.90s/it]
                                                    
{'loss': 0.5724, 'learning_rate': 2.938117654080863e-05, 'epoch': 2.27}

 76%|███████▌  | 945/1248 [1:34:31<29:47,  5.90s/it]
 76%|███████▌  | 946/1248 [1:34:36<29:11,  5.80s/it]
                                                    
{'loss': 0.4805, 'learning_rate': 2.9197586388318932e-05, 'epoch': 2.27}

 76%|███████▌  | 946/1248 [1:34:36<29:11,  5.80s/it]
 76%|███████▌  | 947/1248 [1:34:43<29:39,  5.91s/it]
                                                    
{'loss': 0.5247, 'learning_rate': 2.9014473519771913e-05, 'epoch': 2.28}

 76%|███████▌  | 947/1248 [1:34:43<29:39,  5.91s/it]
 76%|███████▌  | 948/1248 [1:34:49<29:42,  5.94s/it]
                                                    
{'loss': 0.5304, 'learning_rate': 2.8831839169543996e-05, 'epoch': 2.28}

 76%|███████▌  | 948/1248 [1:34:49<29:42,  5.94s/it]
 76%|███████▌  | 949/1248 [1:34:54<29:37,  5.95s/it]
                                                    
{'loss': 0.5059, 'learning_rate': 2.864968456878586e-05, 'epoch': 2.28}

 76%|███████▌  | 949/1248 [1:34:54<29:37,  5.95s/it]
 76%|███████▌  | 950/1248 [1:35:00<29:31,  5.95s/it]
                                                    
{'loss': 0.4839, 'learning_rate': 2.8468010945414303e-05, 'epoch': 2.28}

 76%|███████▌  | 950/1248 [1:35:00<29:31,  5.95s/it]
 76%|███████▌  | 951/1248 [1:35:06<28:57,  5.85s/it]
                                                    
{'loss': 0.4722, 'learning_rate': 2.828681952410366e-05, 'epoch': 2.29}

 76%|███████▌  | 951/1248 [1:35:06<28:57,  5.85s/it]
 76%|███████▋  | 952/1248 [1:35:12<28:54,  5.86s/it]
                                                    
{'loss': 0.5168, 'learning_rate': 2.8106111526277767e-05, 'epoch': 2.29}

 76%|███████▋  | 952/1248 [1:35:12<28:54,  5.86s/it]
 76%|███████▋  | 953/1248 [1:35:18<28:48,  5.86s/it]
                                                    
{'loss': 0.5025, 'learning_rate': 2.7925888170101665e-05, 'epoch': 2.29}

 76%|███████▋  | 953/1248 [1:35:18<28:48,  5.86s/it]
 76%|███████▋  | 954/1248 [1:35:24<28:54,  5.90s/it]
                                                    
{'loss': 0.4958, 'learning_rate': 2.7746150670473458e-05, 'epoch': 2.29}

 76%|███████▋  | 954/1248 [1:35:24<28:54,  5.90s/it]
 77%|███████▋  | 955/1248 [1:35:29<28:23,  5.81s/it]
                                                    
{'loss': 0.4777, 'learning_rate': 2.756690023901596e-05, 'epoch': 2.3}

 77%|███████▋  | 955/1248 [1:35:29<28:23,  5.81s/it]
 77%|███████▋  | 956/1248 [1:35:35<28:25,  5.84s/it]
                                                    
{'loss': 0.5256, 'learning_rate': 2.738813808406866e-05, 'epoch': 2.3}

 77%|███████▋  | 956/1248 [1:35:35<28:25,  5.84s/it]
 77%|███████▋  | 957/1248 [1:35:41<28:21,  5.85s/it]
                                                    
{'loss': 0.4755, 'learning_rate': 2.7209865410679536e-05, 'epoch': 2.3}

 77%|███████▋  | 957/1248 [1:35:41<28:21,  5.85s/it]
 77%|███████▋  | 958/1248 [1:35:47<28:18,  5.86s/it]
                                                    
{'loss': 0.4982, 'learning_rate': 2.7032083420597e-05, 'epoch': 2.3}

 77%|███████▋  | 958/1248 [1:35:47<28:18,  5.86s/it]
 77%|███████▋  | 959/1248 [1:35:53<28:40,  5.95s/it]
                                                    
{'loss': 0.4751, 'learning_rate': 2.685479331226164e-05, 'epoch': 2.31}

 77%|███████▋  | 959/1248 [1:35:53<28:40,  5.95s/it]
 77%|███████▋  | 960/1248 [1:35:59<28:47,  6.00s/it]
                                                    
{'loss': 0.4993, 'learning_rate': 2.667799628079829e-05, 'epoch': 2.31}

 77%|███████▋  | 960/1248 [1:35:59<28:47,  6.00s/it]
 77%|███████▋  | 961/1248 [1:36:05<28:27,  5.95s/it]
                                                    
{'loss': 0.596, 'learning_rate': 2.6501693518007896e-05, 'epoch': 2.31}

 77%|███████▋  | 961/1248 [1:36:05<28:27,  5.95s/it]
 77%|███████▋  | 962/1248 [1:36:11<28:07,  5.90s/it]
                                                    
{'loss': 0.4851, 'learning_rate': 2.6325886212359498e-05, 'epoch': 2.31}

 77%|███████▋  | 962/1248 [1:36:11<28:07,  5.90s/it]
 77%|███████▋  | 963/1248 [1:36:17<27:59,  5.89s/it]
                                                    
{'loss': 0.477, 'learning_rate': 2.6150575548982292e-05, 'epoch': 2.31}

 77%|███████▋  | 963/1248 [1:36:17<27:59,  5.89s/it]
 77%|███████▋  | 964/1248 [1:36:23<27:48,  5.87s/it]
                                                    
{'loss': 0.4548, 'learning_rate': 2.5975762709657504e-05, 'epoch': 2.32}

 77%|███████▋  | 964/1248 [1:36:23<27:48,  5.87s/it]
 77%|███████▋  | 965/1248 [1:36:29<28:12,  5.98s/it]
                                                    
{'loss': 0.5181, 'learning_rate': 2.580144887281051e-05, 'epoch': 2.32}

 77%|███████▋  | 965/1248 [1:36:29<28:12,  5.98s/it]
 77%|███████▋  | 966/1248 [1:36:35<28:10,  6.00s/it]
                                                    
{'loss': 0.5043, 'learning_rate': 2.562763521350283e-05, 'epoch': 2.32}

 77%|███████▋  | 966/1248 [1:36:35<28:10,  6.00s/it]
 77%|███████▋  | 967/1248 [1:36:40<27:29,  5.87s/it]
                                                    
{'loss': 0.4955, 'learning_rate': 2.5454322903424398e-05, 'epoch': 2.32}

 77%|███████▋  | 967/1248 [1:36:41<27:29,  5.87s/it]
 78%|███████▊  | 968/1248 [1:36:46<27:26,  5.88s/it]
                                                    
{'loss': 0.4692, 'learning_rate': 2.528151311088537e-05, 'epoch': 2.33}

 78%|███████▊  | 968/1248 [1:36:46<27:26,  5.88s/it]
 78%|███████▊  | 969/1248 [1:36:52<27:20,  5.88s/it]
                                                    
{'loss': 0.4974, 'learning_rate': 2.5109207000808455e-05, 'epoch': 2.33}

 78%|███████▊  | 969/1248 [1:36:52<27:20,  5.88s/it]
 78%|███████▊  | 970/1248 [1:36:59<28:26,  6.14s/it]
                                                    
{'loss': 0.5115, 'learning_rate': 2.4937405734720966e-05, 'epoch': 2.33}

 78%|███████▊  | 970/1248 [1:36:59<28:26,  6.14s/it]
 78%|███████▊  | 971/1248 [1:37:05<27:56,  6.05s/it]
                                                    
{'loss': 0.522, 'learning_rate': 2.476611047074713e-05, 'epoch': 2.33}

 78%|███████▊  | 971/1248 [1:37:05<27:56,  6.05s/it]
 78%|███████▊  | 972/1248 [1:37:10<27:13,  5.92s/it]
                                                    
{'loss': 0.4962, 'learning_rate': 2.4595322363600072e-05, 'epoch': 2.34}

 78%|███████▊  | 972/1248 [1:37:10<27:13,  5.92s/it]
 78%|███████▊  | 973/1248 [1:37:17<27:28,  5.99s/it]
                                                    
{'loss': 0.4948, 'learning_rate': 2.4425042564574184e-05, 'epoch': 2.34}

 78%|███████▊  | 973/1248 [1:37:17<27:28,  5.99s/it]
 78%|███████▊  | 974/1248 [1:37:22<26:59,  5.91s/it]
                                                    
{'loss': 0.505, 'learning_rate': 2.4255272221537295e-05, 'epoch': 2.34}

 78%|███████▊  | 974/1248 [1:37:22<26:59,  5.91s/it]
 78%|███████▊  | 975/1248 [1:37:28<27:00,  5.93s/it]
                                                    
{'loss': 0.4491, 'learning_rate': 2.4086012478922958e-05, 'epoch': 2.34}

 78%|███████▊  | 975/1248 [1:37:28<27:00,  5.93s/it]
 78%|███████▊  | 976/1248 [1:37:34<26:24,  5.82s/it]
                                                    
{'loss': 0.5062, 'learning_rate': 2.3917264477722788e-05, 'epoch': 2.35}

 78%|███████▊  | 976/1248 [1:37:34<26:24,  5.82s/it]
 78%|███████▊  | 977/1248 [1:37:40<26:41,  5.91s/it]
                                                    
{'loss': 0.5069, 'learning_rate': 2.374902935547866e-05, 'epoch': 2.35}

 78%|███████▊  | 977/1248 [1:37:40<26:41,  5.91s/it]
 78%|███████▊  | 978/1248 [1:37:46<26:04,  5.80s/it]
                                                    
{'loss': 0.5049, 'learning_rate': 2.3581308246275103e-05, 'epoch': 2.35}

 78%|███████▊  | 978/1248 [1:37:46<26:04,  5.80s/it]
 78%|███████▊  | 979/1248 [1:37:51<26:09,  5.84s/it]
                                                    
{'loss': 0.5099, 'learning_rate': 2.341410228073163e-05, 'epoch': 2.35}

 78%|███████▊  | 979/1248 [1:37:52<26:09,  5.84s/it]
 79%|███████▊  | 980/1248 [1:37:58<26:23,  5.91s/it]
                                                    
{'loss': 0.4952, 'learning_rate': 2.324741258599521e-05, 'epoch': 2.36}

 79%|███████▊  | 980/1248 [1:37:58<26:23,  5.91s/it]
 79%|███████▊  | 981/1248 [1:38:04<26:26,  5.94s/it]
                                                    
{'loss': 0.5383, 'learning_rate': 2.308124028573253e-05, 'epoch': 2.36}

 79%|███████▊  | 981/1248 [1:38:04<26:26,  5.94s/it]
 79%|███████▊  | 982/1248 [1:38:10<26:34,  5.99s/it]
                                                    
{'loss': 0.5148, 'learning_rate': 2.29155865001225e-05, 'epoch': 2.36}

 79%|███████▊  | 982/1248 [1:38:10<26:34,  5.99s/it]
 79%|███████▉  | 983/1248 [1:38:15<26:07,  5.92s/it]
                                                    
{'loss': 0.4764, 'learning_rate': 2.2750452345848682e-05, 'epoch': 2.36}

 79%|███████▉  | 983/1248 [1:38:15<26:07,  5.92s/it]
 79%|███████▉  | 984/1248 [1:38:21<25:44,  5.85s/it]
                                                    
{'loss': 0.4848, 'learning_rate': 2.2585838936091754e-05, 'epoch': 2.37}

 79%|███████▉  | 984/1248 [1:38:21<25:44,  5.85s/it]
 79%|███████▉  | 985/1248 [1:38:27<26:00,  5.94s/it]
                                                    
{'loss': 0.5027, 'learning_rate': 2.2421747380522095e-05, 'epoch': 2.37}

 79%|███████▉  | 985/1248 [1:38:27<26:00,  5.94s/it]
 79%|███████▉  | 986/1248 [1:38:33<26:16,  6.02s/it]
                                                    
{'loss': 0.5082, 'learning_rate': 2.225817878529214e-05, 'epoch': 2.37}

 79%|███████▉  | 986/1248 [1:38:33<26:16,  6.02s/it]
 79%|███████▉  | 987/1248 [1:38:39<26:01,  5.98s/it]
                                                    
{'loss': 0.5086, 'learning_rate': 2.2095134253029037e-05, 'epoch': 2.37}

 79%|███████▉  | 987/1248 [1:38:39<26:01,  5.98s/it]
 79%|███████▉  | 988/1248 [1:38:45<25:49,  5.96s/it]
                                                    
{'loss': 0.523, 'learning_rate': 2.1932614882827197e-05, 'epoch': 2.38}

 79%|███████▉  | 988/1248 [1:38:45<25:49,  5.96s/it]
 79%|███████▉  | 989/1248 [1:38:51<25:51,  5.99s/it]
                                                    
{'loss': 0.5186, 'learning_rate': 2.1770621770240905e-05, 'epoch': 2.38}

 79%|███████▉  | 989/1248 [1:38:51<25:51,  5.99s/it]
 79%|███████▉  | 990/1248 [1:38:57<25:26,  5.92s/it]
                                                    
{'loss': 0.5175, 'learning_rate': 2.1609156007276876e-05, 'epoch': 2.38}

 79%|███████▉  | 990/1248 [1:38:57<25:26,  5.92s/it]
 79%|███████▉  | 991/1248 [1:39:03<25:53,  6.05s/it]
                                                    
{'loss': 0.5367, 'learning_rate': 2.1448218682386922e-05, 'epoch': 2.38}

 79%|███████▉  | 991/1248 [1:39:03<25:53,  6.05s/it]
 79%|███████▉  | 992/1248 [1:39:09<25:28,  5.97s/it]
                                                    
{'loss': 0.4746, 'learning_rate': 2.1287810880460635e-05, 'epoch': 2.38}

 79%|███████▉  | 992/1248 [1:39:09<25:28,  5.97s/it]
 80%|███████▉  | 993/1248 [1:39:15<25:35,  6.02s/it]
                                                    
{'loss': 0.4885, 'learning_rate': 2.112793368281799e-05, 'epoch': 2.39}

 80%|███████▉  | 993/1248 [1:39:15<25:35,  6.02s/it]
 80%|███████▉  | 994/1248 [1:39:21<25:19,  5.98s/it]
                                                    
{'loss': 0.502, 'learning_rate': 2.0968588167202262e-05, 'epoch': 2.39}

 80%|███████▉  | 994/1248 [1:39:21<25:19,  5.98s/it]
 80%|███████▉  | 995/1248 [1:39:27<25:10,  5.97s/it]
                                                    
{'loss': 0.4628, 'learning_rate': 2.0809775407772503e-05, 'epoch': 2.39}

 80%|███████▉  | 995/1248 [1:39:27<25:10,  5.97s/it]
 80%|███████▉  | 996/1248 [1:39:33<24:59,  5.95s/it]
                                                    
{'loss': 0.4709, 'learning_rate': 2.0651496475096453e-05, 'epoch': 2.39}

 80%|███████▉  | 996/1248 [1:39:33<24:59,  5.95s/it]
 80%|███████▉  | 997/1248 [1:39:39<24:50,  5.94s/it]
                                                    
{'loss': 0.4771, 'learning_rate': 2.0493752436143264e-05, 'epoch': 2.4}

 80%|███████▉  | 997/1248 [1:39:39<24:50,  5.94s/it]
 80%|███████▉  | 998/1248 [1:39:45<24:36,  5.90s/it]
                                                    
{'loss': 0.493, 'learning_rate': 2.03365443542764e-05, 'epoch': 2.4}

 80%|███████▉  | 998/1248 [1:39:45<24:36,  5.90s/it]
 80%|████████  | 999/1248 [1:39:51<24:32,  5.91s/it]
                                                    
{'loss': 0.5219, 'learning_rate': 2.0179873289246355e-05, 'epoch': 2.4}

 80%|████████  | 999/1248 [1:39:51<24:32,  5.91s/it]
 80%|████████  | 1000/1248 [1:39:57<24:51,  6.01s/it]
                                                     
{'loss': 0.507, 'learning_rate': 2.0023740297183534e-05, 'epoch': 2.4}

 80%|████████  | 1000/1248 [1:39:57<24:51,  6.01s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 80%|████████  | 1001/1248 [1:40:23<49:14, 11.96s/it]
                                                     
{'loss': 0.4902, 'learning_rate': 1.9868146430591194e-05, 'epoch': 2.41}

 80%|████████  | 1001/1248 [1:40:23<49:14, 11.96s/it]
 80%|████████  | 1002/1248 [1:40:28<40:33,  9.89s/it]
                                                     
{'loss': 0.5074, 'learning_rate': 1.971309273833828e-05, 'epoch': 2.41}

 80%|████████  | 1002/1248 [1:40:28<40:33,  9.89s/it]
 80%|████████  | 1003/1248 [1:40:33<35:03,  8.58s/it]
                                                     
{'loss': 0.4746, 'learning_rate': 1.9558580265652448e-05, 'epoch': 2.41}

 80%|████████  | 1003/1248 [1:40:33<35:03,  8.58s/it]
 80%|████████  | 1004/1248 [1:40:39<31:40,  7.79s/it]
                                                     
{'loss': 0.522, 'learning_rate': 1.940461005411288e-05, 'epoch': 2.41}

 80%|████████  | 1004/1248 [1:40:39<31:40,  7.79s/it]
 81%|████████  | 1005/1248 [1:40:45<29:25,  7.27s/it]
                                                     
{'loss': 0.4993, 'learning_rate': 1.925118314164337e-05, 'epoch': 2.42}

 81%|████████  | 1005/1248 [1:40:45<29:25,  7.27s/it]
 81%|████████  | 1006/1248 [1:40:51<27:39,  6.86s/it]
                                                     
{'loss': 0.5047, 'learning_rate': 1.9098300562505266e-05, 'epoch': 2.42}

 81%|████████  | 1006/1248 [1:40:51<27:39,  6.86s/it]
 81%|████████  | 1007/1248 [1:40:57<26:05,  6.49s/it]
                                                     
{'loss': 0.5472, 'learning_rate': 1.8945963347290607e-05, 'epoch': 2.42}

 81%|████████  | 1007/1248 [1:40:57<26:05,  6.49s/it]
 81%|████████  | 1008/1248 [1:41:03<25:00,  6.25s/it]
                                                     
{'loss': 0.5008, 'learning_rate': 1.879417252291502e-05, 'epoch': 2.42}

 81%|████████  | 1008/1248 [1:41:03<25:00,  6.25s/it]
 81%|████████  | 1009/1248 [1:41:08<23:32,  5.91s/it]
                                                     
{'loss': 0.5022, 'learning_rate': 1.8642929112610875e-05, 'epoch': 2.43}

 81%|████████  | 1009/1248 [1:41:08<23:32,  5.91s/it]
 81%|████████  | 1010/1248 [1:41:14<23:22,  5.89s/it]
                                                     
{'loss': 0.4677, 'learning_rate': 1.8492234135920462e-05, 'epoch': 2.43}

 81%|████████  | 1010/1248 [1:41:14<23:22,  5.89s/it]
 81%|████████  | 1011/1248 [1:41:20<23:39,  5.99s/it]
                                                     
{'loss': 0.5649, 'learning_rate': 1.8342088608688944e-05, 'epoch': 2.43}

 81%|████████  | 1011/1248 [1:41:20<23:39,  5.99s/it]
 81%|████████  | 1012/1248 [1:41:26<23:33,  5.99s/it]
                                                     
{'loss': 0.4687, 'learning_rate': 1.8192493543057674e-05, 'epoch': 2.43}

 81%|████████  | 1012/1248 [1:41:26<23:33,  5.99s/it]
 81%|████████  | 1013/1248 [1:41:32<23:10,  5.92s/it]
                                                     
{'loss': 0.5245, 'learning_rate': 1.804344994745727e-05, 'epoch': 2.44}

 81%|████████  | 1013/1248 [1:41:32<23:10,  5.92s/it]
 81%|████████▏ | 1014/1248 [1:41:37<22:54,  5.87s/it]
                                                     
{'loss': 0.4817, 'learning_rate': 1.7894958826600882e-05, 'epoch': 2.44}

 81%|████████▏ | 1014/1248 [1:41:37<22:54,  5.87s/it]
 81%|████████▏ | 1015/1248 [1:41:43<22:43,  5.85s/it]
                                                     
{'loss': 0.4652, 'learning_rate': 1.7747021181477374e-05, 'epoch': 2.44}

 81%|████████▏ | 1015/1248 [1:41:43<22:43,  5.85s/it]
 81%|████████▏ | 1016/1248 [1:41:49<22:49,  5.90s/it]
                                                     
{'loss': 0.5258, 'learning_rate': 1.7599638009344566e-05, 'epoch': 2.44}

 81%|████████▏ | 1016/1248 [1:41:49<22:49,  5.90s/it]
 81%|████████▏ | 1017/1248 [1:41:55<23:01,  5.98s/it]
                                                     
{'loss': 0.5618, 'learning_rate': 1.74528103037226e-05, 'epoch': 2.44}

 81%|████████▏ | 1017/1248 [1:41:55<23:01,  5.98s/it]
 82%|████████▏ | 1018/1248 [1:42:01<22:14,  5.80s/it]
                                                     
{'loss': 0.512, 'learning_rate': 1.730653905438714e-05, 'epoch': 2.45}

 82%|████████▏ | 1018/1248 [1:42:01<22:14,  5.80s/it]
 82%|████████▏ | 1019/1248 [1:42:07<22:11,  5.81s/it]
                                                     
{'loss': 0.5165, 'learning_rate': 1.7160825247362723e-05, 'epoch': 2.45}

 82%|████████▏ | 1019/1248 [1:42:07<22:11,  5.81s/it]
 82%|████████▏ | 1020/1248 [1:42:13<22:31,  5.93s/it]
                                                     
{'loss': 0.5052, 'learning_rate': 1.701566986491614e-05, 'epoch': 2.45}

 82%|████████▏ | 1020/1248 [1:42:13<22:31,  5.93s/it]
 82%|████████▏ | 1021/1248 [1:42:19<22:22,  5.92s/it]
                                                     
{'loss': 0.5324, 'learning_rate': 1.6871073885549847e-05, 'epoch': 2.45}

 82%|████████▏ | 1021/1248 [1:42:19<22:22,  5.92s/it]
 82%|████████▏ | 1022/1248 [1:42:25<22:13,  5.90s/it]
                                                     
{'loss': 0.4823, 'learning_rate': 1.672703828399529e-05, 'epoch': 2.46}

 82%|████████▏ | 1022/1248 [1:42:25<22:13,  5.90s/it]
 82%|████████▏ | 1023/1248 [1:42:30<22:09,  5.91s/it]
                                                     
{'loss': 0.5252, 'learning_rate': 1.6583564031206357e-05, 'epoch': 2.46}

 82%|████████▏ | 1023/1248 [1:42:30<22:09,  5.91s/it]
 82%|████████▏ | 1024/1248 [1:42:36<21:56,  5.88s/it]
                                                     
{'loss': 0.5177, 'learning_rate': 1.644065209435284e-05, 'epoch': 2.46}

 82%|████████▏ | 1024/1248 [1:42:36<21:56,  5.88s/it]
 82%|████████▏ | 1025/1248 [1:42:42<21:49,  5.87s/it]
                                                     
{'loss': 0.5438, 'learning_rate': 1.6298303436813977e-05, 'epoch': 2.46}

 82%|████████▏ | 1025/1248 [1:42:42<21:49,  5.87s/it]
 82%|████████▏ | 1026/1248 [1:42:48<22:06,  5.98s/it]
                                                     
{'loss': 0.5111, 'learning_rate': 1.6156519018171857e-05, 'epoch': 2.47}

 82%|████████▏ | 1026/1248 [1:42:48<22:06,  5.98s/it]
 82%|████████▏ | 1027/1248 [1:42:54<21:38,  5.88s/it]
                                                     
{'loss': 0.4801, 'learning_rate': 1.6015299794204996e-05, 'epoch': 2.47}

 82%|████████▏ | 1027/1248 [1:42:54<21:38,  5.88s/it]
 82%|████████▏ | 1028/1248 [1:43:00<21:33,  5.88s/it]
                                                     
{'loss': 0.5361, 'learning_rate': 1.587464671688187e-05, 'epoch': 2.47}

 82%|████████▏ | 1028/1248 [1:43:00<21:33,  5.88s/it]
 82%|████████▏ | 1029/1248 [1:43:06<21:11,  5.81s/it]
                                                     
{'loss': 0.5052, 'learning_rate': 1.5734560734354618e-05, 'epoch': 2.47}

 82%|████████▏ | 1029/1248 [1:43:06<21:11,  5.81s/it]
 83%|████████▎ | 1030/1248 [1:43:11<21:14,  5.85s/it]
                                                     
{'loss': 0.4966, 'learning_rate': 1.559504279095244e-05, 'epoch': 2.48}

 83%|████████▎ | 1030/1248 [1:43:11<21:14,  5.85s/it]
 83%|████████▎ | 1031/1248 [1:43:17<21:03,  5.82s/it]
                                                     
{'loss': 0.4775, 'learning_rate': 1.5456093827175422e-05, 'epoch': 2.48}

 83%|████████▎ | 1031/1248 [1:43:17<21:03,  5.82s/it]
 83%|████████▎ | 1032/1248 [1:43:23<21:11,  5.89s/it]
                                                     
{'loss': 0.5051, 'learning_rate': 1.5317714779688074e-05, 'epoch': 2.48}

 83%|████████▎ | 1032/1248 [1:43:23<21:11,  5.89s/it]
 83%|████████▎ | 1033/1248 [1:43:29<21:01,  5.87s/it]
                                                     
{'loss': 0.5053, 'learning_rate': 1.5179906581313064e-05, 'epoch': 2.48}

 83%|████████▎ | 1033/1248 [1:43:29<21:01,  5.87s/it]
 83%|████████▎ | 1034/1248 [1:43:35<21:01,  5.90s/it]
                                                     
{'loss': 0.5024, 'learning_rate': 1.5042670161024974e-05, 'epoch': 2.49}

 83%|████████▎ | 1034/1248 [1:43:35<21:01,  5.90s/it]
 83%|████████▎ | 1035/1248 [1:43:42<21:34,  6.08s/it]
                                                     
{'loss': 0.5155, 'learning_rate': 1.4906006443943943e-05, 'epoch': 2.49}

 83%|████████▎ | 1035/1248 [1:43:42<21:34,  6.08s/it]
 83%|████████▎ | 1036/1248 [1:43:48<21:21,  6.04s/it]
                                                     
{'loss': 0.5087, 'learning_rate': 1.4769916351329493e-05, 'epoch': 2.49}

 83%|████████▎ | 1036/1248 [1:43:48<21:21,  6.04s/it]
 83%|████████▎ | 1037/1248 [1:43:53<20:53,  5.94s/it]
                                                     
{'loss': 0.4933, 'learning_rate': 1.4634400800574278e-05, 'epoch': 2.49}

 83%|████████▎ | 1037/1248 [1:43:53<20:53,  5.94s/it]
 83%|████████▎ | 1038/1248 [1:43:59<20:35,  5.88s/it]
                                                     
{'loss': 0.5178, 'learning_rate': 1.4499460705197998e-05, 'epoch': 2.5}

 83%|████████▎ | 1038/1248 [1:43:59<20:35,  5.88s/it]
 83%|████████▎ | 1039/1248 [1:44:04<20:05,  5.77s/it]
                                                     
{'loss': 0.5424, 'learning_rate': 1.4365096974841108e-05, 'epoch': 2.5}

 83%|████████▎ | 1039/1248 [1:44:04<20:05,  5.77s/it]
 83%|████████▎ | 1040/1248 [1:44:10<20:00,  5.77s/it]
                                                     
{'loss': 0.4899, 'learning_rate': 1.4231310515258744e-05, 'epoch': 2.5}

 83%|████████▎ | 1040/1248 [1:44:10<20:00,  5.77s/it]
 83%|████████▎ | 1041/1248 [1:44:16<20:06,  5.83s/it]
                                                     
{'loss': 0.4934, 'learning_rate': 1.4098102228314658e-05, 'epoch': 2.5}

 83%|████████▎ | 1041/1248 [1:44:16<20:06,  5.83s/it]
 83%|████████▎ | 1042/1248 [1:44:22<19:38,  5.72s/it]
                                                     
{'loss': 0.5035, 'learning_rate': 1.3965473011975038e-05, 'epoch': 2.5}

 83%|████████▎ | 1042/1248 [1:44:22<19:38,  5.72s/it]
 84%|████████▎ | 1043/1248 [1:44:27<19:19,  5.66s/it]
                                                     
{'loss': 0.4784, 'learning_rate': 1.3833423760302611e-05, 'epoch': 2.51}

 84%|████████▎ | 1043/1248 [1:44:27<19:19,  5.66s/it]
 84%|████████▎ | 1044/1248 [1:44:32<18:54,  5.56s/it]
                                                     
{'loss': 0.5001, 'learning_rate': 1.3701955363450447e-05, 'epoch': 2.51}

 84%|████████▎ | 1044/1248 [1:44:33<18:54,  5.56s/it]
 84%|████████▎ | 1045/1248 [1:44:39<19:23,  5.73s/it]
                                                     
{'loss': 0.4956, 'learning_rate': 1.3571068707656065e-05, 'epoch': 2.51}

 84%|████████▎ | 1045/1248 [1:44:39<19:23,  5.73s/it]
 84%|████████▍ | 1046/1248 [1:44:44<19:20,  5.75s/it]
                                                     
{'loss': 0.4566, 'learning_rate': 1.3440764675235384e-05, 'epoch': 2.51}

 84%|████████▍ | 1046/1248 [1:44:44<19:20,  5.75s/it]
 84%|████████▍ | 1047/1248 [1:44:50<19:16,  5.76s/it]
                                                     
{'loss': 0.5641, 'learning_rate': 1.33110441445769e-05, 'epoch': 2.52}

 84%|████████▍ | 1047/1248 [1:44:50<19:16,  5.76s/it]
 84%|████████▍ | 1048/1248 [1:44:56<18:47,  5.64s/it]
                                                     
{'loss': 0.5072, 'learning_rate': 1.3181907990135622e-05, 'epoch': 2.52}

 84%|████████▍ | 1048/1248 [1:44:56<18:47,  5.64s/it]
 84%|████████▍ | 1049/1248 [1:45:01<18:25,  5.56s/it]
                                                     
{'loss': 0.492, 'learning_rate': 1.3053357082427253e-05, 'epoch': 2.52}

 84%|████████▍ | 1049/1248 [1:45:01<18:25,  5.56s/it]
 84%|████████▍ | 1050/1248 [1:45:06<17:59,  5.45s/it]
                                                     
{'loss': 0.5022, 'learning_rate': 1.2925392288022298e-05, 'epoch': 2.52}

 84%|████████▍ | 1050/1248 [1:45:06<17:59,  5.45s/it]
 84%|████████▍ | 1051/1248 [1:45:11<17:49,  5.43s/it]
                                                     
{'loss': 0.478, 'learning_rate': 1.279801446954023e-05, 'epoch': 2.53}

 84%|████████▍ | 1051/1248 [1:45:12<17:49,  5.43s/it]
 84%|████████▍ | 1052/1248 [1:45:17<17:54,  5.48s/it]
                                                     
{'loss': 0.4974, 'learning_rate': 1.267122448564374e-05, 'epoch': 2.53}

 84%|████████▍ | 1052/1248 [1:45:17<17:54,  5.48s/it]
 84%|████████▍ | 1053/1248 [1:45:23<18:10,  5.59s/it]
                                                     
{'loss': 0.4973, 'learning_rate': 1.2545023191032801e-05, 'epoch': 2.53}

 84%|████████▍ | 1053/1248 [1:45:23<18:10,  5.59s/it]
 84%|████████▍ | 1054/1248 [1:45:29<18:09,  5.62s/it]
                                                     
{'loss': 0.481, 'learning_rate': 1.2419411436439022e-05, 'epoch': 2.53}

 84%|████████▍ | 1054/1248 [1:45:29<18:09,  5.62s/it]
 85%|████████▍ | 1055/1248 [1:45:34<18:08,  5.64s/it]
                                                     
{'loss': 0.6139, 'learning_rate': 1.2294390068619877e-05, 'epoch': 2.54}

 85%|████████▍ | 1055/1248 [1:45:34<18:08,  5.64s/it]
 85%|████████▍ | 1056/1248 [1:45:40<18:05,  5.65s/it]
                                                     
{'loss': 0.4896, 'learning_rate': 1.2169959930353047e-05, 'epoch': 2.54}

 85%|████████▍ | 1056/1248 [1:45:40<18:05,  5.65s/it]
 85%|████████▍ | 1057/1248 [1:45:46<18:22,  5.77s/it]
                                                     
{'loss': 0.4906, 'learning_rate': 1.2046121860430637e-05, 'epoch': 2.54}

 85%|████████▍ | 1057/1248 [1:45:46<18:22,  5.77s/it]
 85%|████████▍ | 1058/1248 [1:45:52<18:12,  5.75s/it]
                                                     
{'loss': 0.5379, 'learning_rate': 1.1922876693653585e-05, 'epoch': 2.54}

 85%|████████▍ | 1058/1248 [1:45:52<18:12,  5.75s/it]
 85%|████████▍ | 1059/1248 [1:45:57<17:54,  5.69s/it]
                                                     
{'loss': 0.4611, 'learning_rate': 1.1800225260826037e-05, 'epoch': 2.55}

 85%|████████▍ | 1059/1248 [1:45:57<17:54,  5.69s/it]
 85%|████████▍ | 1060/1248 [1:46:03<17:36,  5.62s/it]
                                                     
{'loss': 0.476, 'learning_rate': 1.1678168388749788e-05, 'epoch': 2.55}

 85%|████████▍ | 1060/1248 [1:46:03<17:36,  5.62s/it]
 85%|████████▌ | 1061/1248 [1:46:08<17:37,  5.65s/it]
                                                     
{'loss': 0.5371, 'learning_rate': 1.1556706900218572e-05, 'epoch': 2.55}

 85%|████████▌ | 1061/1248 [1:46:08<17:37,  5.65s/it]
 85%|████████▌ | 1062/1248 [1:46:14<17:42,  5.71s/it]
                                                     
{'loss': 0.5284, 'learning_rate': 1.1435841614012666e-05, 'epoch': 2.55}

 85%|████████▌ | 1062/1248 [1:46:14<17:42,  5.71s/it]
 85%|████████▌ | 1063/1248 [1:46:20<17:45,  5.76s/it]
                                                     
{'loss': 0.4955, 'learning_rate': 1.131557334489326e-05, 'epoch': 2.56}

 85%|████████▌ | 1063/1248 [1:46:20<17:45,  5.76s/it]
 85%|████████▌ | 1064/1248 [1:46:26<17:26,  5.69s/it]
                                                     
{'loss': 0.5041, 'learning_rate': 1.1195902903597023e-05, 'epoch': 2.56}

 85%|████████▌ | 1064/1248 [1:46:26<17:26,  5.69s/it]
 85%|████████▌ | 1065/1248 [1:46:31<17:09,  5.62s/it]
                                                     
{'loss': 0.4744, 'learning_rate': 1.1076831096830676e-05, 'epoch': 2.56}

 85%|████████▌ | 1065/1248 [1:46:31<17:09,  5.62s/it]
 85%|████████▌ | 1066/1248 [1:46:37<17:05,  5.64s/it]
                                                     
{'loss': 0.4895, 'learning_rate': 1.0958358727265438e-05, 'epoch': 2.56}

 85%|████████▌ | 1066/1248 [1:46:37<17:05,  5.64s/it]
 85%|████████▌ | 1067/1248 [1:46:43<17:02,  5.65s/it]
                                                     
{'loss': 0.5091, 'learning_rate': 1.0840486593531706e-05, 'epoch': 2.56}

 85%|████████▌ | 1067/1248 [1:46:43<17:02,  5.65s/it]
 86%|████████▌ | 1068/1248 [1:46:48<17:12,  5.74s/it]
                                                     
{'loss': 0.4817, 'learning_rate': 1.0723215490213634e-05, 'epoch': 2.57}

 86%|████████▌ | 1068/1248 [1:46:48<17:12,  5.74s/it]
 86%|████████▌ | 1069/1248 [1:46:54<16:58,  5.69s/it]
                                                     
{'loss': 0.476, 'learning_rate': 1.0606546207843837e-05, 'epoch': 2.57}

 86%|████████▌ | 1069/1248 [1:46:54<16:58,  5.69s/it]
 86%|████████▌ | 1070/1248 [1:47:00<16:51,  5.68s/it]
                                                     
{'loss': 0.5003, 'learning_rate': 1.0490479532897946e-05, 'epoch': 2.57}

 86%|████████▌ | 1070/1248 [1:47:00<16:51,  5.68s/it]
 86%|████████▌ | 1071/1248 [1:47:05<16:48,  5.70s/it]
                                                     
{'loss': 0.5438, 'learning_rate': 1.0375016247789404e-05, 'epoch': 2.57}

 86%|████████▌ | 1071/1248 [1:47:05<16:48,  5.70s/it]
 86%|████████▌ | 1072/1248 [1:47:11<16:44,  5.71s/it]
                                                     
{'loss': 0.4734, 'learning_rate': 1.026015713086418e-05, 'epoch': 2.58}

 86%|████████▌ | 1072/1248 [1:47:11<16:44,  5.71s/it]
 86%|████████▌ | 1073/1248 [1:47:17<16:44,  5.74s/it]
                                                     
{'loss': 0.4936, 'learning_rate': 1.0145902956395447e-05, 'epoch': 2.58}

 86%|████████▌ | 1073/1248 [1:47:17<16:44,  5.74s/it]
 86%|████████▌ | 1074/1248 [1:47:23<16:36,  5.72s/it]
                                                     
{'loss': 0.4971, 'learning_rate': 1.003225449457852e-05, 'epoch': 2.58}

 86%|████████▌ | 1074/1248 [1:47:23<16:36,  5.72s/it]
 86%|████████▌ | 1075/1248 [1:47:28<16:25,  5.70s/it]
                                                     
{'loss': 0.4796, 'learning_rate': 9.919212511525456e-06, 'epoch': 2.58}

 86%|████████▌ | 1075/1248 [1:47:28<16:25,  5.70s/it]
 86%|████████▌ | 1076/1248 [1:47:34<16:20,  5.70s/it]
                                                     
{'loss': 0.5202, 'learning_rate': 9.806777769260033e-06, 'epoch': 2.59}

 86%|████████▌ | 1076/1248 [1:47:34<16:20,  5.70s/it]
 86%|████████▋ | 1077/1248 [1:47:40<16:18,  5.72s/it]
                                                     
{'loss': 0.4866, 'learning_rate': 9.69495102571263e-06, 'epoch': 2.59}

 86%|████████▋ | 1077/1248 [1:47:40<16:18,  5.72s/it]
 86%|████████▋ | 1078/1248 [1:47:46<16:22,  5.78s/it]
                                                     
{'loss': 0.4856, 'learning_rate': 9.583733034714981e-06, 'epoch': 2.59}

 86%|████████▋ | 1078/1248 [1:47:46<16:22,  5.78s/it]
 86%|████████▋ | 1079/1248 [1:47:52<16:37,  5.90s/it]
                                                     
{'loss': 0.5034, 'learning_rate': 9.473124545995249e-06, 'epoch': 2.59}

 86%|████████▋ | 1079/1248 [1:47:52<16:37,  5.90s/it]
 87%|████████▋ | 1080/1248 [1:47:58<16:30,  5.90s/it]
                                                     
{'loss': 0.4679, 'learning_rate': 9.363126305172831e-06, 'epoch': 2.6}

 87%|████████▋ | 1080/1248 [1:47:58<16:30,  5.90s/it]
 87%|████████▋ | 1081/1248 [1:48:04<16:15,  5.84s/it]
                                                     
{'loss': 0.5151, 'learning_rate': 9.253739053753474e-06, 'epoch': 2.6}

 87%|████████▋ | 1081/1248 [1:48:04<16:15,  5.84s/it]
 87%|████████▋ | 1082/1248 [1:48:10<16:24,  5.93s/it]
                                                     
{'loss': 0.4847, 'learning_rate': 9.144963529124162e-06, 'epoch': 2.6}

 87%|████████▋ | 1082/1248 [1:48:10<16:24,  5.93s/it]
 87%|████████▋ | 1083/1248 [1:48:15<16:14,  5.91s/it]
                                                     
{'loss': 0.5021, 'learning_rate': 9.036800464548157e-06, 'epoch': 2.6}

 87%|████████▋ | 1083/1248 [1:48:16<16:14,  5.91s/it]
 87%|████████▋ | 1084/1248 [1:48:22<16:54,  6.19s/it]
                                                     
{'loss': 0.5388, 'learning_rate': 8.929250589160166e-06, 'epoch': 2.61}

 87%|████████▋ | 1084/1248 [1:48:22<16:54,  6.19s/it]
 87%|████████▋ | 1085/1248 [1:48:28<16:34,  6.10s/it]
                                                     
{'loss': 0.4783, 'learning_rate': 8.822314627961293e-06, 'epoch': 2.61}

 87%|████████▋ | 1085/1248 [1:48:28<16:34,  6.10s/it]
 87%|████████▋ | 1086/1248 [1:48:34<16:13,  6.01s/it]
                                                     
{'loss': 0.498, 'learning_rate': 8.715993301814173e-06, 'epoch': 2.61}

 87%|████████▋ | 1086/1248 [1:48:34<16:13,  6.01s/it]
 87%|████████▋ | 1087/1248 [1:48:40<16:01,  5.97s/it]
                                                     
{'loss': 0.4922, 'learning_rate': 8.610287327438227e-06, 'epoch': 2.61}

 87%|████████▋ | 1087/1248 [1:48:40<16:01,  5.97s/it]
 87%|████████▋ | 1088/1248 [1:48:46<15:54,  5.97s/it]
                                                     
{'loss': 0.4737, 'learning_rate': 8.505197417404687e-06, 'epoch': 2.62}

 87%|████████▋ | 1088/1248 [1:48:46<15:54,  5.97s/it]
 87%|████████▋ | 1089/1248 [1:48:52<16:07,  6.08s/it]
                                                     
{'loss': 0.5591, 'learning_rate': 8.400724280131866e-06, 'epoch': 2.62}

 87%|████████▋ | 1089/1248 [1:48:52<16:07,  6.08s/it]
 87%|████████▋ | 1090/1248 [1:48:58<16:02,  6.09s/it]
                                                     
{'loss': 0.4679, 'learning_rate': 8.296868619880372e-06, 'epoch': 2.62}

 87%|████████▋ | 1090/1248 [1:48:58<16:02,  6.09s/it]
 87%|████████▋ | 1091/1248 [1:49:04<15:41,  6.00s/it]
                                                     
{'loss': 0.4975, 'learning_rate': 8.193631136748347e-06, 'epoch': 2.62}

 87%|████████▋ | 1091/1248 [1:49:04<15:41,  6.00s/it]
 88%|████████▊ | 1092/1248 [1:49:10<15:35,  6.00s/it]
                                                     
{'loss': 0.4809, 'learning_rate': 8.091012526666796e-06, 'epoch': 2.62}

 88%|████████▊ | 1092/1248 [1:49:10<15:35,  6.00s/it]
 88%|████████▊ | 1093/1248 [1:49:16<15:22,  5.95s/it]
                                                     
{'loss': 0.4877, 'learning_rate': 7.989013481394814e-06, 'epoch': 2.63}

 88%|████████▊ | 1093/1248 [1:49:16<15:22,  5.95s/it]
 88%|████████▊ | 1094/1248 [1:49:22<15:22,  5.99s/it]
                                                     
{'loss': 0.4645, 'learning_rate': 7.887634688515e-06, 'epoch': 2.63}

 88%|████████▊ | 1094/1248 [1:49:22<15:22,  5.99s/it]
 88%|████████▊ | 1095/1248 [1:49:27<14:43,  5.77s/it]
                                                     
{'loss': 0.5396, 'learning_rate': 7.786876831428736e-06, 'epoch': 2.63}

 88%|████████▊ | 1095/1248 [1:49:27<14:43,  5.77s/it]
 88%|████████▊ | 1096/1248 [1:49:33<14:40,  5.79s/it]
                                                     
{'loss': 0.4949, 'learning_rate': 7.686740589351704e-06, 'epoch': 2.63}

 88%|████████▊ | 1096/1248 [1:49:33<14:40,  5.79s/it]
 88%|████████▊ | 1097/1248 [1:49:39<14:34,  5.79s/it]
                                                     
{'loss': 0.4633, 'learning_rate': 7.5872266373092085e-06, 'epoch': 2.64}

 88%|████████▊ | 1097/1248 [1:49:39<14:34,  5.79s/it]
 88%|████████▊ | 1098/1248 [1:49:45<14:37,  5.85s/it]
                                                     
{'loss': 0.5377, 'learning_rate': 7.488335646131628e-06, 'epoch': 2.64}

 88%|████████▊ | 1098/1248 [1:49:45<14:37,  5.85s/it]
 88%|████████▊ | 1099/1248 [1:49:51<14:41,  5.91s/it]
                                                     
{'loss': 0.5019, 'learning_rate': 7.390068282449936e-06, 'epoch': 2.64}

 88%|████████▊ | 1099/1248 [1:49:51<14:41,  5.91s/it]
 88%|████████▊ | 1100/1248 [1:49:57<14:31,  5.89s/it]
                                                     
{'loss': 0.4966, 'learning_rate': 7.292425208691212e-06, 'epoch': 2.64}

 88%|████████▊ | 1100/1248 [1:49:57<14:31,  5.89s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 88%|████████▊ | 1101/1248 [1:50:23<29:31, 12.05s/it]
                                                     
{'loss': 0.5109, 'learning_rate': 7.195407083074135e-06, 'epoch': 2.65}

 88%|████████▊ | 1101/1248 [1:50:23<29:31, 12.05s/it]
 88%|████████▊ | 1102/1248 [1:50:29<24:36, 10.11s/it]
                                                     
{'loss': 0.5369, 'learning_rate': 7.0990145596045555e-06, 'epoch': 2.65}

 88%|████████▊ | 1102/1248 [1:50:29<24:36, 10.11s/it]
 88%|████████▊ | 1103/1248 [1:50:35<21:27,  8.88s/it]
                                                     
{'loss': 0.5185, 'learning_rate': 7.003248288071118e-06, 'epoch': 2.65}

 88%|████████▊ | 1103/1248 [1:50:35<21:27,  8.88s/it]
 88%|████████▊ | 1104/1248 [1:50:41<19:08,  7.98s/it]
                                                     
{'loss': 0.5248, 'learning_rate': 6.908108914040823e-06, 'epoch': 2.65}

 88%|████████▊ | 1104/1248 [1:50:41<19:08,  7.98s/it]
 89%|████████▊ | 1105/1248 [1:50:46<17:26,  7.32s/it]
                                                     
{'loss': 0.524, 'learning_rate': 6.813597078854772e-06, 'epoch': 2.66}

 89%|████████▊ | 1105/1248 [1:50:47<17:26,  7.32s/it]
 89%|████████▊ | 1106/1248 [1:50:53<16:24,  6.94s/it]
                                                     
{'loss': 0.4422, 'learning_rate': 6.7197134196237295e-06, 'epoch': 2.66}

 89%|████████▊ | 1106/1248 [1:50:53<16:24,  6.94s/it]
 89%|████████▊ | 1107/1248 [1:50:59<15:37,  6.65s/it]
                                                     
{'loss': 0.4757, 'learning_rate': 6.626458569223926e-06, 'epoch': 2.66}

 89%|████████▊ | 1107/1248 [1:50:59<15:37,  6.65s/it]
 89%|████████▉ | 1108/1248 [1:51:04<14:55,  6.40s/it]
                                                     
{'loss': 0.5045, 'learning_rate': 6.533833156292679e-06, 'epoch': 2.66}

 89%|████████▉ | 1108/1248 [1:51:04<14:55,  6.40s/it]
 89%|████████▉ | 1109/1248 [1:51:10<14:27,  6.24s/it]
                                                     
{'loss': 0.5092, 'learning_rate': 6.441837805224327e-06, 'epoch': 2.67}

 89%|████████▉ | 1109/1248 [1:51:10<14:27,  6.24s/it]
 89%|████████▉ | 1110/1248 [1:51:16<14:09,  6.16s/it]
                                                     
{'loss': 0.4645, 'learning_rate': 6.350473136165835e-06, 'epoch': 2.67}

 89%|████████▉ | 1110/1248 [1:51:16<14:09,  6.16s/it]
 89%|████████▉ | 1111/1248 [1:51:22<14:03,  6.15s/it]
                                                     
{'loss': 0.5219, 'learning_rate': 6.25973976501274e-06, 'epoch': 2.67}

 89%|████████▉ | 1111/1248 [1:51:22<14:03,  6.15s/it]
 89%|████████▉ | 1112/1248 [1:51:28<13:54,  6.13s/it]
                                                     
{'loss': 0.5058, 'learning_rate': 6.169638303404912e-06, 'epoch': 2.67}

 89%|████████▉ | 1112/1248 [1:51:28<13:54,  6.13s/it]
 89%|████████▉ | 1113/1248 [1:51:34<13:38,  6.06s/it]
                                                     
{'loss': 0.5238, 'learning_rate': 6.08016935872251e-06, 'epoch': 2.68}

 89%|████████▉ | 1113/1248 [1:51:34<13:38,  6.06s/it]
 89%|████████▉ | 1114/1248 [1:51:40<13:21,  5.98s/it]
                                                     
{'loss': 0.5011, 'learning_rate': 5.991333534081878e-06, 'epoch': 2.68}

 89%|████████▉ | 1114/1248 [1:51:40<13:21,  5.98s/it]
 89%|████████▉ | 1115/1248 [1:51:46<13:32,  6.11s/it]
                                                     
{'loss': 0.5257, 'learning_rate': 5.903131428331399e-06, 'epoch': 2.68}

 89%|████████▉ | 1115/1248 [1:51:46<13:32,  6.11s/it]
 89%|████████▉ | 1116/1248 [1:51:52<13:15,  6.03s/it]
                                                     
{'loss': 0.5376, 'learning_rate': 5.8155636360475385e-06, 'epoch': 2.68}

 89%|████████▉ | 1116/1248 [1:51:52<13:15,  6.03s/it]
 90%|████████▉ | 1117/1248 [1:51:58<13:01,  5.96s/it]
                                                     
{'loss': 0.5046, 'learning_rate': 5.728630747530805e-06, 'epoch': 2.69}

 90%|████████▉ | 1117/1248 [1:51:58<13:01,  5.96s/it]
 90%|████████▉ | 1118/1248 [1:52:04<12:54,  5.96s/it]
                                                     
{'loss': 0.481, 'learning_rate': 5.6423333488018095e-06, 'epoch': 2.69}

 90%|████████▉ | 1118/1248 [1:52:04<12:54,  5.96s/it]
 90%|████████▉ | 1119/1248 [1:52:10<12:43,  5.92s/it]
                                                     
{'loss': 0.4668, 'learning_rate': 5.556672021597232e-06, 'epoch': 2.69}

 90%|████████▉ | 1119/1248 [1:52:10<12:43,  5.92s/it]
 90%|████████▉ | 1120/1248 [1:52:16<12:41,  5.95s/it]
                                                     
{'loss': 0.4931, 'learning_rate': 5.471647343365982e-06, 'epoch': 2.69}

 90%|████████▉ | 1120/1248 [1:52:16<12:41,  5.95s/it]
 90%|████████▉ | 1121/1248 [1:52:22<12:37,  5.97s/it]
                                                     
{'loss': 0.5704, 'learning_rate': 5.387259887265261e-06, 'epoch': 2.69}

 90%|████████▉ | 1121/1248 [1:52:22<12:37,  5.97s/it]
 90%|████████▉ | 1122/1248 [1:52:28<12:35,  6.00s/it]
                                                     
{'loss': 0.474, 'learning_rate': 5.303510222156716e-06, 'epoch': 2.7}

 90%|████████▉ | 1122/1248 [1:52:28<12:35,  6.00s/it]
 90%|████████▉ | 1123/1248 [1:52:34<12:20,  5.92s/it]
                                                     
{'loss': 0.5272, 'learning_rate': 5.22039891260262e-06, 'epoch': 2.7}

 90%|████████▉ | 1123/1248 [1:52:34<12:20,  5.92s/it]
 90%|█████████ | 1124/1248 [1:52:40<12:16,  5.94s/it]
                                                     
{'loss': 0.5158, 'learning_rate': 5.137926518862013e-06, 'epoch': 2.7}

 90%|█████████ | 1124/1248 [1:52:40<12:16,  5.94s/it]
 90%|█████████ | 1125/1248 [1:52:45<11:41,  5.70s/it]
                                                     
{'loss': 0.5083, 'learning_rate': 5.056093596886991e-06, 'epoch': 2.7}

 90%|█████████ | 1125/1248 [1:52:45<11:41,  5.70s/it]
 90%|█████████ | 1126/1248 [1:52:50<11:28,  5.64s/it]
                                                     
{'loss': 0.5022, 'learning_rate': 4.974900698318885e-06, 'epoch': 2.71}

 90%|█████████ | 1126/1248 [1:52:50<11:28,  5.64s/it]
 90%|█████████ | 1127/1248 [1:52:56<11:25,  5.67s/it]
                                                     
{'loss': 0.4642, 'learning_rate': 4.8943483704846475e-06, 'epoch': 2.71}

 90%|█████████ | 1127/1248 [1:52:56<11:25,  5.67s/it]
 90%|█████████ | 1128/1248 [1:53:02<11:10,  5.59s/it]
                                                     
{'loss': 0.4981, 'learning_rate': 4.8144371563930476e-06, 'epoch': 2.71}

 90%|█████████ | 1128/1248 [1:53:02<11:10,  5.59s/it]
 90%|█████████ | 1129/1248 [1:53:07<11:03,  5.58s/it]
                                                     
{'loss': 0.476, 'learning_rate': 4.735167594731083e-06, 'epoch': 2.71}

 90%|█████████ | 1129/1248 [1:53:07<11:03,  5.58s/it]
 91%|█████████ | 1130/1248 [1:53:12<10:50,  5.51s/it]
                                                     
{'loss': 0.4895, 'learning_rate': 4.656540219860317e-06, 'epoch': 2.72}

 91%|█████████ | 1130/1248 [1:53:12<10:50,  5.51s/it]
 91%|█████████ | 1131/1248 [1:53:18<10:42,  5.49s/it]
                                                     
{'loss': 0.5247, 'learning_rate': 4.57855556181328e-06, 'epoch': 2.72}

 91%|█████████ | 1131/1248 [1:53:18<10:42,  5.49s/it]
 91%|█████████ | 1132/1248 [1:53:23<10:38,  5.50s/it]
                                                     
{'loss': 0.4451, 'learning_rate': 4.501214146289956e-06, 'epoch': 2.72}

 91%|█████████ | 1132/1248 [1:53:23<10:38,  5.50s/it]
 91%|█████████ | 1133/1248 [1:53:29<10:50,  5.66s/it]
                                                     
{'loss': 0.4542, 'learning_rate': 4.424516494654118e-06, 'epoch': 2.72}

 91%|█████████ | 1133/1248 [1:53:29<10:50,  5.66s/it]
 91%|█████████ | 1134/1248 [1:53:35<10:39,  5.61s/it]
                                                     
{'loss': 0.518, 'learning_rate': 4.3484631239299356e-06, 'epoch': 2.73}

 91%|█████████ | 1134/1248 [1:53:35<10:39,  5.61s/it]
 91%|█████████ | 1135/1248 [1:53:40<10:20,  5.49s/it]
                                                     
{'loss': 0.4875, 'learning_rate': 4.273054546798394e-06, 'epoch': 2.73}

 91%|█████████ | 1135/1248 [1:53:40<10:20,  5.49s/it]
 91%|█████████ | 1136/1248 [1:53:45<10:04,  5.40s/it]
                                                     
{'loss': 0.4778, 'learning_rate': 4.198291271593924e-06, 'epoch': 2.73}

 91%|█████████ | 1136/1248 [1:53:45<10:04,  5.40s/it]
 91%|█████████ | 1137/1248 [1:53:51<10:17,  5.56s/it]
                                                     
{'loss': 0.4766, 'learning_rate': 4.1241738023009016e-06, 'epoch': 2.73}

 91%|█████████ | 1137/1248 [1:53:51<10:17,  5.56s/it]
 91%|█████████ | 1138/1248 [1:53:57<10:06,  5.52s/it]
                                                     
{'loss': 0.4871, 'learning_rate': 4.050702638550275e-06, 'epoch': 2.74}

 91%|█████████ | 1138/1248 [1:53:57<10:06,  5.52s/it]
 91%|█████████▏| 1139/1248 [1:54:02<09:52,  5.44s/it]
                                                     
{'loss': 0.524, 'learning_rate': 3.97787827561622e-06, 'epoch': 2.74}

 91%|█████████▏| 1139/1248 [1:54:02<09:52,  5.44s/it]
 91%|█████████▏| 1140/1248 [1:54:08<09:56,  5.52s/it]
                                                     
{'loss': 0.4614, 'learning_rate': 3.9057012044127815e-06, 'epoch': 2.74}

 91%|█████████▏| 1140/1248 [1:54:08<09:56,  5.52s/it]
 91%|█████████▏| 1141/1248 [1:54:14<10:06,  5.67s/it]
                                                     
{'loss': 0.497, 'learning_rate': 3.834171911490569e-06, 'epoch': 2.74}

 91%|█████████▏| 1141/1248 [1:54:14<10:06,  5.67s/it]
 92%|█████████▏| 1142/1248 [1:54:19<09:58,  5.65s/it]
                                                     
{'loss': 0.474, 'learning_rate': 3.7632908790334655e-06, 'epoch': 2.75}

 92%|█████████▏| 1142/1248 [1:54:19<09:58,  5.65s/it]
 92%|█████████▏| 1143/1248 [1:54:25<10:06,  5.78s/it]
                                                     
{'loss': 0.4832, 'learning_rate': 3.693058584855369e-06, 'epoch': 2.75}

 92%|█████████▏| 1143/1248 [1:54:25<10:06,  5.78s/it]
 92%|█████████▏| 1144/1248 [1:54:31<10:02,  5.79s/it]
                                                     
{'loss': 0.5033, 'learning_rate': 3.6234755023970446e-06, 'epoch': 2.75}

 92%|█████████▏| 1144/1248 [1:54:31<10:02,  5.79s/it]
 92%|█████████▏| 1145/1248 [1:54:38<10:17,  5.99s/it]
                                                     
{'loss': 0.6024, 'learning_rate': 3.5545421007228243e-06, 'epoch': 2.75}

 92%|█████████▏| 1145/1248 [1:54:38<10:17,  5.99s/it]
 92%|█████████▏| 1146/1248 [1:54:44<10:23,  6.11s/it]
                                                     
{'loss': 0.655, 'learning_rate': 3.4862588445174984e-06, 'epoch': 2.75}

 92%|█████████▏| 1146/1248 [1:54:44<10:23,  6.11s/it]
 92%|█████████▏| 1147/1248 [1:54:50<10:12,  6.07s/it]
                                                     
{'loss': 0.4979, 'learning_rate': 3.4186261940832076e-06, 'epoch': 2.76}

 92%|█████████▏| 1147/1248 [1:54:50<10:12,  6.07s/it]
 92%|█████████▏| 1148/1248 [1:54:56<10:00,  6.01s/it]
                                                     
{'loss': 0.4942, 'learning_rate': 3.3516446053363015e-06, 'epoch': 2.76}

 92%|█████████▏| 1148/1248 [1:54:56<10:00,  6.01s/it]
 92%|█████████▏| 1149/1248 [1:55:02<10:03,  6.10s/it]
                                                     
{'loss': 0.4998, 'learning_rate': 3.2853145298042953e-06, 'epoch': 2.76}

 92%|█████████▏| 1149/1248 [1:55:02<10:03,  6.10s/it]
 92%|█████████▏| 1150/1248 [1:55:09<10:05,  6.18s/it]
                                                     
{'loss': 0.4919, 'learning_rate': 3.2196364146227507e-06, 'epoch': 2.76}

 92%|█████████▏| 1150/1248 [1:55:09<10:05,  6.18s/it]
 92%|█████████▏| 1151/1248 [1:55:14<09:53,  6.12s/it]
                                                     
{'loss': 0.5304, 'learning_rate': 3.154610702532412e-06, 'epoch': 2.77}

 92%|█████████▏| 1151/1248 [1:55:15<09:53,  6.12s/it]
 92%|█████████▏| 1152/1248 [1:55:20<09:32,  5.96s/it]
                                                     
{'loss': 0.6093, 'learning_rate': 3.090237831876053e-06, 'epoch': 2.77}

 92%|█████████▏| 1152/1248 [1:55:20<09:32,  5.96s/it]
 92%|█████████▏| 1153/1248 [1:55:26<09:28,  5.98s/it]
                                                     
{'loss': 0.5022, 'learning_rate': 3.026518236595621e-06, 'epoch': 2.77}

 92%|█████████▏| 1153/1248 [1:55:26<09:28,  5.98s/it]
 92%|█████████▏| 1154/1248 [1:55:32<09:32,  6.09s/it]
                                                     
{'loss': 0.5008, 'learning_rate': 2.9634523462293005e-06, 'epoch': 2.77}

 92%|█████████▏| 1154/1248 [1:55:32<09:32,  6.09s/it]
 93%|█████████▎| 1155/1248 [1:55:38<09:16,  5.99s/it]
                                                     
{'loss': 0.4821, 'learning_rate': 2.9010405859086097e-06, 'epoch': 2.78}

 93%|█████████▎| 1155/1248 [1:55:38<09:16,  5.99s/it]
 93%|█████████▎| 1156/1248 [1:55:44<09:07,  5.95s/it]
                                                     
{'loss': 0.4893, 'learning_rate': 2.839283376355506e-06, 'epoch': 2.78}

 93%|█████████▎| 1156/1248 [1:55:44<09:07,  5.95s/it]
 93%|█████████▎| 1157/1248 [1:55:50<08:57,  5.91s/it]
                                                     
{'loss': 0.4899, 'learning_rate': 2.778181133879576e-06, 'epoch': 2.78}

 93%|█████████▎| 1157/1248 [1:55:50<08:57,  5.91s/it]
 93%|█████████▎| 1158/1248 [1:55:56<09:06,  6.08s/it]
                                                     
{'loss': 0.5189, 'learning_rate': 2.717734270375272e-06, 'epoch': 2.78}

 93%|█████████▎| 1158/1248 [1:55:56<09:06,  6.08s/it]
 93%|█████████▎| 1159/1248 [1:56:03<09:04,  6.11s/it]
                                                     
{'loss': 0.4717, 'learning_rate': 2.657943193319035e-06, 'epoch': 2.79}

 93%|█████████▎| 1159/1248 [1:56:03<09:04,  6.11s/it]
 93%|█████████▎| 1160/1248 [1:56:08<08:41,  5.93s/it]
                                                     
{'loss': 0.4901, 'learning_rate': 2.5988083057666533e-06, 'epoch': 2.79}

 93%|█████████▎| 1160/1248 [1:56:08<08:41,  5.93s/it]
 93%|█████████▎| 1161/1248 [1:56:13<08:21,  5.76s/it]
                                                     
{'loss': 0.4924, 'learning_rate': 2.5403300063504555e-06, 'epoch': 2.79}

 93%|█████████▎| 1161/1248 [1:56:13<08:21,  5.76s/it]
 93%|█████████▎| 1162/1248 [1:56:19<08:07,  5.67s/it]
                                                     
{'loss': 0.5018, 'learning_rate': 2.482508689276675e-06, 'epoch': 2.79}

 93%|█████████▎| 1162/1248 [1:56:19<08:07,  5.67s/it]
 93%|█████████▎| 1163/1248 [1:56:25<08:01,  5.67s/it]
                                                     
{'loss': 0.4853, 'learning_rate': 2.4253447443228106e-06, 'epoch': 2.8}

 93%|█████████▎| 1163/1248 [1:56:25<08:01,  5.67s/it]
 93%|█████████▎| 1164/1248 [1:56:30<07:51,  5.62s/it]
                                                     
{'loss': 0.4936, 'learning_rate': 2.3688385568349515e-06, 'epoch': 2.8}

 93%|█████████▎| 1164/1248 [1:56:30<07:51,  5.62s/it]
 93%|█████████▎| 1165/1248 [1:56:36<07:42,  5.57s/it]
                                                     
{'loss': 0.4938, 'learning_rate': 2.3129905077251768e-06, 'epoch': 2.8}

 93%|█████████▎| 1165/1248 [1:56:36<07:42,  5.57s/it]
 93%|█████████▎| 1166/1248 [1:56:41<07:43,  5.65s/it]
                                                     
{'loss': 0.4883, 'learning_rate': 2.2578009734690265e-06, 'epoch': 2.8}

 93%|█████████▎| 1166/1248 [1:56:41<07:43,  5.65s/it]
 94%|█████████▎| 1167/1248 [1:56:47<07:26,  5.52s/it]
                                                     
{'loss': 0.4925, 'learning_rate': 2.2032703261029686e-06, 'epoch': 2.81}

 94%|█████████▎| 1167/1248 [1:56:47<07:26,  5.52s/it]
 94%|█████████▎| 1168/1248 [1:56:52<07:09,  5.36s/it]
                                                     
{'loss': 0.4825, 'learning_rate': 2.1493989332218468e-06, 'epoch': 2.81}

 94%|█████████▎| 1168/1248 [1:56:52<07:09,  5.36s/it]
 94%|█████████▎| 1169/1248 [1:56:58<07:19,  5.57s/it]
                                                     
{'loss': 0.4968, 'learning_rate': 2.096187157976426e-06, 'epoch': 2.81}

 94%|█████████▎| 1169/1248 [1:56:58<07:19,  5.57s/it]
 94%|█████████▍| 1170/1248 [1:57:04<07:29,  5.76s/it]
                                                     
{'loss': 0.488, 'learning_rate': 2.043635359070928e-06, 'epoch': 2.81}

 94%|█████████▍| 1170/1248 [1:57:04<07:29,  5.76s/it]
 94%|█████████▍| 1171/1248 [1:57:10<07:33,  5.89s/it]
                                                     
{'loss': 0.467, 'learning_rate': 1.9917438907606556e-06, 'epoch': 2.81}

 94%|█████████▍| 1171/1248 [1:57:10<07:33,  5.89s/it]
 94%|█████████▍| 1172/1248 [1:57:16<07:33,  5.96s/it]
                                                     
{'loss': 0.4479, 'learning_rate': 1.9405131028495836e-06, 'epoch': 2.82}

 94%|█████████▍| 1172/1248 [1:57:16<07:33,  5.96s/it]
 94%|█████████▍| 1173/1248 [1:57:22<07:26,  5.95s/it]
                                                     
{'loss': 0.5255, 'learning_rate': 1.8899433406879608e-06, 'epoch': 2.82}

 94%|█████████▍| 1173/1248 [1:57:22<07:26,  5.95s/it]
 94%|█████████▍| 1174/1248 [1:57:27<07:03,  5.73s/it]
                                                     
{'loss': 0.4865, 'learning_rate': 1.8400349451700438e-06, 'epoch': 2.82}

 94%|█████████▍| 1174/1248 [1:57:27<07:03,  5.73s/it]
 94%|█████████▍| 1175/1248 [1:57:33<07:05,  5.82s/it]
                                                     
{'loss': 0.4842, 'learning_rate': 1.7907882527317454e-06, 'epoch': 2.82}

 94%|█████████▍| 1175/1248 [1:57:33<07:05,  5.82s/it]
 94%|█████████▍| 1176/1248 [1:57:40<07:07,  5.94s/it]
                                                     
{'loss': 0.491, 'learning_rate': 1.742203595348435e-06, 'epoch': 2.83}

 94%|█████████▍| 1176/1248 [1:57:40<07:07,  5.94s/it]
 94%|█████████▍| 1177/1248 [1:57:45<07:01,  5.93s/it]
                                                     
{'loss': 0.5241, 'learning_rate': 1.6942813005326075e-06, 'epoch': 2.83}

 94%|█████████▍| 1177/1248 [1:57:45<07:01,  5.93s/it]
 94%|█████████▍| 1178/1248 [1:57:51<06:57,  5.97s/it]
                                                     
{'loss': 0.5181, 'learning_rate': 1.6470216913317626e-06, 'epoch': 2.83}

 94%|█████████▍| 1178/1248 [1:57:52<06:57,  5.97s/it]
 94%|█████████▍| 1179/1248 [1:57:58<06:56,  6.04s/it]
                                                     
{'loss': 0.5163, 'learning_rate': 1.6004250863261739e-06, 'epoch': 2.83}

 94%|█████████▍| 1179/1248 [1:57:58<06:56,  6.04s/it]
 95%|█████████▍| 1180/1248 [1:58:04<06:52,  6.06s/it]
                                                     
{'loss': 0.4831, 'learning_rate': 1.554491799626756e-06, 'epoch': 2.84}

 95%|█████████▍| 1180/1248 [1:58:04<06:52,  6.06s/it]
 95%|█████████▍| 1181/1248 [1:58:10<06:55,  6.20s/it]
                                                     
{'loss': 0.4652, 'learning_rate': 1.509222140872979e-06, 'epoch': 2.84}

 95%|█████████▍| 1181/1248 [1:58:10<06:55,  6.20s/it]
 95%|█████████▍| 1182/1248 [1:58:16<06:45,  6.14s/it]
                                                     
{'loss': 0.4944, 'learning_rate': 1.4646164152307018e-06, 'epoch': 2.84}

 95%|█████████▍| 1182/1248 [1:58:16<06:45,  6.14s/it]
 95%|█████████▍| 1183/1248 [1:58:22<06:38,  6.13s/it]
                                                     
{'loss': 0.501, 'learning_rate': 1.4206749233902084e-06, 'epoch': 2.84}

 95%|█████████▍| 1183/1248 [1:58:22<06:38,  6.13s/it]
 95%|█████████▍| 1184/1248 [1:58:28<06:28,  6.08s/it]
                                                     
{'loss': 0.4823, 'learning_rate': 1.3773979615640975e-06, 'epoch': 2.85}

 95%|█████████▍| 1184/1248 [1:58:28<06:28,  6.08s/it]
 95%|█████████▍| 1185/1248 [1:58:35<06:23,  6.09s/it]
                                                     
{'loss': 0.5145, 'learning_rate': 1.3347858214853736e-06, 'epoch': 2.85}

 95%|█████████▍| 1185/1248 [1:58:35<06:23,  6.09s/it]
 95%|█████████▌| 1186/1248 [1:58:41<06:17,  6.09s/it]
                                                     
{'loss': 0.4883, 'learning_rate': 1.292838790405393e-06, 'epoch': 2.85}

 95%|█████████▌| 1186/1248 [1:58:41<06:17,  6.09s/it]
 95%|█████████▌| 1187/1248 [1:58:47<06:13,  6.13s/it]
                                                     
{'loss': 0.5201, 'learning_rate': 1.2515571510919754e-06, 'epoch': 2.85}

 95%|█████████▌| 1187/1248 [1:58:47<06:13,  6.13s/it]
 95%|█████████▌| 1188/1248 [1:58:53<06:04,  6.08s/it]
                                                     
{'loss': 0.5294, 'learning_rate': 1.2109411818274852e-06, 'epoch': 2.86}

 95%|█████████▌| 1188/1248 [1:58:53<06:04,  6.08s/it]
 95%|█████████▌| 1189/1248 [1:58:59<05:53,  5.99s/it]
                                                     
{'loss': 0.5123, 'learning_rate': 1.1709911564069976e-06, 'epoch': 2.86}

 95%|█████████▌| 1189/1248 [1:58:59<05:53,  5.99s/it]
 95%|█████████▌| 1190/1248 [1:59:05<05:46,  5.98s/it]
                                                     
{'loss': 0.4945, 'learning_rate': 1.1317073441363457e-06, 'epoch': 2.86}

 95%|█████████▌| 1190/1248 [1:59:05<05:46,  5.98s/it]
 95%|█████████▌| 1191/1248 [1:59:10<05:37,  5.93s/it]
                                                     
{'loss': 0.4477, 'learning_rate': 1.0930900098304443e-06, 'epoch': 2.86}

 95%|█████████▌| 1191/1248 [1:59:10<05:37,  5.93s/it]
 96%|█████████▌| 1192/1248 [1:59:16<05:35,  6.00s/it]
                                                     
{'loss': 0.4927, 'learning_rate': 1.055139413811379e-06, 'epoch': 2.87}

 96%|█████████▌| 1192/1248 [1:59:16<05:35,  6.00s/it]
 96%|█████████▌| 1193/1248 [1:59:23<05:30,  6.02s/it]
                                                     
{'loss': 0.5659, 'learning_rate': 1.0178558119067315e-06, 'epoch': 2.87}

 96%|█████████▌| 1193/1248 [1:59:23<05:30,  6.02s/it]
 96%|█████████▌| 1194/1248 [1:59:29<05:27,  6.06s/it]
                                                     
{'loss': 0.4963, 'learning_rate': 9.812394554478355e-07, 'epoch': 2.87}

 96%|█████████▌| 1194/1248 [1:59:29<05:27,  6.06s/it]
 96%|█████████▌| 1195/1248 [1:59:35<05:21,  6.06s/it]
                                                     
{'loss': 0.4735, 'learning_rate': 9.452905912680665e-07, 'epoch': 2.87}

 96%|█████████▌| 1195/1248 [1:59:35<05:21,  6.06s/it]
 96%|█████████▌| 1196/1248 [1:59:41<05:12,  6.02s/it]
                                                     
{'loss': 0.4797, 'learning_rate': 9.10009461701189e-07, 'epoch': 2.88}

 96%|█████████▌| 1196/1248 [1:59:41<05:12,  6.02s/it]
 96%|█████████▌| 1197/1248 [1:59:47<05:05,  5.98s/it]
                                                     
{'loss': 0.4976, 'learning_rate': 8.753963045797342e-07, 'epoch': 2.88}

 96%|█████████▌| 1197/1248 [1:59:47<05:05,  5.98s/it]
 96%|█████████▌| 1198/1248 [1:59:52<04:57,  5.95s/it]
                                                     
{'loss': 0.5003, 'learning_rate': 8.41451353233369e-07, 'epoch': 2.88}

 96%|█████████▌| 1198/1248 [1:59:52<04:57,  5.95s/it]
 96%|█████████▌| 1199/1248 [1:59:59<04:56,  6.05s/it]
                                                     
{'loss': 0.5025, 'learning_rate': 8.081748364873521e-07, 'epoch': 2.88}

 96%|█████████▌| 1199/1248 [1:59:59<04:56,  6.05s/it]
 96%|█████████▌| 1200/1248 [2:00:04<04:46,  5.96s/it]
                                                     
{'loss': 0.5096, 'learning_rate': 7.755669786609687e-07, 'epoch': 2.88}

 96%|█████████▌| 1200/1248 [2:00:04<04:46,  5.96s/it]/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/nn/modules/module.py:1879: UserWarning: Positional args are being deprecated, use kwargs instead. Refer to https://pytorch.org/docs/master/generated/torch.nn.Module.html#torch.nn.Module.state_dict for details.
  warnings.warn(
/data/yuanxiaoyan/ECGpro/llava/lib/python3.10/site-packages/torch/utils/checkpoint.py:429: UserWarning: torch.utils.checkpoint: please pass in use_reentrant=True or use_reentrant=False explicitly. The default value of use_reentrant will be updated to be False in the future. To maintain current behavior, pass use_reentrant=True. It is recommended that you use use_reentrant=False. Refer to docs for more details on the differences between the two variants.
  warnings.warn(

 96%|█████████▌| 1201/1248 [2:00:31<09:26, 12.06s/it]
                                                     
{'loss': 0.4802, 'learning_rate': 7.436279995660212e-07, 'epoch': 2.89}

 96%|█████████▌| 1201/1248 [2:00:31<09:26, 12.06s/it]
 96%|█████████▋| 1202/1248 [2:00:37<07:54, 10.32s/it]
                                                     
{'loss': 0.5113, 'learning_rate': 7.123581145053848e-07, 'epoch': 2.89}

 96%|█████████▋| 1202/1248 [2:00:37<07:54, 10.32s/it]
 96%|█████████▋| 1203/1248 [2:00:43<06:42,  8.94s/it]
                                                     
{'loss': 0.6472, 'learning_rate': 6.817575342714988e-07, 'epoch': 2.89}

 96%|█████████▋| 1203/1248 [2:00:43<06:42,  8.94s/it]
 96%|█████████▋| 1204/1248 [2:00:49<05:58,  8.14s/it]
                                                     
{'loss': 0.5218, 'learning_rate': 6.518264651449779e-07, 'epoch': 2.89}

 96%|█████████▋| 1204/1248 [2:00:49<05:58,  8.14s/it]
 97%|█████████▋| 1205/1248 [2:00:54<05:07,  7.15s/it]
                                                     
{'loss': 0.4833, 'learning_rate': 6.225651088932361e-07, 'epoch': 2.9}

 97%|█████████▋| 1205/1248 [2:00:54<05:07,  7.15s/it]
 97%|█████████▋| 1206/1248 [2:00:59<04:38,  6.64s/it]
                                                     
{'loss': 0.506, 'learning_rate': 5.939736627690539e-07, 'epoch': 2.9}

 97%|█████████▋| 1206/1248 [2:00:59<04:38,  6.64s/it]
 97%|█████████▋| 1207/1248 [2:01:05<04:16,  6.24s/it]
                                                     
{'loss': 0.5167, 'learning_rate': 5.660523195093692e-07, 'epoch': 2.9}

 97%|█████████▋| 1207/1248 [2:01:05<04:16,  6.24s/it]
 97%|█████████▋| 1208/1248 [2:01:10<04:02,  6.07s/it]
                                                     
{'loss': 0.5107, 'learning_rate': 5.388012673338661e-07, 'epoch': 2.9}

 97%|█████████▋| 1208/1248 [2:01:10<04:02,  6.07s/it]
 97%|█████████▋| 1209/1248 [2:01:15<03:44,  5.75s/it]
                                                     
{'loss': 0.4712, 'learning_rate': 5.122206899437654e-07, 'epoch': 2.91}

 97%|█████████▋| 1209/1248 [2:01:15<03:44,  5.75s/it]
 97%|█████████▋| 1210/1248 [2:01:21<03:35,  5.67s/it]
                                                     
{'loss': 0.4886, 'learning_rate': 4.863107665205701e-07, 'epoch': 2.91}

 97%|█████████▋| 1210/1248 [2:01:21<03:35,  5.67s/it]
 97%|█████████▋| 1211/1248 [2:01:26<03:24,  5.52s/it]
                                                     
{'loss': 0.5197, 'learning_rate': 4.610716717248442e-07, 'epoch': 2.91}

 97%|█████████▋| 1211/1248 [2:01:26<03:24,  5.52s/it]
 97%|█████████▋| 1212/1248 [2:01:31<03:13,  5.38s/it]
                                                     
{'loss': 0.5076, 'learning_rate': 4.365035756950797e-07, 'epoch': 2.91}

 97%|█████████▋| 1212/1248 [2:01:31<03:13,  5.38s/it]
 97%|█████████▋| 1213/1248 [2:01:36<03:05,  5.29s/it]
                                                     
{'loss': 0.4925, 'learning_rate': 4.126066440464982e-07, 'epoch': 2.92}

 97%|█████████▋| 1213/1248 [2:01:36<03:05,  5.29s/it]
 97%|█████████▋| 1214/1248 [2:01:42<03:05,  5.46s/it]
                                                     
{'loss': 0.4868, 'learning_rate': 3.893810378699514e-07, 'epoch': 2.92}

 97%|█████████▋| 1214/1248 [2:01:42<03:05,  5.46s/it]
 97%|█████████▋| 1215/1248 [2:01:48<03:04,  5.60s/it]
                                                     
{'loss': 0.4997, 'learning_rate': 3.6682691373086665e-07, 'epoch': 2.92}

 97%|█████████▋| 1215/1248 [2:01:48<03:04,  5.60s/it]
 97%|█████████▋| 1216/1248 [2:01:54<03:02,  5.70s/it]
                                                     
{'loss': 0.4625, 'learning_rate': 3.449444236681254e-07, 'epoch': 2.92}

 97%|█████████▋| 1216/1248 [2:01:54<03:02,  5.70s/it]
 98%|█████████▊| 1217/1248 [2:02:00<03:00,  5.84s/it]
                                                     
{'loss': 0.5222, 'learning_rate': 3.2373371519310856e-07, 'epoch': 2.93}

 98%|█████████▊| 1217/1248 [2:02:00<03:00,  5.84s/it]
 98%|█████████▊| 1218/1248 [2:02:06<02:54,  5.82s/it]
                                                     
{'loss': 0.4554, 'learning_rate': 3.0319493128866396e-07, 'epoch': 2.93}

 98%|█████████▊| 1218/1248 [2:02:06<02:54,  5.82s/it]
 98%|█████████▊| 1219/1248 [2:02:12<02:50,  5.88s/it]
                                                     
{'loss': 0.4641, 'learning_rate': 2.833282104081514e-07, 'epoch': 2.93}

 98%|█████████▊| 1219/1248 [2:02:12<02:50,  5.88s/it]
 98%|█████████▊| 1220/1248 [2:02:18<02:44,  5.87s/it]
                                                     
{'loss': 0.4878, 'learning_rate': 2.6413368647449923e-07, 'epoch': 2.93}

 98%|█████████▊| 1220/1248 [2:02:18<02:44,  5.87s/it]
 98%|█████████▊| 1221/1248 [2:02:23<02:33,  5.69s/it]
                                                     
{'loss': 0.5033, 'learning_rate': 2.4561148887930483e-07, 'epoch': 2.94}

 98%|█████████▊| 1221/1248 [2:02:23<02:33,  5.69s/it]
 98%|█████████▊| 1222/1248 [2:02:29<02:29,  5.77s/it]
                                                     
{'loss': 0.4918, 'learning_rate': 2.2776174248199112e-07, 'epoch': 2.94}

 98%|█████████▊| 1222/1248 [2:02:29<02:29,  5.77s/it]
 98%|█████████▊| 1223/1248 [2:02:35<02:24,  5.80s/it]
                                                     
{'loss': 0.4801, 'learning_rate': 2.1058456760891798e-07, 'epoch': 2.94}

 98%|█████████▊| 1223/1248 [2:02:35<02:24,  5.80s/it]
 98%|█████████▊| 1224/1248 [2:02:40<02:16,  5.69s/it]
                                                     
{'loss': 0.5003, 'learning_rate': 1.9408008005260548e-07, 'epoch': 2.94}

 98%|█████████▊| 1224/1248 [2:02:40<02:16,  5.69s/it]
 98%|█████████▊| 1225/1248 [2:02:46<02:10,  5.68s/it]
                                                     
{'loss': 0.4977, 'learning_rate': 1.7824839107094537e-07, 'epoch': 2.94}

 98%|█████████▊| 1225/1248 [2:02:46<02:10,  5.68s/it]
 98%|█████████▊| 1226/1248 [2:02:51<02:04,  5.67s/it]
                                                     
{'loss': 0.4849, 'learning_rate': 1.630896073864352e-07, 'epoch': 2.95}

 98%|█████████▊| 1226/1248 [2:02:51<02:04,  5.67s/it]
 98%|█████████▊| 1227/1248 [2:02:57<01:59,  5.71s/it]
                                                     
{'loss': 0.5121, 'learning_rate': 1.4860383118547872e-07, 'epoch': 2.95}

 98%|█████████▊| 1227/1248 [2:02:57<01:59,  5.71s/it]
 98%|█████████▊| 1228/1248 [2:03:03<01:53,  5.68s/it]
                                                     
{'loss': 0.4787, 'learning_rate': 1.3479116011769767e-07, 'epoch': 2.95}

 98%|█████████▊| 1228/1248 [2:03:03<01:53,  5.68s/it]
 98%|█████████▊| 1229/1248 [2:03:08<01:44,  5.51s/it]
                                                     
{'loss': 0.5092, 'learning_rate': 1.2165168729525444e-07, 'epoch': 2.95}

 98%|█████████▊| 1229/1248 [2:03:08<01:44,  5.51s/it]
 99%|█████████▊| 1230/1248 [2:03:13<01:37,  5.41s/it]
                                                     
{'loss': 0.5297, 'learning_rate': 1.0918550129223048e-07, 'epoch': 2.96}

 99%|█████████▊| 1230/1248 [2:03:13<01:37,  5.41s/it]
 99%|█████████▊| 1231/1248 [2:03:18<01:28,  5.20s/it]
                                                     
{'loss': 0.4932, 'learning_rate': 9.739268614405994e-08, 'epoch': 2.96}

 99%|█████████▊| 1231/1248 [2:03:18<01:28,  5.20s/it]
 99%|█████████▊| 1232/1248 [2:03:23<01:23,  5.20s/it]
                                                     
{'loss': 0.4561, 'learning_rate': 8.627332134690802e-08, 'epoch': 2.96}

 99%|█████████▊| 1232/1248 [2:03:23<01:23,  5.20s/it]
 99%|█████████▉| 1233/1248 [2:03:28<01:18,  5.25s/it]
                                                     
{'loss': 0.5047, 'learning_rate': 7.582748185719358e-08, 'epoch': 2.96}

 99%|█████████▉| 1233/1248 [2:03:28<01:18,  5.25s/it]
 99%|█████████▉| 1234/1248 [2:03:34<01:13,  5.26s/it]
                                                     
{'loss': 0.4753, 'learning_rate': 6.605523809102287e-08, 'epoch': 2.97}

 99%|█████████▉| 1234/1248 [2:03:34<01:13,  5.26s/it]
 99%|█████████▉| 1235/1248 [2:03:39<01:08,  5.27s/it]
                                                     
{'loss': 0.4607, 'learning_rate': 5.695665592376776e-08, 'epoch': 2.97}

 99%|█████████▉| 1235/1248 [2:03:39<01:08,  5.27s/it]
 99%|█████████▉| 1236/1248 [2:03:44<01:02,  5.20s/it]
                                                     
{'loss': 0.4763, 'learning_rate': 4.853179668959928e-08, 'epoch': 2.97}

 99%|█████████▉| 1236/1248 [2:03:44<01:02,  5.20s/it]
 99%|█████████▉| 1237/1248 [2:03:49<00:57,  5.21s/it]
                                                     
{'loss': 0.4823, 'learning_rate': 4.078071718107701e-08, 'epoch': 2.97}

 99%|█████████▉| 1237/1248 [2:03:49<00:57,  5.21s/it]
 99%|█████████▉| 1238/1248 [2:03:54<00:52,  5.21s/it]
                                                     
{'loss': 0.5239, 'learning_rate': 3.370346964876036e-08, 'epoch': 2.98}

 99%|█████████▉| 1238/1248 [2:03:55<00:52,  5.21s/it]
 99%|█████████▉| 1239/1248 [2:04:00<00:47,  5.30s/it]
                                                     
{'loss': 0.4965, 'learning_rate': 2.7300101800853405e-08, 'epoch': 2.98}

 99%|█████████▉| 1239/1248 [2:04:00<00:47,  5.30s/it]
 99%|█████████▉| 1240/1248 [2:04:06<00:43,  5.39s/it]
                                                     
{'loss': 0.4997, 'learning_rate': 2.1570656802905043e-08, 'epoch': 2.98}

 99%|█████████▉| 1240/1248 [2:04:06<00:43,  5.39s/it]
 99%|█████████▉| 1241/1248 [2:04:11<00:37,  5.40s/it]
                                                     
{'loss': 0.4919, 'learning_rate': 1.6515173277498186e-08, 'epoch': 2.98}

 99%|█████████▉| 1241/1248 [2:04:11<00:37,  5.40s/it]
100%|█████████▉| 1242/1248 [2:04:17<00:33,  5.56s/it]
                                                     
{'loss': 0.4831, 'learning_rate': 1.2133685303994391e-08, 'epoch': 2.99}

100%|█████████▉| 1242/1248 [2:04:17<00:33,  5.56s/it]
100%|█████████▉| 1243/1248 [2:04:23<00:28,  5.68s/it]
                                                     
{'loss': 0.4729, 'learning_rate': 8.426222418311814e-09, 'epoch': 2.99}

100%|█████████▉| 1243/1248 [2:04:23<00:28,  5.68s/it]
100%|█████████▉| 1244/1248 [2:04:29<00:23,  5.91s/it]
                                                     
{'loss': 0.5026, 'learning_rate': 5.3928096127031644e-09, 'epoch': 2.99}

100%|█████████▉| 1244/1248 [2:04:29<00:23,  5.91s/it]
100%|█████████▉| 1245/1248 [2:04:35<00:17,  5.86s/it]
                                                     
{'loss': 0.4937, 'learning_rate': 3.033467335622486e-09, 'epoch': 2.99}

100%|█████████▉| 1245/1248 [2:04:35<00:17,  5.86s/it]
100%|█████████▉| 1246/1248 [2:04:41<00:11,  5.84s/it]
                                                     
{'loss': 0.4747, 'learning_rate': 1.3482114915475131e-09, 'epoch': 3.0}

100%|█████████▉| 1246/1248 [2:04:41<00:11,  5.84s/it]
100%|█████████▉| 1247/1248 [2:04:47<00:05,  5.88s/it]
                                                     
{'loss': 0.5171, 'learning_rate': 3.370534409130599e-10, 'epoch': 3.0}

100%|█████████▉| 1247/1248 [2:04:47<00:05,  5.88s/it]
100%|██████████| 1248/1248 [2:04:53<00:00,  5.97s/it]
                                                     
{'loss': 0.4778, 'learning_rate': 0.0, 'epoch': 3.0}

100%|██████████| 1248/1248 [2:04:53<00:00,  5.97s/it]
                                                     
{'train_runtime': 7494.0142, 'train_samples_per_second': 7.984, 'train_steps_per_second': 0.167, 'train_loss': 0.5991567766102843, 'epoch': 3.0}

100%|██████████| 1248/1248 [2:04:53<00:00,  5.97s/it]
100%|██████████| 1248/1248 [2:04:53<00:00,  6.00s/it]
Some non-default generation parameters are set in the model config. These should go into a GenerationConfig file (https://huggingface.co/docs/transformers/generation_strategies#save-a-custom-decoding-strategy-with-your-model) instead. This warning will be raised to an exception in v4.41.
Non-default generation parameters: {'max_length': 4096}
[1;34mwandb[0m: 
[1;34mwandb[0m: You can sync this run to the cloud by running:
[1;34mwandb[0m: [1mwandb sync /data/yuanxiaoyan/ECGpro/ECG-Chat/wandb/offline-run-20250913_052602-kmhs1dpa[0m
[1;34mwandb[0m: Find logs at: [1;35mwandb/offline-run-20250913_052602-kmhs1dpa/logs[0m
[2025-09-13 07:31:06,949] [INFO] [launch.py:347:main] Process 3492543 exits successfully.
[2025-09-13 07:31:07,952] [INFO] [launch.py:347:main] Process 3492544 exits successfully.
[2025-09-13 07:31:07,952] [INFO] [launch.py:347:main] Process 3492542 exits successfully.
