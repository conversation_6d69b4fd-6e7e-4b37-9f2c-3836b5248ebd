pip==25.2
sentencepiece==0.1.99
pytz==2025.2
pydub==0.25.1
mpmath==1.3.0
websockets==11.0.3
urllib3==2.5.0
tzdata==2025.2
typing_extensions==4.15.0
tqdm==4.67.1
tomlkit==0.12.0
threadpoolctl==3.6.0
sympy==1.14.0
svgwrite==1.4.3
sniffio==1.3.1
six==1.17.0
shortuuid==1.0.13
shellingham==1.5.4
semantic-version==2.10.0
safetensors==0.6.2
ruff==0.12.12
rpds-py==0.27.1
regex==2025.9.1
PyYAML==6.0.2
python-multipart==0.0.20
pyparsing==3.2.3
Pygments==2.19.2
psutil==7.0.0
pillow==10.4.0
packaging==25.0
orjson==3.11.3
nvidia-nvtx-cu12==12.1.105
nvidia-nvjitlink-cu12==12.9.86
nvidia-nccl-cu12==2.18.1
nvidia-curand-cu12==**********
nvidia-cufft-cu12==*********
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cublas-cu12==********
numpy==1.26.4
networkx==3.4.2
narwhals==2.4.0
mdurl==0.1.2
MarkupSafe==2.1.5
markdown2==2.5.4
latex2mathml==3.78.1
kiwisolver==1.4.9
joblib==1.5.2
importlib_resources==6.5.2
idna==3.10
hf-xet==1.1.9
h11==0.14.0
fsspec==2025.9.0
fonttools==4.59.2
filelock==3.19.1
ffmpy==0.6.1
einops==0.6.1
cycler==0.12.1
click==8.2.1
charset-normalizer==3.4.3
certifi==2025.8.3
attrs==25.3.0
annotated-types==0.7.0
aiofiles==23.2.1
wavedrom==2.0.3.post3
uvicorn==0.35.0
typing-inspection==0.4.1
triton==2.1.0
scipy==1.15.3
requests==2.32.5
referencing==0.36.2
python-dateutil==2.9.0.post0
pydantic_core==2.33.2
nvidia-cusparse-cu12==**********
nvidia-cudnn-cu12==********
markdown-it-py==4.0.0
Jinja2==3.1.6
exceptiongroup==1.3.0
einops-exts==0.0.4
contourpy==1.3.2
scikit-learn==1.2.2
rich==14.1.0
pydantic==2.11.7
pandas==2.3.2
nvidia-cusolver-cu12==**********
matplotlib==3.10.6
jsonschema-specifications==2025.9.1
huggingface-hub==0.34.4
anyio==4.10.0
typer==0.17.4
torch==2.1.2
tokenizers==0.15.1
starlette==0.47.3
jsonschema==4.25.1
httpcore==0.17.3
transformers==4.37.2
torchvision==0.16.2
httpx==0.24.0
fastapi==0.116.1
bitsandbytes==0.45.5
altair==5.5.0
pycparser==2.23
gradio_client==0.8.1
gradio==4.16.0
py-cpuinfo==9.0.0
hjson==3.1.0
smmap==5.0.2
sentry-sdk==2.37.1
pynvml==13.0.1
protobuf==6.32.0
platformdirs==4.4.0
ninja==1.13.0
gitdb==4.0.12
GitPython==3.1.45
wandb==0.21.3
deepspeed==0.12.6
llava==1.2.2.post1
propcache==0.3.2
multidict==6.6.4
frozenlist==1.7.0
async-timeout==5.0.1
aiohappyeyeballs==2.6.1
yarl==1.20.1
cffi==2.0.0
aiosignal==1.4.0
soundfile==0.13.1
aiohttp==3.12.15
wfdb==4.3.0
julius==0.2.7
wcwidth==0.2.13
braceexpand==0.1.7
webdataset==0.2.86
ftfy==6.3.1
timm==1.0.19
open_clip_torch==3.1.0
peft==0.4.0
accelerate==0.21.0
nvidia-ml-py==13.580.65
wheel==0.45.1
setuptools==80.9.0
nvitop==1.5.3
flash-attn==2.7.3
platformdirs==4.2.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
