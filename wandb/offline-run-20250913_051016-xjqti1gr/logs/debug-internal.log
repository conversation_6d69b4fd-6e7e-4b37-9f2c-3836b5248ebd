{"time":"2025-09-13T05:10:16.373799429Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-13T05:10:16.504986077Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-13T05:10:16.505159541Z","level":"INFO","msg":"stream: created new stream","id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.505176818Z","level":"INFO","msg":"stream: started","id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.505235427Z","level":"INFO","msg":"sender: started","stream_id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.505227385Z","level":"INFO","msg":"writer: started","stream_id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.505294917Z","level":"INFO","msg":"handler: started","stream_id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.506133606Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-09-13T05:10:16.93884171Z","level":"INFO","msg":"stream: closing","id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.939184367Z","level":"INFO","msg":"handler: closed","stream_id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.939285513Z","level":"INFO","msg":"sender: closed","stream_id":"xjqti1gr"}
{"time":"2025-09-13T05:10:16.939297821Z","level":"INFO","msg":"stream: closed","id":"xjqti1gr"}
