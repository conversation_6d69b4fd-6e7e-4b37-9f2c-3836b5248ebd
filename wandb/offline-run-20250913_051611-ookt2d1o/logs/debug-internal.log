{"time":"2025-09-13T05:16:11.560541875Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-13T05:16:11.691898043Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-13T05:16:11.692046859Z","level":"INFO","msg":"stream: created new stream","id":"ookt2d1o"}
{"time":"2025-09-13T05:16:11.692062749Z","level":"INFO","msg":"stream: started","id":"ookt2d1o"}
{"time":"2025-09-13T05:16:11.692115634Z","level":"INFO","msg":"writer: started","stream_id":"ookt2d1o"}
{"time":"2025-09-13T05:16:11.692140019Z","level":"INFO","msg":"handler: started","stream_id":"ookt2d1o"}
{"time":"2025-09-13T05:16:11.692125286Z","level":"INFO","msg":"sender: started","stream_id":"ookt2d1o"}
{"time":"2025-09-13T05:16:11.69302422Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
