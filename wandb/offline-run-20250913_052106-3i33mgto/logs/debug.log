2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_setup.py:_flush():81] Current SDK version is 0.21.3
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_setup.py:_flush():81] Configure stats pid to 3455972
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/.config/wandb/settings
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_setup.py:_flush():81] Loading settings from /data/yuanxiaoyan/ECGpro/ECG-Chat/wandb/settings
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_init.py:setup_run_log_directory():686] Logging user logs to /data/yuanxiaoyan/ECGpro/ECG-Chat/wandb/offline-run-20250913_052106-3i33mgto/logs/debug.log
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_init.py:setup_run_log_directory():687] Logging internal logs to /data/yuanxiaoyan/ECGpro/ECG-Chat/wandb/offline-run-20250913_052106-3i33mgto/logs/debug-internal.log
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_init.py:init():813] calling init triggers
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_init.py:init():818] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-09-13 05:21:06,703 INFO    MainThread:3455972 [wandb_init.py:init():854] starting backend
2025-09-13 05:21:06,910 INFO    MainThread:3455972 [wandb_init.py:init():857] sending inform_init request
2025-09-13 05:21:06,914 INFO    MainThread:3455972 [wandb_init.py:init():865] backend started and connected
2025-09-13 05:21:06,915 INFO    MainThread:3455972 [wandb_init.py:init():936] updated telemetry
2025-09-13 05:21:06,920 INFO    MainThread:3455972 [wandb_init.py:init():960] communicating run to backend with 90.0 second timeout
2025-09-13 05:21:07,055 INFO    MainThread:3455972 [wandb_init.py:init():1011] starting run threads in backend
2025-09-13 05:21:07,131 INFO    MainThread:3455972 [wandb_run.py:_console_start():2494] atexit reg
2025-09-13 05:21:07,131 INFO    MainThread:3455972 [wandb_run.py:_redirect():2342] redirect: wrap_raw
2025-09-13 05:21:07,132 INFO    MainThread:3455972 [wandb_run.py:_redirect():2411] Wrapping output streams.
2025-09-13 05:21:07,132 INFO    MainThread:3455972 [wandb_run.py:_redirect():2434] Redirects installed.
2025-09-13 05:21:07,133 INFO    MainThread:3455972 [wandb_init.py:init():1057] run started, returning control to user process
2025-09-13 05:21:07,134 INFO    MainThread:3455972 [wandb_run.py:_config_callback():1380] config_cb None None {'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 5120, 'intermediate_size': 13824, 'num_hidden_layers': 40, 'num_attention_heads': 40, 'num_key_value_heads': 40, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': False, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'return_dict': True, 'output_hidden_states': False, 'output_attentions': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 4096, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': 0, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': '/data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5', 'transformers_version': '4.37.2', 'model_type': 'llava_llama', 'mm_ecg_tower': '/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt', 'use_mm_proj': True, 'mm_projector_type': 'linear', 'mm_hidden_size': 768, 'mm_patch_merge_type': 'flat', 'tokenizer_padding_side': 'right', 'tokenizer_model_max_length': 2048, 'tune_mm_mlp_adapter': False, 'freeze_mm_mlp_adapter': False, 'mm_use_ecg_start_end': False, 'mm_projector_lr': None, 'mm_use_ecg_patch_token': False, 'output_dir': './checkpoints/llava-vicuna-13b-v1.5-finetune_lora', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': False, 'do_predict': False, 'evaluation_strategy': 'no', 'prediction_loss_only': False, 'per_device_train_batch_size': 16, 'per_device_eval_batch_size': 4, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 1, 'eval_accumulation_steps': None, 'eval_delay': 0, 'learning_rate': 0.0002, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 3.0, 'max_steps': -1, 'lr_scheduler_type': 'cosine', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.03, 'warmup_steps': 0, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': './checkpoints/llava-vicuna-13b-v1.5-finetune_lora/runs/Sep13_05-19-04_eipc31', 'logging_strategy': 'steps', 'logging_first_step': False, 'logging_steps': 1.0, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 50000, 'save_total_limit': 1, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': True, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': None, 'dataloader_num_workers': 4, 'past_index': -1, 'run_name': './checkpoints/llava-vicuna-13b-v1.5-finetune_lora', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'deepspeed': '/data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llava/scripts/zero2.json', 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': True, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': False, 'hub_always_push': False, 'gradient_checkpointing': True, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'dispatch_batches': None, 'split_batches': False, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'cache_dir': None, 'mpt_attn_impl': 'triton', 'model_max_length': 2048, 'double_quant': True, 'quant_type': 'nf4', 'bits': 16, 'lora_enable': True, 'lora_r': 64, 'lora_alpha': 16, 'lora_dropout': 0.05, 'lora_weight_path': '', 'lora_bias': 'none', 'group_by_modality_length': True}
