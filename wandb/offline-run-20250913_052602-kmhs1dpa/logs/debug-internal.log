{"time":"2025-09-13T05:26:02.634192379Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-13T05:26:02.758085579Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-13T05:26:02.758218263Z","level":"INFO","msg":"stream: created new stream","id":"kmhs1dpa"}
{"time":"2025-09-13T05:26:02.758229989Z","level":"INFO","msg":"stream: started","id":"kmhs1dpa"}
{"time":"2025-09-13T05:26:02.758298132Z","level":"INFO","msg":"sender: started","stream_id":"kmhs1dpa"}
{"time":"2025-09-13T05:26:02.758396649Z","level":"INFO","msg":"handler: started","stream_id":"kmhs1dpa"}
{"time":"2025-09-13T05:26:02.758293152Z","level":"INFO","msg":"writer: started","stream_id":"kmhs1dpa"}
{"time":"2025-09-13T05:26:02.759489222Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-09-13T07:31:04.841705835Z","level":"INFO","msg":"stream: closing","id":"kmhs1dpa"}
{"time":"2025-09-13T07:31:04.84203023Z","level":"INFO","msg":"handler: closed","stream_id":"kmhs1dpa"}
{"time":"2025-09-13T07:31:04.842254002Z","level":"INFO","msg":"sender: closed","stream_id":"kmhs1dpa"}
{"time":"2025-09-13T07:31:04.842283659Z","level":"INFO","msg":"stream: closed","id":"kmhs1dpa"}
