{"time":"2025-09-14T14:34:02.170911892Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-14T14:34:02.296285325Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-14T14:34:02.296430362Z","level":"INFO","msg":"stream: created new stream","id":"qy9her97"}
{"time":"2025-09-14T14:34:02.296446633Z","level":"INFO","msg":"stream: started","id":"qy9her97"}
{"time":"2025-09-14T14:34:02.29651Z","level":"INFO","msg":"handler: started","stream_id":"qy9her97"}
{"time":"2025-09-14T14:34:02.296511663Z","level":"INFO","msg":"writer: started","stream_id":"qy9her97"}
{"time":"2025-09-14T14:34:02.296631988Z","level":"INFO","msg":"sender: started","stream_id":"qy9her97"}
{"time":"2025-09-14T14:34:02.297761475Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-09-14T20:12:54.80354518Z","level":"INFO","msg":"stream: closing","id":"qy9her97"}
{"time":"2025-09-14T20:12:54.803852053Z","level":"INFO","msg":"handler: closed","stream_id":"qy9her97"}
{"time":"2025-09-14T20:12:54.803980158Z","level":"INFO","msg":"sender: closed","stream_id":"qy9her97"}
{"time":"2025-09-14T20:12:54.803995308Z","level":"INFO","msg":"stream: closed","id":"qy9her97"}
