{"time":"2025-09-11T16:10:22.230310205Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-11T16:10:22.355835292Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-11T16:10:22.355980307Z","level":"INFO","msg":"stream: created new stream","id":"21fx098i"}
{"time":"2025-09-11T16:10:22.355993684Z","level":"INFO","msg":"stream: started","id":"21fx098i"}
{"time":"2025-09-11T16:10:22.356055753Z","level":"INFO","msg":"handler: started","stream_id":"21fx098i"}
{"time":"2025-09-11T16:10:22.356061232Z","level":"INFO","msg":"writer: started","stream_id":"21fx098i"}
{"time":"2025-09-11T16:10:22.356098173Z","level":"INFO","msg":"sender: started","stream_id":"21fx098i"}
{"time":"2025-09-11T16:10:22.357154855Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
