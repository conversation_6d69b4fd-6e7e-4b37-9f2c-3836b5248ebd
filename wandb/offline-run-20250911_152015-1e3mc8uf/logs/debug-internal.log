{"time":"2025-09-11T15:20:15.559339242Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-11T15:20:15.691600595Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-11T15:20:15.691763867Z","level":"INFO","msg":"stream: created new stream","id":"1e3mc8uf"}
{"time":"2025-09-11T15:20:15.691781186Z","level":"INFO","msg":"stream: started","id":"1e3mc8uf"}
{"time":"2025-09-11T15:20:15.691901062Z","level":"INFO","msg":"writer: started","stream_id":"1e3mc8uf"}
{"time":"2025-09-11T15:20:15.691959421Z","level":"INFO","msg":"sender: started","stream_id":"1e3mc8uf"}
{"time":"2025-09-11T15:20:15.692020626Z","level":"INFO","msg":"handler: started","stream_id":"1e3mc8uf"}
{"time":"2025-09-11T15:20:15.693127233Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
