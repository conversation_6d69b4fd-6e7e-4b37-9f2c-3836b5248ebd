{"time":"2025-09-12T16:34:08.997805185Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-12T16:34:09.123538402Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-12T16:34:09.123688548Z","level":"INFO","msg":"stream: created new stream","id":"dn8eq1n9"}
{"time":"2025-09-12T16:34:09.123706133Z","level":"INFO","msg":"stream: started","id":"dn8eq1n9"}
{"time":"2025-09-12T16:34:09.123755858Z","level":"INFO","msg":"handler: started","stream_id":"dn8eq1n9"}
{"time":"2025-09-12T16:34:09.123768119Z","level":"INFO","msg":"writer: started","stream_id":"dn8eq1n9"}
{"time":"2025-09-12T16:34:09.123826236Z","level":"INFO","msg":"sender: started","stream_id":"dn8eq1n9"}
{"time":"2025-09-12T16:34:09.124912331Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-09-13T03:33:45.928942214Z","level":"INFO","msg":"stream: closing","id":"dn8eq1n9"}
{"time":"2025-09-13T03:33:45.929242914Z","level":"INFO","msg":"handler: closed","stream_id":"dn8eq1n9"}
{"time":"2025-09-13T03:33:45.929318942Z","level":"INFO","msg":"sender: closed","stream_id":"dn8eq1n9"}
{"time":"2025-09-13T03:33:45.929349251Z","level":"INFO","msg":"stream: closed","id":"dn8eq1n9"}
