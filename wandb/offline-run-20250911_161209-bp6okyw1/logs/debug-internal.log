{"time":"2025-09-11T16:12:10.10871863Z","level":"INFO","msg":"stream: starting","core version":"0.21.3"}
{"time":"2025-09-11T16:12:10.236020334Z","level":"WARN","msg":"featurechecker: GraphQL client is nil, skipping feature loading"}
{"time":"2025-09-11T16:12:10.23615473Z","level":"INFO","msg":"stream: created new stream","id":"bp6okyw1"}
{"time":"2025-09-11T16:12:10.236169897Z","level":"INFO","msg":"stream: started","id":"bp6okyw1"}
{"time":"2025-09-11T16:12:10.236241995Z","level":"INFO","msg":"sender: started","stream_id":"bp6okyw1"}
{"time":"2025-09-11T16:12:10.23633399Z","level":"INFO","msg":"handler: started","stream_id":"bp6okyw1"}
{"time":"2025-09-11T16:12:10.236238475Z","level":"INFO","msg":"writer: started","stream_id":"bp6okyw1"}
{"time":"2025-09-11T16:12:10.237676959Z","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-09-12T00:42:12.891218003Z","level":"INFO","msg":"stream: closing","id":"bp6okyw1"}
{"time":"2025-09-12T00:42:12.891745707Z","level":"INFO","msg":"handler: closed","stream_id":"bp6okyw1"}
{"time":"2025-09-12T00:42:12.891913738Z","level":"INFO","msg":"sender: closed","stream_id":"bp6okyw1"}
{"time":"2025-09-12T00:42:12.891932083Z","level":"INFO","msg":"stream: closed","id":"bp6okyw1"}
