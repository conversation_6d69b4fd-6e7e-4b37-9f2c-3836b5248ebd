#!/usr/bin/env python3
"""
LLaVA ECG Inference Script with LoRA
Supports interactive ECG analysis and batch processing
"""

import os
import sys
import json
import argparse
import torch
import numpy as np
import wfdb
from pathlib import Path

# Add paths to sys.path for proper imports
script_dir = os.path.dirname(os.path.abspath(__file__))
open_clip_dir = os.path.join(script_dir, '..', 'open_clip')
dataset_extra_dir = os.path.join(script_dir, '..', 'datasetfromanotherproject')
sys.path.insert(0, script_dir)
sys.path.insert(0, open_clip_dir)
if os.path.isdir(dataset_extra_dir):
    sys.path.insert(0, dataset_extra_dir)

from transformers import AutoTokenizer
from llava.model.builder import load_pretrained_model
from llava.conversation import conv_templates, SeparatorStyle
from llava.utils import disable_torch_init
from llava.constants import ECG_TOKEN_INDEX, DEFAULT_ECG_PATCH_TOKEN, DEFAULT_ECG_START_TOKEN, DEFAULT_ECG_END_TOKEN, DEFAULT_ECG_TOKEN
from llava.mm_utils import tokenizer_ecg_token
try:
    # Optional: new dataset reader
    from finetune_dataset import getdataset
except Exception:
    getdataset = None

class LLaVAECGInference:
    def __init__(self, model_path, base_model_path, ecg_tower_path, ecg_folder, device='cuda',
                 new_dataset_name=None, new_data_path=None, new_csv_path=None):
        self.model_path = model_path
        self.base_model_path = base_model_path
        self.ecg_tower_path = ecg_tower_path
        self.ecg_folder = ecg_folder
        self.device = device
        self.model = None
        self.tokenizer = None
        self.ecg_tower = None
        self.context_len = None
        # Optional new dataset support
        self.new_dataset_name = new_dataset_name
        self.new_data_path = new_data_path
        self.new_csv_path = new_csv_path
        self.new_dataset = None
        # Auto-infer new_data_path if not provided, consistent with datasetfromanotherproject/main_single_ori.py
        if self.new_dataset_name and not self.new_data_path:
            base_meta_path = '/data/yuanxiaoyan/ECGpro/datasets/downstream'
            if 'ptbxl' in self.new_dataset_name:
                self.new_data_path = os.path.join(base_meta_path, 'ptbxl')
            elif self.new_dataset_name in ('icbeb', 'icbeb2018', 'cpsc2018'):
                self.new_data_path = os.path.join(base_meta_path, 'ICBEB', 'records500')
            elif 'chapman' in self.new_dataset_name or 'CSN' in self.new_dataset_name:
                self.new_data_path = base_meta_path
            else:
                print(f"Warning: Unknown dataset name '{self.new_dataset_name}'. Please provide --new_data_path explicitly.")

        if self.new_dataset_name and self.new_data_path and self.new_csv_path and getdataset is not None:
            try:
                import pandas as pd  # local import to avoid hard dep if unused
                # Build dataset once; use mode='test' for inference
                self.new_dataset = getdataset(self.new_data_path, self.new_csv_path, mode='test',
                                              dataset_name=self.new_dataset_name)
                print(f"Loaded new dataset for inference: {self.new_dataset_name} with {len(self.new_dataset)} samples")
            except Exception as e:
                print(f"Warning: failed to initialize new dataset loader: {e}")
        
    def load_model(self):
        """Load the LLaVA model with LoRA weights"""
        print("Loading model and tokenizer...")
        disable_torch_init()
        
        # Temporarily patch CLIPECGTower to avoid immediate loading
        import llava.model.multimodal_encoder.clip_encoder as clip_encoder_module
        original_init = clip_encoder_module.CLIPECGTower.__init__
        
        def patched_init(self, ecg_tower, args, delay_load=False):
            super(clip_encoder_module.CLIPECGTower, self).__init__()
            self.model_config = None
            self.ecg_processor = None
            self.ecg_tower = None
            self.is_loaded = False
            self.ecg_tower_name = ecg_tower
            self.model_name = getattr(args, 'open_clip_config', None)
            if self.model_name is None:
                raise ValueError('No open_clip config for building ECG encoder!')
            
            # Only load if delay_load is False
            if not delay_load:
                self.load_model(self.model_name)
            else:
                # Set default values for delayed loading
                self.hidden_size = 768
                self.seq_length = 5000
                self.patch_size = 50
                self.num_patches_per_side = self.seq_length // self.patch_size
                self.num_patches = self.seq_length // self.patch_size
        
        # Apply the patch
        clip_encoder_module.CLIPECGTower.__init__ = patched_init
        
        try:
            # Follow the exact logic from builder.py for LoRA loading
            from llava.model.language_model.llava_llama import LlavaConfig, LlavaLlamaForCausalLM
            from transformers import AutoTokenizer
            from peft import PeftModel
            
            # Load config and add open_clip_config
            lora_cfg_pretrained = LlavaConfig.from_pretrained(self.model_path)
            lora_cfg_pretrained.open_clip_config = 'coca_ViT-B-32'
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.base_model_path, use_fast=False)
            
            # Prepare kwargs for model loading
            kwargs = {
                'torch_dtype': torch.float16 if self.device == "cuda" else torch.float32,
            }
            # Force all model components to the same device
            if self.device != "cuda":
                kwargs['device_map'] = {"": self.device}
            else:
                kwargs['device_map'] = {"": self.device}
            
            print('Loading LLaVA from base model...')
            # Load base model with config but without low_cpu_mem_usage to avoid meta tensor issues
            self.model = LlavaLlamaForCausalLM.from_pretrained(
                self.base_model_path, 
                config=lora_cfg_pretrained,
                **kwargs
            )
        finally:
            # Restore original init
            clip_encoder_module.CLIPECGTower.__init__ = original_init
        
        # Load additional LLaVA weights (non-LoRA trainables)
        print('Loading additional LLaVA weights...')
        non_lora_trainables_path = os.path.join(self.model_path, 'non_lora_trainables.bin')
        if os.path.exists(non_lora_trainables_path):
            non_lora_trainables = torch.load(non_lora_trainables_path, map_location='cpu')
            non_lora_trainables = {(k[11:] if k.startswith('base_model.') else k): v for k, v in non_lora_trainables.items()}
            if any(k.startswith('model.model.') for k in non_lora_trainables):
                non_lora_trainables = {(k[6:] if k.startswith('model.') else k): v for k, v in non_lora_trainables.items()}
            self.model.load_state_dict(non_lora_trainables, strict=False)
        
        # Load LoRA weights
        print('Loading LoRA weights...')
        self.model = PeftModel.from_pretrained(self.model, self.model_path)
        print('Merging LoRA weights...')
        self.model = self.model.merge_and_unload()
        print('Convert to appropriate dtype...')
        if self.device == "cuda":
            self.model.to(torch.float16)
        else:
            self.model.to(torch.float32)
        
        # Set context length
        self.context_len = 2048
        
        # Load ECG tower
        print("Loading ECG tower...")
        self.ecg_tower = self.model.get_ecg_tower()
        if not self.ecg_tower.is_loaded:
            # Force ECG tower to same device as main model
            if self.device == "cuda":
                self.ecg_tower.load_model('coca_ViT-B-32', device_map={"": self.device})
            else:
                self.ecg_tower.load_model('coca_ViT-B-32', device_map={"": self.device})
        
        # Ensure ECG tower is on the same device as the main model
        if self.device == "cuda":
            self.ecg_tower.to(device=self.device, dtype=torch.float16)
        else:
            self.ecg_tower.to(device=self.device, dtype=torch.float32)
        
        print("Model loaded successfully!")
        
    def preprocess_ecg(self, ecg_path, seq_length=5000):
        """Preprocess ECG data for model input"""
        try:
            # Load ECG data using wfdb - ecg_path should not include .hea extension
            ecg_data = wfdb.rdsamp(ecg_path)[0]
            
            # Handle NaN and Inf values
            ecg_data[np.isnan(ecg_data)] = 0
            ecg_data[np.isinf(ecg_data)] = 0
            
            # Transpose to (channels, length) format
            ecg_tensor = torch.Tensor(np.transpose(ecg_data, (1, 0)).astype(np.float32))
            
            # Pad or truncate to fixed length (consistent with training)
            c, length = ecg_tensor.shape
            if length < seq_length:
                new_ecg = torch.zeros((c, seq_length), dtype=ecg_tensor.dtype)
                new_ecg[:, 0:length] = ecg_tensor
                ecg_tensor = new_ecg
            elif length > seq_length:
                ecg_tensor = ecg_tensor[:, 0:seq_length]
            print(ecg_tensor.shape)
            return ecg_tensor
            
        except Exception as e:
            print(f"Error processing ECG file {ecg_path}: {e}")
            return None

    def preprocess_ecg_from_dataset(self, index):
        """Preprocess ECG from optional new dataset by index. Returns a tensor (C, L)."""
        if self.new_dataset is None:
            raise ValueError("New dataset not initialized. Provide --new_dataset_name, --new_data_path, and --new_csv_path.")
        # The dataset already returns normalized tensor of shape (C, 5000)
        dataset_item = self.new_dataset[index]
        if isinstance(dataset_item, tuple):
            ecg_tensor, _ = dataset_item
        else:
            ecg_tensor = dataset_item
        return ecg_tensor
    
    def generate_response(self, ecg_path=None, question="", max_new_tokens=512, temperature=0.7, top_p=0.9, *, ecg_tensor=None):
        """Generate response for ECG analysis"""
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        # Prepare ECG tensor
        if ecg_tensor is None:
            if ecg_path is None:
                raise ValueError("Either ecg_path or ecg_tensor must be provided.")
            ecg_tensor = self.preprocess_ecg(ecg_path)
        if ecg_tensor is None:
            return None

        # Prepare conversation following training format
        # Use vicuna_v1 template which is the default for version v1 in training
        conv = conv_templates['vicuna_v1'].copy()
        # Format the question with ECG token at the beginning, consistent with training data
        formatted_question = f"{DEFAULT_ECG_TOKEN}\n{question}"
        conv.append_message(conv.roles[0], formatted_question)
        conv.append_message(conv.roles[1], None)
        prompt = conv.get_prompt()

        # Tokenize using the same method as training (tokenizer_ecg_token)
        input_ids = tokenizer_ecg_token(prompt, self.tokenizer, return_tensors='pt')
        if input_ids.dim() == 1:
            input_ids = input_ids.unsqueeze(0)
        input_ids = input_ids.to(self.device)

        # Generate response
        with torch.inference_mode():
            # Ensure ECG tensor has the same dtype and device as the model
            model_dtype = next(self.model.parameters()).dtype
            model_device = next(self.model.parameters()).device
            ecg_tensor = ecg_tensor.to(dtype=model_dtype, device=model_device)

            output_ids = self.model.generate(
                input_ids,
                ecgs=ecg_tensor.unsqueeze(0),
                do_sample=True,
                temperature=temperature,
                top_p=top_p,
                max_new_tokens=max_new_tokens,
                use_cache=True
            )

        # Decode response
        input_token_len = input_ids.shape[1] if input_ids.dim() > 1 else input_ids.shape[0]
        if output_ids.dim() > 1:
            output_ids = output_ids[0, input_token_len:]
        else:
            output_ids = output_ids[input_token_len:]
        response = self.tokenizer.decode(output_ids, skip_special_tokens=True)

        return response
    
    def interactive_mode(self):
        """Interactive mode for ECG analysis"""
        print("\n" + "="*60)
        print("LLaVA ECG Analysis - Interactive Mode")
        print("="*60)
        print("Enter ECG file path and questions for analysis.")
        print("Type 'quit' to exit.")
        print("="*60)
        
        while True:
            try:
                # Get ECG file path
                ecg_path = input("\nECG file path (relative to ECG folder): ").strip()
                if ecg_path.lower() == 'quit':
                    break
                    
                if not ecg_path:
                    print("Please provide a valid ECG file path.")
                    continue
                    
                # Construct full path - ECG files need .hea extension for wfdb
                full_ecg_path = os.path.join(self.ecg_folder, ecg_path)
                if not os.path.exists(full_ecg_path + '.hea'):
                    print(f"ECG file not found: {full_ecg_path}.hea")
                    continue
                
                # Get question
                question = input("Question (press Enter for default): ").strip()
                if not question:
                    question = "Is there any abnormality in the electrocardiogram? Can you explain it in detail?"
                
                # Generate response
                print("\nAnalyzing ECG...")
                response = self.generate_response(full_ecg_path, question)
                
                if response:
                    print("\n" + "="*50)
                    print("ECG Analysis Result:")
                    print("="*50)
                    print(response)
                    print("="*50)
                else:
                    print("Failed to generate response.")
                    
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def batch_inference(self, input_file, output_file, question=None):
        """Batch inference from JSON file (default) or from new dataset if configured and input_file is None.
        If output_file ends with .csv, will write two columns: id,response.
        """
        try:
            results = []
            if input_file:
                with open(input_file, 'r') as f:
                    data = json.load(f)
                for i, item in enumerate(data):
                    print(f"Processing {i+1}/{len(data)}: {item.get('id', 'unknown')}")
                    ecg_path = os.path.join(self.ecg_folder, item['ecg'])
                    if not os.path.exists(ecg_path + '.hea'):
                        print(f"ECG file not found: {ecg_path}.hea, skipping...")
                        continue
                    # Extract question from conversation, removing ECG token if present
                    question_value = item['conversations'][0]['value']
                    if DEFAULT_ECG_TOKEN in question_value:
                        question = question_value.replace(DEFAULT_ECG_TOKEN, '').strip()
                        # Remove any leading newline after ECG token removal
                        if question.startswith('\n'):
                            question = question[1:]
                    else:
                        question = question_value
                    response = self.generate_response(ecg_path, question)
                    result = {
                        'id': item.get('id', f'item_{i}'),
                        'ecg': item['ecg'],
                        'question': question,
                        'response': response
                    }
                    results.append(result)
            else:
                if self.new_dataset is None:
                    raise ValueError("No input file provided and new dataset not configured.")
                print(f"Running batch inference over new dataset: {self.new_dataset_name}")
                default_question = question or "Is there any abnormality in the electrocardiogram? Can you explain it in detail?"
                for i in range(len(self.new_dataset)):
                    print(f"Processing {i+1}/{len(self.new_dataset)}")
                    dataset_item = self.new_dataset[i]
                    if isinstance(dataset_item, tuple):
                        ecg_tensor, _ = dataset_item
                    else:
                        ecg_tensor = dataset_item
                    response = self.generate_response(None, default_question, ecg_tensor=ecg_tensor)
                    # Determine an ID string from dataset
                    print(response)
                    try:
                        id_str = str(self.new_dataset.ecg_path[i])
                    except Exception:
                        id_str = f'{self.new_dataset_name}:{i}'
                    result = {
                        'id': f'dataset_item_{i}',
                        'ecg': id_str,
                        'question': default_question,
                        'response': response
                    }
                    results.append(result)
            # Write outputs
            if output_file.lower().endswith('.csv'):
                try:
                    import pandas as pd
                except Exception as e:
                    raise RuntimeError(f"Pandas is required to write CSV output: {e}")
                # Build two-column CSV: id,response
                rows = []
                for item in results:
                    rows.append({'id': item.get('ecg') or item.get('id'), 'response': item.get('response')})
                df = pd.DataFrame(rows)
                df.to_csv(output_file, index=False)
            else:
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"Batch inference completed. Results saved to {output_file}")
            
        except Exception as e:
            print(f"Error in batch inference: {e}")

def main():
    parser = argparse.ArgumentParser(description='LLaVA ECG Inference with LoRA (CSV→CSV batch for new datasets)')
    parser.add_argument('--model_path', 
                       default='/data/yuanxiaoyan/ECGpro/ECG-Chat/checkpoints/llava-vicuna-13b-v1.5-finetune-ecginstruct_lora',
                       help='Path to LoRA model')
    parser.add_argument('--base_model_path',
                       default='/data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5',
                       help='Path to base model')
    parser.add_argument('--ecg_tower_path',
                       default='/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt',
                       help='Path to ECG tower')
    parser.add_argument('--ecg_folder',
                       default='/data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0',
                       help='Path to ECG data folder')
    parser.add_argument('--question', 
                       default='Is there any abnormality in the electrocardiogram? Can you explain it in detail?',
                       help='Question for ECG analysis')
    parser.add_argument('--batch_output', help='Output file for batch results (CSV preferred: id,response)')
    parser.add_argument('--device', default='cuda', help='Device to use (cuda/cpu)')
    # Optional: New dataset reader args
    parser.add_argument('--new_dataset_name', choices=['ptbxl', 'icbeb', 'chapman'], help='Use new dataset reader by name')
    parser.add_argument('--new_data_path', help='Base path to raw data for new dataset reader (if omitted, auto-infer by dataset name)')
    parser.add_argument('--new_csv_path', help='CSV file path for new dataset reader (you choose the CSV)')
    parser.add_argument('--dataset_index', type=int, help='Index of sample in new dataset for single inference')
    
    args = parser.parse_args()
    
    # Initialize inference class
    inference = LLaVAECGInference(
        model_path=args.model_path,
        base_model_path=args.base_model_path,
        ecg_tower_path=args.ecg_tower_path,
        ecg_folder=args.ecg_folder,
        device=args.device,
        new_dataset_name=args.new_dataset_name,
        new_data_path=args.new_data_path,
        new_csv_path=args.new_csv_path
    )
    
    # Load model
    inference.load_model()
    
    # Only support: run over provided new CSV and output CSV/JSON
    if args.new_dataset_name and args.new_csv_path and args.batch_output:
        inference.batch_inference(None, args.batch_output, question=args.question)
    else:
        print("Required: --new_dataset_name, --new_csv_path, --batch_output. Optionally: --new_data_path, --question")
        parser.print_help()

if __name__ == "__main__":
    main()
