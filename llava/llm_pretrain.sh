#!/bin/bash

script_dir=$(dirname "$(realpath "$0")")
open_clip_dir="$script_dir/../open_clip"
export PYTHONPATH="$script_dir:$open_clip_dir:$PYTHONPATH"

# 设置离线模式，避免网络连接问题
export HF_ENDPOINT=https://hf-mirror.com

# MODEL_VERSION=vicuna-13b
# MODEL_VERSION=llama-2-7b-chat
MODEL_VERSION=vicuna-13b-v1.5
# /data/yuanxiaoyan/ECGpro/Pertrain_M/llava-v1.6-vicuna-7b
WANDB_MODE=offline deepspeed --include "localhost:1,4,5,7" /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llava/train/train.py \
    --deepspeed "$script_dir/llava/scripts/zero2.json" \
    --model_name_or_path /data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5 \
    --version plain \
    --data_path ./llava/playground/data/pretrain_mimic.json \
    --ecg_folder /data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0 \
    --ecg_tower /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt \
    --open_clip_config coca_ViT-B-32 \
    --tune_mm_mlp_adapter True \
    --mm_use_ecg_start_end False \
    --mm_use_ecg_patch_token False \
    --bf16 True \
    --output_dir ./checkpoints/llava-$MODEL_VERSION-pretrain \
    --num_train_epochs 1 \
    --per_device_train_batch_size 1 \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 16 \
    --evaluation_strategy "no" \
    --save_strategy "steps" \
    --save_steps 1000 \
    --save_total_limit 1 \
    --learning_rate 2e-3 \
    --weight_decay 0. \
    --warmup_ratio 0.03 \
    --lr_scheduler_type "cosine" \
    --logging_steps 1 \
    --tf32 False \
    --model_max_length 2048 \
    --gradient_checkpointing True \
    --dataloader_num_workers 4 \
    --lazy_preprocess True